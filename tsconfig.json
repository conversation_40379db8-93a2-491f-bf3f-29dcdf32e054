{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@search-keywords-scraper/types": ["./packages/types/src"], "@search-keywords-scraper/database": ["./packages/database/src"], "@search-keywords-scraper/config": ["./packages/config/src"], "@search-keywords-scraper/utils": ["./packages/utils/src"], "@search-keywords-scraper/queue": ["./packages/queue/src"]}}, "include": [], "exclude": ["node_modules"], "references": [{"path": "./apps/web"}, {"path": "./apps/api"}, {"path": "./apps/worker"}, {"path": "./packages/types"}, {"path": "./packages/database"}, {"path": "./packages/config"}, {"path": "./packages/utils"}, {"path": "./packages/scraper"}, {"path": "./packages/queue"}]}