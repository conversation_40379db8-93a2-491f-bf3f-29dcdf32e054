{"globalDependencies": ["**/.env.*local"], "tasks": {"dev": {"cache": false, "persistent": true}, "build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "lint": {"dependsOn": ["^lint"]}, "type-check": {"dependsOn": ["^type-check"]}, "test": {"dependsOn": ["^test"], "outputs": ["coverage/**"]}, "format": {"dependsOn": ["^format"]}, "db:migrate": {"cache": false}, "db:seed": {"cache": false}}}