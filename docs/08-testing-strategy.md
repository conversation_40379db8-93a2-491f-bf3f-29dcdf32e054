# Testing Strategy

## Executive Summary

This document outlines a comprehensive testing strategy for the Search Keywords Scraper application, prioritized by feature criticality and business value. The strategy focuses on maximizing testing ROI by targeting the most impactful components and user flows that directly contribute to the application's core value proposition.

Our approach emphasizes risk-based testing prioritization, ensuring that the most critical functionality - web scraping with anti-detection measures, data integrity, and user authentication - receives thorough coverage while maintaining efficient resource allocation.

## Testing Philosophy

### Core Principles

1. **Risk-Based Prioritization**: Focus testing efforts on components with the highest business impact and technical complexity
2. **Quality Over Coverage**: Prioritize meaningful tests that catch real bugs over achieving arbitrary coverage metrics
3. **Test Pyramid Approach**: Emphasize fast, reliable unit tests with strategic integration and end-to-end testing
4. **Business Value Focus**: Ensure testing directly supports the application's core differentiators and user flows

### Strategic Testing Approach

Our testing strategy is built around the application's unique challenges and value propositions:

- **Anti-Scraping Measures**: Robust testing of proxy rotation, retry logic, and stealth configurations
- **Data Integrity**: Comprehensive validation of CSV processing, keyword management, and result storage
- **Distributed Processing**: Thorough testing of queue-based job processing and worker reliability
- **User Experience**: Validation of real-time updates and authentication flows

## Testing Pyramid Structure

### Test Distribution by Value

```
        ╔═══════════════════════╗
        ║    End-to-End Tests   ║ 10%
        ║   (Critical Paths)    ║
        ╠═══════════════════════╣
        ║   Integration Tests   ║ 30%
        ║  (System Boundaries)  ║
        ╠═══════════════════════╣
        ║     Unit Tests        ║ 60%
        ║  (Business Logic)     ║
        ╚═══════════════════════╝
```

### Rationale for Distribution

**60% Unit Tests** - Foundation layer focusing on:
- Business logic validation
- Edge case handling
- Algorithm correctness
- Fast feedback loops

**30% Integration Tests** - System interaction layer covering:
- Database operations
- Queue processing
- API endpoint behavior
- Authentication flows

**10% End-to-End Tests** - User journey validation ensuring:
- Complete workflow functionality
- Real-time update mechanisms
- Cross-system integration

## Priority-Based Test Planning

### Priority 1: Core Business Logic (Critical)

These tests provide the highest ROI by validating the application's fundamental value propositions.

#### 1.1 CSV Processing & Validation
**Business Impact**: Prevents invalid data from entering the system
**Components**: `apps/api/src/keywords/services/csv-parser.service.ts`

```typescript
// High-value test scenarios:
- Valid CSV with 1-100 keywords
- Edge cases: empty files, malformed CSV, special characters
- Boundary testing: exactly 100 keywords, 101 keywords (should fail)
- Duplicate keyword handling
- Invalid file formats
```

#### 1.2 Scraper Core Logic
**Business Impact**: Core differentiator - successful data extraction
**Components**: `apps/worker/src/core/scraper/`

```typescript
// Critical test scenarios:
- HTML parsing accuracy (ad counting, link counting)
- Retry logic for failed requests
- Error classification (retryable vs permanent)
- Timeout handling
- Result data structure validation
```

#### 1.3 Authentication & User Isolation
**Business Impact**: Data security and compliance
**Components**: `apps/api/src/auth/`

```typescript
// Essential test scenarios:
- JWT token validation
- User data isolation (RLS enforcement)
- Protected endpoint access
- Invalid token handling
- Session management
```

### Priority 2: System Integration (High Value)

These tests validate critical system boundaries and data flows.

#### 2.1 Queue Processing
**Business Impact**: Ensures reliable job execution and scaling
**Components**: `apps/worker/src/processors/`

```typescript
// Key integration scenarios:
- Job creation and consumption
- Failure handling and retry mechanisms
- Concurrent job processing
- Status updates propagation
- Dead letter queue handling
```

#### 2.2 API Endpoint Integration
**Business Impact**: Validates complete request-response cycles
**Components**: `apps/api/src/**/*.controller.ts`

```typescript
// Important endpoint tests:
- POST /keywords/upload with real file processing
- GET /keywords with pagination and filtering
- Real database interactions
- Error response formatting
- Request validation
```

#### 2.3 Real-time Progress Updates
**Business Impact**: User experience and system responsiveness
**Components**: Frontend polling, API status endpoints

```typescript
// Progress tracking scenarios:
- Batch status updates during processing
- Individual keyword status changes
- Progress percentage calculations
- Error state propagation
```

### Priority 3: User Experience Validation (Moderate Value)

These tests ensure the complete user journey functions correctly.

#### 3.1 End-to-End User Flows
**Business Impact**: Validates complete functionality
**Tools**: Playwright E2E testing

```typescript
// Complete user journey:
1. User authentication
2. CSV file upload
3. Batch creation and job queuing
4. Progress monitoring
5. Results viewing
6. Search and filtering
```

#### 3.2 Frontend Component Testing
**Business Impact**: UI reliability and user interaction
**Components**: `apps/web/src/components/`

```typescript
// Key component tests:
- Upload form validation
- Data table functionality
- Progress indicators
- Error message display
- Navigation components
```

## Implementation Strategy by Component

### Backend API Testing (apps/api)

#### Unit Tests
- **Services**: Business logic, validation rules, data transformations
- **DTOs**: Request/response validation, type safety
- **Utils**: Helper functions, parsing logic, calculations

#### Integration Tests
- **Controllers**: Full request-response cycle with real database
- **Authentication**: JWT middleware, guard behavior
- **Database**: Repository patterns, query correctness

```typescript
// Example: Keyword Service Test Structure
describe('KeywordsService', () => {
  describe('uploadKeywords', () => {
    it('should process valid CSV and create batch')
    it('should reject files with > 100 keywords')
    it('should handle duplicate keywords correctly')
    it('should validate file format requirements')
  })
})
```

### Worker Testing (apps/worker)

#### Unit Tests
- **Scraper Logic**: HTML parsing, data extraction algorithms
- **Retry Mechanisms**: Exponential backoff, error classification
- **Data Processing**: Result formatting, storage preparation

#### Integration Tests
- **Job Processing**: Queue consumption, database updates
- **Error Handling**: Failed job recovery, status updates
- **Proxy Management**: Health checking, rotation logic

```typescript
// Example: Keyword Processor Test Structure
describe('KeywordProcessor', () => {
  describe('processKeyword', () => {
    it('should successfully scrape and store results')
    it('should retry on network failures')
    it('should mark as failed after max retries')
    it('should update batch progress correctly')
  })
})
```

### Frontend Testing (apps/web)

#### Component Tests
- **Form Validation**: Upload forms, search inputs
- **Data Display**: Tables, progress indicators, result viewers
- **Navigation**: Routing, authentication guards

#### Integration Tests
- **API Integration**: Data fetching, mutation handling
- **State Management**: TanStack Query cache behavior
- **Authentication**: Login flows, protected routes

```typescript
// Example: Upload Form Test Structure
describe('UploadForm', () => {
  it('should validate file type and size')
  it('should show progress during upload')
  it('should handle upload errors gracefully')
  it('should redirect to results after successful upload')
})
```

## Testing Infrastructure

### Test Configuration

#### Root Level (Jest Configuration)
```json
// jest.config.js
{
  "projects": [
    "<rootDir>/apps/*/jest.config.js",
    "<rootDir>/packages/*/jest.config.js"
  ],
  "collectCoverageFrom": [
    "src/**/*.{ts,tsx}",
    "!src/**/*.d.ts",
    "!src/types/**/*"
  ]
}
```

#### Shared Test Utilities
```typescript
// packages/test-utils/src/factories/
- KeywordFactory: Generate test keyword data
- BatchFactory: Create test batches
- UserFactory: Mock user objects
- MockQueueProvider: Queue testing utilities
```

### Database Testing Strategy

#### Test Database Setup
- Separate test database instance
- Automated migration and seeding
- Transaction-based test isolation
- Cleanup between test suites

#### Data Factories
```typescript
// Example: Keyword Factory
export const KeywordFactory = {
  create: (overrides = {}) => ({
    id: generateId(),
    keyword: faker.lorem.word(),
    userId: faker.string.uuid(),
    status: 'pending',
    ...overrides
  })
}
```

### Mock Strategies

#### External Service Mocking
- **Scraping Targets**: Mock Google/Bing responses
- **Proxy Services**: Simulate proxy rotation
- **Authentication**: Mock Supabase Auth responses

#### Queue Mocking
```typescript
// Mock Bull Queue for unit tests
const mockQueue = {
  add: jest.fn(),
  process: jest.fn(),
  on: jest.fn()
}
```

## Sample Test Implementations

### Unit Test Example: CSV Parser

```typescript
// apps/api/src/keywords/services/__tests__/csv-parser.service.spec.ts
describe('CsvParserService', () => {
  let service: CsvParserService;

  beforeEach(() => {
    service = new CsvParserService();
  });

  describe('parseKeywords', () => {
    it('should parse valid CSV with keywords', async () => {
      const csvContent = 'keyword1\nkeyword2\nkeyword3';
      const file = createMockFile(csvContent);
      
      const result = await service.parseKeywords(file);
      
      expect(result).toEqual(['keyword1', 'keyword2', 'keyword3']);
    });

    it('should reject files with more than 100 keywords', async () => {
      const keywords = Array.from({ length: 101 }, (_, i) => `keyword${i}`);
      const csvContent = keywords.join('\n');
      const file = createMockFile(csvContent);
      
      await expect(service.parseKeywords(file)).rejects.toThrow(
        'Maximum 100 keywords allowed per batch'
      );
    });

    it('should handle empty lines and whitespace', async () => {
      const csvContent = 'keyword1\n\n  keyword2  \n\nkeyword3';
      const file = createMockFile(csvContent);
      
      const result = await service.parseKeywords(file);
      
      expect(result).toEqual(['keyword1', 'keyword2', 'keyword3']);
    });

    it('should remove duplicate keywords', async () => {
      const csvContent = 'keyword1\nkeyword2\nkeyword1\nkeyword3';
      const file = createMockFile(csvContent);
      
      const result = await service.parseKeywords(file);
      
      expect(result).toEqual(['keyword1', 'keyword2', 'keyword3']);
    });
  });
});
```

### Integration Test Example: Upload Endpoint

```typescript
// apps/api/src/keywords/__tests__/keywords.controller.integration.spec.ts
describe('KeywordsController (Integration)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    app = await createTestApp();
    authToken = await createTestUserAndGetToken(app);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /keywords/upload', () => {
    it('should upload CSV and create batch with jobs', async () => {
      const csvContent = 'keyword1\nkeyword2\nkeyword3';
      
      const response = await request(app.getHttpServer())
        .post('/keywords/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', Buffer.from(csvContent), 'test.csv')
        .expect(201);

      expect(response.body).toMatchObject({
        filename: 'test.csv',
        totalKeywords: 3,
        status: 'pending'
      });

      // Verify database records were created
      const batch = await getBatchById(response.body.id);
      expect(batch.totalKeywords).toBe(3);

      const keywords = await getKeywordsByBatchId(response.body.id);
      expect(keywords).toHaveLength(3);
      expect(keywords.map(k => k.keyword)).toEqual(['keyword1', 'keyword2', 'keyword3']);
    });

    it('should reject unauthorized requests', async () => {
      const csvContent = 'keyword1\nkeyword2';
      
      await request(app.getHttpServer())
        .post('/keywords/upload')
        .attach('file', Buffer.from(csvContent), 'test.csv')
        .expect(401);
    });
  });
});
```

### E2E Test Example: Complete User Journey

```typescript
// apps/web/__tests__/upload-flow.e2e.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Keyword Upload Flow', () => {
  test('should complete full upload and processing journey', async ({ page }) => {
    // 1. Authentication
    await page.goto('/signin');
    await page.fill('[data-testid=email]', '<EMAIL>');
    await page.fill('[data-testid=password]', 'password123');
    await page.click('[data-testid=signin-button]');
    
    await expect(page).toHaveURL('/dashboard');

    // 2. Navigate to upload
    await page.click('[data-testid=upload-nav]');
    await expect(page).toHaveURL('/upload');

    // 3. Upload CSV file
    const csvContent = 'keyword1\nkeyword2\nkeyword3';
    const fileBuffer = Buffer.from(csvContent);
    
    await page.setInputFiles('[data-testid=file-input]', {
      name: 'keywords.csv',
      mimeType: 'text/csv',
      buffer: fileBuffer
    });

    await page.click('[data-testid=upload-button]');

    // 4. Verify batch creation
    await expect(page.locator('[data-testid=upload-success]')).toBeVisible();
    await expect(page.locator('[data-testid=batch-info]')).toContainText('3 keywords');

    // 5. Monitor progress
    await page.click('[data-testid=view-progress]');
    await expect(page).toHaveURL(/\/keywords\/[^/]+$/);

    // 6. Wait for processing completion (mock for test speed)
    await page.locator('[data-testid=processing-complete]').waitFor({ timeout: 10000 });

    // 7. Verify results
    await expect(page.locator('[data-testid=keyword-row]')).toHaveCount(3);
    await expect(page.locator('[data-testid=success-count]')).toContainText('3');
  });
});
```

## Continuous Integration Strategy

### GitHub Actions Workflow

```yaml
name: Test Suite

on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - run: pnpm install
      - run: pnpm test:unit
      - run: pnpm test:coverage

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - run: pnpm install
      - run: pnpm db:migrate
      - run: pnpm test:integration

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - run: pnpm install
      - run: pnpm build
      - run: pnpm test:e2e
```

## Quality Metrics and Success Criteria

### Coverage Targets

- **Unit Tests**: 80%+ coverage for business logic
- **Integration Tests**: 100% coverage for critical API endpoints
- **E2E Tests**: 100% coverage for primary user flows

### Performance Benchmarks

- **Unit Tests**: < 10ms average execution time
- **Integration Tests**: < 100ms average execution time
- **E2E Tests**: < 30 seconds for complete flows

### Quality Gates

1. All tests must pass before merge
2. No decrease in coverage percentage
3. No failing end-to-end tests in critical paths
4. Performance regression threshold: +20% execution time

## Testing Best Practices

### Test Structure and Organization

1. **Arrange-Act-Assert Pattern**: Clear test structure
2. **Descriptive Test Names**: What, when, should behavior
3. **Single Responsibility**: One assertion per test when possible
4. **Test Data Isolation**: Independent test execution

### Mocking Guidelines

1. **Mock External Dependencies**: Third-party services, network calls
2. **Real Database for Integration**: Use test database, not mocks
3. **Spy on Internal Services**: Verify interaction patterns
4. **Avoid Over-Mocking**: Test real behavior when possible

### Maintenance Strategy

1. **Regular Test Review**: Remove obsolete tests
2. **Refactor with Code**: Update tests when logic changes
3. **Test Documentation**: Comment complex test scenarios
4. **Performance Monitoring**: Track test execution times

This testing strategy ensures comprehensive coverage of the Search Keywords Scraper's critical functionality while maintaining development efficiency and code quality. The priority-based approach focuses testing efforts where they provide the most value, supporting both immediate development needs and long-term maintainability.