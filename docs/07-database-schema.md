# Database Schema & ERD

## Entity Relationship Diagram

```mermaid
erDiagram
    users {
        uuid id PK
        varchar email UK
        timestamp email_verified
        varchar name
        varchar image
        timestamp created_at
        timestamp updated_at
    }
    
    keyword_batches {
        uuid id PK
        uuid user_id FK
        varchar filename
        int original_size
        int total_keywords
        int processed_keywords
        int successful_keywords
        int failed_keywords
        status_enum status
        timestamp processing_started_at
        timestamp processing_completed_at
        timestamp created_at
        timestamp updated_at
    }
    
    keywords {
        uuid id PK
        uuid batch_id FK
        uuid user_id FK
        varchar keyword
        status_enum status
        int retry_count
        timestamp last_retry_at
        timestamp processing_started_at
        timestamp processing_completed_at
        text error_message
        timestamp created_at
        timestamp updated_at
    }
    
    search_results {
        uuid id PK
        uuid keyword_id FK
        search_engine_enum search_engine
        int total_ads
        int total_links
        text html_cache
        varchar page_title
        varchar search_url
        int processing_time_ms
        varchar proxy_used
        varchar user_agent
        jsonb metadata
        timestamp scraped_at
    }

    users ||--o{ keyword_batches : "creates"
    users ||--o{ keywords : "owns"
    keyword_batches ||--o{ keywords : "contains"
    keywords ||--|| search_results : "produces"
```

## Table Definitions

### Users Table
Primary table for user authentication and management.

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified TIMESTAMP,
    name VARCHAR(255),
    image VARCHAR(500),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_created_at ON users(created_at);
```

**Purpose**: Stores user account information compatible with Supabase Auth.

**Key Fields**:
- `id`: Primary UUID identifier
- `email`: Unique email address for authentication
- `email_verified`: Timestamp when email was verified
- `name`: User display name
- `image`: Profile image URL

### Keyword Batches Table
Represents a batch of keywords uploaded by a user.

```sql
CREATE TYPE status_enum AS ENUM ('pending', 'processing', 'completed', 'failed', 'retrying');

CREATE TABLE keyword_batches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_size INTEGER NOT NULL,
    total_keywords INTEGER NOT NULL,
    processed_keywords INTEGER DEFAULT 0,
    successful_keywords INTEGER DEFAULT 0,
    failed_keywords INTEGER DEFAULT 0,
    status status_enum DEFAULT 'pending',
    processing_started_at TIMESTAMP,
    processing_completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_keyword_batches_user_id ON keyword_batches(user_id);
CREATE INDEX idx_keyword_batches_status ON keyword_batches(status);
CREATE INDEX idx_keyword_batches_created_at ON keyword_batches(created_at DESC);
CREATE INDEX idx_keyword_batches_user_status ON keyword_batches(user_id, status);
```

**Purpose**: Tracks batches of keywords and their processing progress.

**Key Fields**:
- `id`: Primary UUID identifier
- `user_id`: Foreign key to users table
- `filename`: Original filename or batch name
- `original_size`: Size of original data in bytes
- `total_keywords`: Total number of keywords in batch
- `processed_keywords`: Number of keywords processed so far
- `successful_keywords`: Number of successfully processed keywords
- `failed_keywords`: Number of failed keyword processing attempts
- `status`: Current processing status of the batch

### Keywords Table
Individual keywords within a batch.

```sql
CREATE TABLE keywords (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    batch_id UUID REFERENCES keyword_batches(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    keyword VARCHAR(500) NOT NULL,
    status status_enum DEFAULT 'pending',
    retry_count INTEGER DEFAULT 0,
    last_retry_at TIMESTAMP,
    processing_started_at TIMESTAMP,
    processing_completed_at TIMESTAMP,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_keywords_batch_id ON keywords(batch_id);
CREATE INDEX idx_keywords_user_id ON keywords(user_id);
CREATE INDEX idx_keywords_status ON keywords(status);
CREATE INDEX idx_keywords_keyword ON keywords(keyword);
CREATE INDEX idx_keywords_user_status ON keywords(user_id, status);
CREATE INDEX idx_keywords_batch_status ON keywords(batch_id, status);
CREATE INDEX idx_keywords_created_at ON keywords(created_at DESC);

-- Full-text search index for keywords
CREATE INDEX idx_keywords_keyword_gin ON keywords USING gin(to_tsvector('english', keyword));
```

**Purpose**: Stores individual keywords and their processing status.

**Key Fields**:
- `id`: Primary UUID identifier
- `batch_id`: Foreign key to keyword_batches table
- `user_id`: Foreign key to users table (denormalized for easier queries)
- `keyword`: The actual keyword text to be scraped
- `status`: Current processing status
- `retry_count`: Number of retry attempts
- `last_retry_at`: Timestamp of last retry attempt
- `error_message`: Error details if processing failed

### Search Results Table
Results from scraping operations.

```sql
CREATE TYPE search_engine_enum AS ENUM ('google', 'bing');

CREATE TABLE search_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    keyword_id UUID REFERENCES keywords(id) ON DELETE CASCADE NOT NULL,
    search_engine search_engine_enum DEFAULT 'google',
    total_ads INTEGER NOT NULL,
    total_links INTEGER NOT NULL,
    html_cache TEXT NOT NULL,
    page_title VARCHAR(500),
    search_url VARCHAR(1000),
    processing_time_ms INTEGER,
    proxy_used VARCHAR(100),
    user_agent VARCHAR(500),
    metadata JSONB,
    scraped_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_search_results_keyword_id ON search_results(keyword_id);
CREATE INDEX idx_search_results_search_engine ON search_results(search_engine);
CREATE INDEX idx_search_results_scraped_at ON search_results(scraped_at DESC);
CREATE INDEX idx_search_results_total_ads ON search_results(total_ads DESC);
CREATE INDEX idx_search_results_total_links ON search_results(total_links DESC);

-- GIN index for JSONB metadata queries
CREATE INDEX idx_search_results_metadata ON search_results USING gin(metadata);
```

**Purpose**: Stores the scraped data and metrics for each keyword.

**Key Fields**:
- `id`: Primary UUID identifier
- `keyword_id`: Foreign key to keywords table
- `search_engine`: Which search engine was used
- `total_ads`: Number of advertisements found on the page
- `total_links`: Total number of links found on the page
- `html_cache`: Full HTML content of the scraped page
- `page_title`: Title of the scraped page
- `search_url`: Actual URL that was scraped
- `processing_time_ms`: Time taken to process in milliseconds
- `proxy_used`: Identifier of the proxy server used
- `user_agent`: User agent string used for scraping
- `metadata`: Additional JSON metadata about the scraping process

## Drizzle ORM Schema Implementation

### Schema Definition File Structure

```typescript
// packages/database/src/schema/enums.ts
import { pgEnum } from 'drizzle-orm/pg-core';

export const statusEnum = pgEnum('status', [
  'pending',
  'processing', 
  'completed',
  'failed',
  'retrying'
]);

export const searchEngineEnum = pgEnum('search_engine', ['google', 'bing']);
```

```typescript
// packages/database/src/schema/users.ts
import { pgTable, uuid, varchar, timestamp } from 'drizzle-orm/pg-core';

export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).unique().notNull(),
  emailVerified: timestamp('email_verified'),
  name: varchar('name', { length: 255 }),
  image: varchar('image', { length: 500 }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
}, (table) => ({
  emailIdx: index('idx_users_email').on(table.email),
  createdAtIdx: index('idx_users_created_at').on(table.createdAt),
}));
```

```typescript
// packages/database/src/schema/keyword-batches.ts
import { pgTable, uuid, varchar, integer, timestamp, index } from 'drizzle-orm/pg-core';
import { statusEnum } from './enums';
import { users } from './users';

export const keywordBatches = pgTable('keyword_batches', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  filename: varchar('filename', { length: 255 }).notNull(),
  originalSize: integer('original_size').notNull(),
  totalKeywords: integer('total_keywords').notNull(),
  processedKeywords: integer('processed_keywords').default(0).notNull(),
  successfulKeywords: integer('successful_keywords').default(0).notNull(),
  failedKeywords: integer('failed_keywords').default(0).notNull(),
  status: statusEnum('status').default('pending').notNull(),
  processingStartedAt: timestamp('processing_started_at'),
  processingCompletedAt: timestamp('processing_completed_at'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
}, (table) => ({
  userIdIdx: index('idx_keyword_batches_user_id').on(table.userId),
  statusIdx: index('idx_keyword_batches_status').on(table.status),
  createdAtIdx: index('idx_keyword_batches_created_at').on(table.createdAt.desc()),
  userStatusIdx: index('idx_keyword_batches_user_status').on(table.userId, table.status),
}));
```

```typescript
// packages/database/src/schema/keywords.ts
import { pgTable, uuid, varchar, integer, timestamp, text, index } from 'drizzle-orm/pg-core';
import { statusEnum } from './enums';
import { users } from './users';
import { keywordBatches } from './keyword-batches';

export const keywords = pgTable('keywords', {
  id: uuid('id').primaryKey().defaultRandom(),
  batchId: uuid('batch_id').references(() => keywordBatches.id, { onDelete: 'cascade' }).notNull(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  keyword: varchar('keyword', { length: 500 }).notNull(),
  status: statusEnum('status').default('pending').notNull(),
  retryCount: integer('retry_count').default(0).notNull(),
  lastRetryAt: timestamp('last_retry_at'),
  processingStartedAt: timestamp('processing_started_at'),
  processingCompletedAt: timestamp('processing_completed_at'),
  errorMessage: text('error_message'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
}, (table) => ({
  batchIdIdx: index('idx_keywords_batch_id').on(table.batchId),
  userIdIdx: index('idx_keywords_user_id').on(table.userId),
  statusIdx: index('idx_keywords_status').on(table.status),
  keywordIdx: index('idx_keywords_keyword').on(table.keyword),
  userStatusIdx: index('idx_keywords_user_status').on(table.userId, table.status),
  batchStatusIdx: index('idx_keywords_batch_status').on(table.batchId, table.status),
  createdAtIdx: index('idx_keywords_created_at').on(table.createdAt.desc()),
}));
```

```typescript
// packages/database/src/schema/search-results.ts
import { pgTable, uuid, integer, text, varchar, timestamp, jsonb, index } from 'drizzle-orm/pg-core';
import { searchEngineEnum } from './enums';
import { keywords } from './keywords';

export const searchResults = pgTable('search_results', {
  id: uuid('id').primaryKey().defaultRandom(),
  keywordId: uuid('keyword_id').references(() => keywords.id, { onDelete: 'cascade' }).notNull(),
  searchEngine: searchEngineEnum('search_engine').default('google').notNull(),
  totalAds: integer('total_ads').notNull(),
  totalLinks: integer('total_links').notNull(),
  htmlCache: text('html_cache').notNull(),
  pageTitle: varchar('page_title', { length: 500 }),
  searchUrl: varchar('search_url', { length: 1000 }),
  processingTimeMs: integer('processing_time_ms'),
  proxyUsed: varchar('proxy_used', { length: 100 }),
  userAgent: varchar('user_agent', { length: 500 }),
  metadata: jsonb('metadata'),
  scrapedAt: timestamp('scraped_at').defaultNow().notNull()
}, (table) => ({
  keywordIdIdx: index('idx_search_results_keyword_id').on(table.keywordId),
  searchEngineIdx: index('idx_search_results_search_engine').on(table.searchEngine),
  scrapedAtIdx: index('idx_search_results_scraped_at').on(table.scrapedAt.desc()),
  totalAdsIdx: index('idx_search_results_total_ads').on(table.totalAds.desc()),
  totalLinksIdx: index('idx_search_results_total_links').on(table.totalLinks.desc()),
}));
```

```typescript
// packages/database/src/schema/relations.ts
import { relations } from 'drizzle-orm';
import { users } from './users';
import { keywordBatches } from './keyword-batches';
import { keywords } from './keywords';
import { searchResults } from './search-results';

export const usersRelations = relations(users, ({ many }) => ({
  keywordBatches: many(keywordBatches),
  keywords: many(keywords),
}));

export const keywordBatchesRelations = relations(keywordBatches, ({ one, many }) => ({
  user: one(users, {
    fields: [keywordBatches.userId],
    references: [users.id],
  }),
  keywords: many(keywords),
}));

export const keywordsRelations = relations(keywords, ({ one, many }) => ({
  batch: one(keywordBatches, {
    fields: [keywords.batchId],
    references: [keywordBatches.id],
  }),
  user: one(users, {
    fields: [keywords.userId],
    references: [users.id],
  }),
  searchResults: many(searchResults),
}));

export const searchResultsRelations = relations(searchResults, ({ one }) => ({
  keyword: one(keywords, {
    fields: [searchResults.keywordId],
    references: [keywords.id],
  }),
}));
```

```typescript
// packages/database/src/schema/index.ts
export * from './enums';
export * from './users';
export * from './keyword-batches';
export * from './keywords';
export * from './search-results';
export * from './relations';
```

## Database Constraints & Business Rules

### Data Integrity Rules

1. **Cascade Deletes**: When a user is deleted, all their data is automatically removed
2. **Status Consistency**: Batch status must reflect the aggregate status of its keywords
3. **Keyword Uniqueness**: Same keyword can exist multiple times across different batches
4. **Result Uniqueness**: Each keyword can have only one successful search result

### Performance Considerations

1. **Indexing Strategy**:
   - Composite indexes for common query patterns
   - Partial indexes for status-based queries
   - GIN indexes for full-text search and JSONB queries

2. **Partitioning Strategy** (Future):
   - Partition search_results by scraped_at for time-based queries
   - Partition keywords by user_id for large datasets

3. **Archival Strategy**:
   - Archive old search results after 1 year
   - Compress HTML cache for old results
   - Maintain aggregated statistics for historical data

### Row Level Security (RLS) Policies

```sql
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE keyword_batches ENABLE ROW LEVEL SECURITY;
ALTER TABLE keywords ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_results ENABLE ROW LEVEL SECURITY;

-- Users can only access their own data
CREATE POLICY "Users can access own data" ON keyword_batches
    FOR ALL USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can access own keywords" ON keywords
    FOR ALL USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can access own results" ON search_results
    FOR ALL USING (
        keyword_id IN (
            SELECT id FROM keywords WHERE user_id::text = auth.uid()::text
        )
    );
```

This database schema provides a robust foundation for the search keywords scraper application, ensuring data integrity, performance, and security while supporting all required functionality.