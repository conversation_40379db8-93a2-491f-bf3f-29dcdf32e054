# Feature-Based Architecture Guidelines

This document outlines the feature-based architecture pattern used in the Search Keywords Scraper web application to maintain clean separation of concerns and improve maintainability.

## 📂 Directory Structure

### Feature Organization
Features should be organized under `src/features/` with the following structure:

```
src/features/
├── [feature-name]/
│   ├── components/           # Feature-specific UI components
│   ├── hooks/               # Feature-specific React hooks
│   ├── services/            # Business logic and API services
│   ├── types/               # Feature-specific TypeScript types (optional)
│   └── index.ts            # Public API exports
```

### App Directory
The `src/app/` directory should contain **only route declarations** and minimal routing logic:

```
src/app/
├── [route]/
│   ├── page.tsx            # Route component (imports from features)
│   ├── layout.tsx          # Route layout (if needed)
│   └── loading.tsx         # Loading UI (if needed)
```

## ✅ Implementation Examples

### Current Features
- **`auth/`** - Authentication and user management
- **`keywords/`** - CSV upload, keyword processing, batch management
- **`dashboard/`** - Dashboard components and layout

## 🔧 Feature Structure Guidelines

### 1. Components Directory (`/components/`)
**Purpose**: Feature-specific UI components that are not reusable across features.

**Rules**:
- Components should be named descriptively (e.g., `KeywordUploadFlow`, `CsvUploadZone`)
- Internal imports should use relative paths (`./ComponentName`)
- External dependencies should use absolute paths (`@/components/ui/Button`)

**Example**:
```typescript
// ✅ Good: Feature-specific component
export function KeywordUploadFlow() {
  return <div>...</div>
}

// ❌ Avoid: Generic components belong in src/components/ui/
export function Button() {
  return <button>...</button>
}
```

### 2. Hooks Directory (`/hooks/`)
**Purpose**: Feature-specific React hooks for state management and side effects.

**Rules**:
- Hooks should be prefixed with `use` (e.g., `useKeywordBatches`, `useCsvParsing`)
- Business logic should be extracted to services when complex
- API calls should use TanStack Query patterns

**Example**:
```typescript
// ✅ Good: Feature-specific hook
export function useKeywordBatches() {
  return useQuery({
    queryKey: ['keyword-batches'],
    queryFn: () => apiClient.get('/api/v1/keywords/batches')
  })
}
```

### 3. Services Directory (`/services/`)
**Purpose**: Business logic, API clients, and data processing utilities.

**Rules**:
- Services should be classes or pure functions
- Complex business logic should live here, not in components
- API calls and data transformation logic

**Example**:
```typescript
// ✅ Good: Service with business logic
export class CsvParserService {
  static parseCsvFile(file: File): Promise<CsvParseResult> {
    // Complex parsing logic here
  }
}
```

### 4. Index File (`/index.ts`)
**Purpose**: Define the public API of the feature - what other parts of the app can import.

**Rules**:
- Export only what needs to be used outside the feature
- Group exports by type (components, hooks, services, types)
- Use explicit named exports

**Example**:
```typescript
// Components
export { KeywordUploadFlow } from './components/KeywordUploadFlow'
export { CsvUploadZone } from './components/CsvUploadZone'

// Hooks  
export { useKeywordBatches, useCreateKeywordBatch } from './hooks/useKeywordBatch'

// Services
export * from './services/CsvParserService'

// Types
export type { KeywordBatch, CreateBatchRequest } from './hooks/useKeywordBatch'
```

## 🚫 What NOT to Put in Features

### App Directory Should NOT Contain:
- ❌ Complex business logic
- ❌ Feature-specific components
- ❌ Data fetching logic
- ❌ Form validation logic
- ❌ API service calls

### App Directory Should Only Contain:
- ✅ Route declarations (`page.tsx`)
- ✅ Layout components (`layout.tsx`)  
- ✅ Loading/error boundaries
- ✅ Simple imports from features

## 📝 Migration Checklist

When moving functionality into features, follow this checklist:

### Phase 1: Setup
- [ ] Create feature directory structure
- [ ] Create `index.ts` with exports

### Phase 2: Move Files
- [ ] Move components to `[feature]/components/`
- [ ] Move hooks to `[feature]/hooks/`
- [ ] Move services/utilities to `[feature]/services/`

### Phase 3: Update Imports
- [ ] Update internal imports to use relative paths
- [ ] Update external imports to use feature imports
- [ ] Update app routes to import from features
- [ ] Remove old files and directories

### Phase 4: Testing
- [ ] Run TypeScript type checking
- [ ] Test application functionality
- [ ] Verify no circular dependencies

## 🔄 Import Patterns

### From App Routes
```typescript
// ✅ Good: Import from feature public API
import { KeywordUploadFlow } from '@/features/keywords'

// ❌ Avoid: Direct imports from feature internals
import { KeywordUploadFlow } from '@/features/keywords/components/KeywordUploadFlow'
```

### Within Features
```typescript
// ✅ Good: Relative imports within feature
import { CsvUploadZone } from './CsvUploadZone'
import { useCsvParsing } from '../hooks/useCsvParsing'

// ✅ Good: External dependencies
import { Button } from '@/components/ui/Button'
```

### Cross-Feature Dependencies
```typescript
// ✅ Good: Import from other feature's public API
import { useAuth } from '@/features/auth'

// ❌ Avoid: Direct imports from other feature internals  
import { AuthService } from '@/features/auth/services/AuthService'
```

## 🎯 Benefits

### Maintainability
- **Clear boundaries**: Each feature has well-defined responsibilities
- **Easier refactoring**: Changes are contained within feature boundaries
- **Reduced coupling**: Features depend on public APIs, not internals

### Developer Experience
- **Predictable structure**: Developers know where to find feature-related code
- **Easier testing**: Features can be tested in isolation
- **Better collaboration**: Multiple developers can work on different features

### Scalability
- **Feature independence**: Features can evolve separately
- **Code reuse**: Common patterns emerge across features
- **Clean architecture**: Business logic is separated from UI concerns

## 📚 Additional Resources

- [Clean Architecture Principles](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [Next.js App Router Best Practices](https://nextjs.org/docs/app/building-your-application/routing)
- [React Hook Patterns](https://react.dev/reference/react)

---

**Note**: This architecture was implemented after the initial CSV keywords upload feature to improve code organization and maintainability. All new features should follow these guidelines from the start.