# Bull Queue Implementation Plan

**Project**: Search Keywords Scraper  
**Target Issues**: #6 (Bull Queue + Redis), #7 (Retry Logic), #14 (Auto-scaling)  
**Branch**: `feature/issue-6-bull-queue-implementation`  
**Date**: 2025-08-21

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Current State Analysis](#current-state-analysis)
4. [Implementation Phases](#implementation-phases)
5. [Technical Specifications](#technical-specifications)
6. [Success Criteria](#success-criteria)
7. [Risk Mitigation](#risk-mitigation)
8. [Next Steps](#next-steps)

## Overview

This document outlines the implementation plan for integrating Bull Queue with Redis backend to enable distributed, fault-tolerant keyword scraping processing. The implementation transforms the current synchronous keyword processing into an asynchronous, scalable job queue system.

### Goals

- **Reliability**: Implement fault-tolerant job processing with automatic retries
- **Scalability**: Enable horizontal scaling of worker processes  
- **Performance**: Process multiple keyword batches concurrently
- **Observability**: Provide monitoring and introspection of job processing

### Target Issues

- **Issue #6**: Implement Bull Queue with Redis Backend for Job Processing (Priority: Critical)
- **Issue #7**: Implement Robust Retry Logic with Exponential Backoff (Priority: Critical)
- **Issue #14**: Implement Auto-scaling Worker Processes (Priority: Low)

## Architecture

### Current Architecture

```
[Web Frontend] --> [NestJS API] --> [PostgreSQL Database]
                        |
                        v
                  [Direct Processing]
                  (Synchronous)
```

### Target Architecture

```
[Web Frontend] --> [NestJS API] --> [PostgreSQL Database]
                        |                    ^
                        v                    |
                  [Redis Queue] ---------> [Worker Processes]
                        |                    |
                        v                    v
                [Bull Dashboard]      [Google Scraper]
```

### Data Flow

1. **API Layer**: Receives keyword batch from frontend
2. **Job Creation**: Creates individual scraping jobs and enqueues them
3. **Queue Management**: Redis stores jobs with persistence and retry logic
4. **Worker Processing**: Multiple workers consume jobs and execute scraping
5. **Status Updates**: Workers update database with results and progress
6. **Monitoring**: Bull Dashboard provides job introspection and metrics

## Current State Analysis

### Existing Components

**API Backend (NestJS)**:
- Keywords service with `processKeywords()` method
- Database integration with keyword batches and keywords tables
- Status tracking (pending, processing, completed, failed)

**Worker Process**:
- GoogleScraper integration with stealth browser automation
- Winston logging configuration
- Bull dependency already installed
- Currently runs in test mode with TODO for production queue consumer

**Database Schema**:
- `keyword_batches`: Batch tracking with status and progress counters
- `keywords`: Individual keyword records with retry count and status
- Existing status fields support queue integration

### Integration Points

1. **Keywords Service**: `processKeywords()` method needs job enqueueing logic
2. **Worker Index**: Replace test mode with Bull queue consumer
3. **Database Updates**: Coordinate queue status with database status
4. **Error Handling**: Integrate retry logic with existing status tracking

## Implementation Phases

### Phase 1: Foundation Setup (Sprint 1)

#### Step 1: Environment & Infrastructure Setup

**Redis Installation**:
```bash
# Local development
brew install redis
# or using Docker
docker run -d -p 6379:6379 redis:alpine
```

**Environment Configuration**:
```env
# Add to .env files (apps/api/.env, apps/worker/.env)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
```

**Docker Compose for Development**:
```yaml
# docker-compose.yml
version: '3.8'
services:
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

volumes:
  redis_data:
```

#### Step 2: Shared Queue Package

**Package Structure**:
```
packages/queue/
├── package.json
├── src/
│   ├── index.ts
│   ├── types/
│   │   ├── jobs.ts
│   │   ├── queues.ts
│   │   └── index.ts
│   ├── constants/
│   │   ├── queue-names.ts
│   │   ├── job-priorities.ts
│   │   └── index.ts
│   └── validators/
│       ├── job-data.ts
│       └── index.ts
└── tsconfig.json
```

**Job Type Definitions**:
```typescript
// packages/queue/src/types/jobs.ts
export interface KeywordScrapingJob {
  keywordId: string;
  batchId: string;
  userId: string;
  keyword: string;
  searchEngine: 'google' | 'bing';
  retryAttempt: number;
  priority?: 'high' | 'normal' | 'low';
  metadata?: Record<string, any>;
}

export interface BatchJob {
  batchId: string;
  userId: string;
  action: 'complete' | 'failed' | 'progress_update';
  completedCount?: number;
  failedCount?: number;
  totalCount?: number;
}

export type QueueJobData = KeywordScrapingJob | BatchJob;
```

**Queue Configuration**:
```typescript
// packages/queue/src/constants/queue-names.ts
export const QUEUE_NAMES = {
  SCRAPER_KEYWORD: 'scraper-keyword',
  SCRAPER_BATCH: 'scraper-batch',
} as const;

export const JOB_PRIORITIES = {
  HIGH: 1,      // Retry jobs, urgent processing
  NORMAL: 5,    // Standard new jobs
  LOW: 10,      // Background tasks
} as const;
```

### Phase 2: API Integration (Sprint 2)

#### Step 3: Queue Service Implementation

**Package Dependencies**:
```json
// apps/api/package.json additions
{
  "dependencies": {
    "@nestjs/bull": "^0.6.3",
    "bull": "^4.16.3",
    "ioredis": "^5.4.1",
    "@search-keywords-scraper/queue": "workspace:*"
  }
}
```

**Queue Module Setup**:
```typescript
// apps/api/src/core/queue/queue.module.ts
import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { QueueService } from './queue.service';
import { QUEUE_NAMES } from '@search-keywords-scraper/queue';

@Module({
  imports: [
    BullModule.forRootAsync({
      useFactory: () => ({
        redis: {
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT || '6379'),
          password: process.env.REDIS_PASSWORD,
        },
        defaultJobOptions: {
          attempts: 3,
          backoff: { type: 'exponential', delay: 60000 },
          removeOnComplete: 100,
          removeOnFail: 50,
        },
      }),
    }),
    BullModule.registerQueue(
      { name: QUEUE_NAMES.SCRAPER_KEYWORD },
      { name: QUEUE_NAMES.SCRAPER_BATCH }
    ),
  ],
  providers: [QueueService],
  exports: [QueueService, BullModule],
})
export class QueueModule {}
```

**Queue Service Implementation**:
```typescript
// apps/api/src/core/queue/queue.service.ts
import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { KeywordScrapingJob, BatchJob, QUEUE_NAMES, JOB_PRIORITIES } from '@search-keywords-scraper/queue';

@Injectable()
export class QueueService {
  constructor(
    @InjectQueue(QUEUE_NAMES.SCRAPER_KEYWORD)
    private keywordQueue: Queue<KeywordScrapingJob>,
    
    @InjectQueue(QUEUE_NAMES.SCRAPER_BATCH)
    private batchQueue: Queue<BatchJob>,
  ) {}

  async enqueueKeywordJob(jobData: KeywordScrapingJob, priority: number = JOB_PRIORITIES.NORMAL) {
    return this.keywordQueue.add('scrape-keyword', jobData, {
      priority,
      jobId: `keyword-${jobData.keywordId}-${Date.now()}`,
    });
  }

  async enqueueBatchJob(jobData: BatchJob, priority: number = JOB_PRIORITIES.NORMAL) {
    return this.batchQueue.add('process-batch', jobData, {
      priority,
      jobId: `batch-${jobData.batchId}-${jobData.action}-${Date.now()}`,
    });
  }

  async getQueueStatus() {
    const [keywordWaiting, keywordActive, keywordCompleted, keywordFailed] = await Promise.all([
      this.keywordQueue.getWaiting(),
      this.keywordQueue.getActive(),
      this.keywordQueue.getCompleted(),
      this.keywordQueue.getFailed(),
    ]);

    return {
      keyword: {
        waiting: keywordWaiting.length,
        active: keywordActive.length,
        completed: keywordCompleted.length,
        failed: keywordFailed.length,
      },
    };
  }
}
```

#### Step 4: Keywords Service Integration

**Updated Keywords Service**:
```typescript
// apps/api/src/features/keywords/services/keywords.service.ts
import { QueueService } from '../../../core/queue/queue.service';
import { KeywordScrapingJob, JOB_PRIORITIES } from '@search-keywords-scraper/queue';

@Injectable()
export class KeywordsService {
  constructor(
    private queueService: QueueService,
    // ... other dependencies
  ) {}

  async processKeywords(
    userId: string,
    data: CreateKeywordsDto,
  ): Promise<ProcessKeywordsResponse> {
    // ... existing validation logic

    try {
      return await db.transaction(async (tx: any) => {
        // Create the batch
        const [batch] = await tx
          .insert(keywordBatches)
          .values({
            userId: userId,
            name: data.name,
            status: 'pending',
            totalKeywords: uniqueKeywords.length,
            completedKeywords: 0,
            failedKeywords: 0,
          })
          .returning();

        // Create individual keywords
        const keywordRecords = uniqueKeywords.map((keyword) => ({
          batchId: batch.id,
          keyword: keyword.trim(),
          status: 'pending' as const,
          retryCount: 0,
          searchEngine: data.searchEngine || ('google' as const),
        }));

        const insertedKeywords = await tx.insert(keywords).values(keywordRecords).returning();

        // Enqueue scraping jobs
        const jobPromises = insertedKeywords.map((keywordRecord) => {
          const jobData: KeywordScrapingJob = {
            keywordId: keywordRecord.id,
            batchId: batch.id,
            userId: userId,
            keyword: keywordRecord.keyword,
            searchEngine: keywordRecord.searchEngine,
            retryAttempt: 0,
          };

          return this.queueService.enqueueKeywordJob(jobData, JOB_PRIORITIES.NORMAL);
        });

        await Promise.all(jobPromises);

        return {
          batch: {
            id: batch.id,
            name: batch.name,
            status: batch.status,
            totalKeywords: batch.totalKeywords,
            completedKeywords: batch.completedKeywords,
            failedKeywords: batch.failedKeywords,
            userId: batch.userId,
            createdAt: batch.createdAt.toISOString(),
            updatedAt: batch.updatedAt.toISOString(),
          },
          totalKeywords: uniqueKeywords.length,
          message: `Successfully created batch "${data.name}" with ${uniqueKeywords.length} jobs queued for processing`,
        };
      });
    } catch (error) {
      throw new BadRequestException(
        `Failed to create keyword batch: ${error.message}`,
      );
    }
  }
}
```

#### Step 5: Job Event Handlers

**Job Event Processor**:
```typescript
// apps/api/src/core/queue/processors/job-events.processor.ts
import { Processor, Process, OnQueueActive, OnQueueCompleted, OnQueueFailed } from '@nestjs/bull';
import { Job } from 'bull';
import { Logger } from '@nestjs/common';
import { KeywordScrapingJob, QUEUE_NAMES } from '@search-keywords-scraper/queue';
import { KeywordStatusService } from './keyword-status.service';

@Processor(QUEUE_NAMES.SCRAPER_KEYWORD)
export class JobEventsProcessor {
  private readonly logger = new Logger(JobEventsProcessor.name);

  constructor(private keywordStatusService: KeywordStatusService) {}

  @OnQueueActive()
  async onActive(job: Job<KeywordScrapingJob>) {
    this.logger.log(`Job ${job.id} started processing keyword: ${job.data.keyword}`);
    await this.keywordStatusService.updateKeywordStatus(
      job.data.keywordId, 
      'processing'
    );
  }

  @OnQueueCompleted()
  async onCompleted(job: Job<KeywordScrapingJob>, result: any) {
    this.logger.log(`Job ${job.id} completed for keyword: ${job.data.keyword}`);
    await this.keywordStatusService.updateKeywordStatus(
      job.data.keywordId, 
      'completed',
      result
    );
  }

  @OnQueueFailed()
  async onFailed(job: Job<KeywordScrapingJob>, error: Error) {
    this.logger.error(`Job ${job.id} failed for keyword: ${job.data.keyword}`, error);
    
    const shouldRetry = job.attemptsMade < (job.opts.attempts || 3);
    const status = shouldRetry ? 'pending' : 'failed';
    
    await this.keywordStatusService.updateKeywordStatus(
      job.data.keywordId,
      status,
      null,
      job.attemptsMade
    );
  }
}
```

### Phase 3: Worker Integration (Sprint 3)

#### Step 6: Worker Queue Consumer

**Worker Package Dependencies**:
```json
// apps/worker/package.json additions  
{
  "dependencies": {
    "bull": "^4.16.3",
    "ioredis": "^5.4.1",
    "@search-keywords-scraper/queue": "workspace:*",
    "@search-keywords-scraper/database": "workspace:*"
  }
}
```

**Worker Queue Setup**:
```typescript
// apps/worker/src/queue/queue-consumer.ts
import Bull from 'bull';
import { createLogger } from 'winston';
import { KeywordScrapingJob, QUEUE_NAMES } from '@search-keywords-scraper/queue';
import { GoogleScraper } from '@search-keywords-scraper/scraper';
import { KeywordProcessor } from './processors/keyword.processor';

export class QueueConsumer {
  private keywordQueue: Bull.Queue<KeywordScrapingJob>;
  private keywordProcessor: KeywordProcessor;
  private logger: any;

  constructor(
    private scraperConfig: any,
    private workerConfig: any
  ) {
    this.logger = createLogger({/* winston config */});
    this.keywordProcessor = new KeywordProcessor(scraperConfig, this.logger);
    this.setupQueues();
  }

  private setupQueues() {
    const redisConfig = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
    };

    this.keywordQueue = new Bull(QUEUE_NAMES.SCRAPER_KEYWORD, {
      redis: redisConfig,
      defaultJobOptions: {
        attempts: 3,
        backoff: { type: 'exponential', delay: 60000 },
      },
    });

    // Set up job processors
    this.keywordQueue.process(
      'scrape-keyword',
      this.workerConfig.concurrency,
      this.processKeywordJob.bind(this)
    );

    // Set up event handlers
    this.setupEventHandlers();
  }

  private async processKeywordJob(job: Bull.Job<KeywordScrapingJob>) {
    return this.keywordProcessor.process(job);
  }

  private setupEventHandlers() {
    this.keywordQueue.on('completed', (job, result) => {
      this.logger.info(`Job completed: ${job.id}`, { result });
    });

    this.keywordQueue.on('failed', (job, error) => {
      this.logger.error(`Job failed: ${job.id}`, { error: error.message });
    });

    this.keywordQueue.on('stalled', (job) => {
      this.logger.warn(`Job stalled: ${job.id}`);
    });
  }

  async start() {
    this.logger.info('Queue consumer starting...', {
      queues: [QUEUE_NAMES.SCRAPER_KEYWORD],
      concurrency: this.workerConfig.concurrency,
    });

    // Wait for Redis connection
    await this.keywordQueue.isReady();
    this.logger.info('Connected to Redis and ready to process jobs');
  }

  async stop() {
    this.logger.info('Shutting down queue consumer...');
    await this.keywordQueue.close();
    this.logger.info('Queue consumer stopped');
  }
}
```

#### Step 7: Keyword Job Processor

**Keyword Processor Implementation**:
```typescript
// apps/worker/src/queue/processors/keyword.processor.ts
import { Job } from 'bull';
import { GoogleScraper } from '@search-keywords-scraper/scraper';
import { KeywordScrapingJob } from '@search-keywords-scraper/queue';
import { db } from '@search-keywords-scraper/database';
import { keywords, searchResults } from '@search-keywords-scraper/database';
import { eq } from 'drizzle-orm';

export class KeywordProcessor {
  private scraper: GoogleScraper;

  constructor(
    private scraperConfig: any,
    private logger: any
  ) {
    this.scraper = new GoogleScraper(scraperConfig);
  }

  async process(job: Job<KeywordScrapingJob>) {
    const { keywordId, keyword, searchEngine, userId } = job.data;

    try {
      this.logger.info(`Processing keyword: ${keyword}`, { jobId: job.id, keywordId });

      // Update job progress
      await job.progress(10);

      // Initialize scraper if not already done
      if (!this.scraper.isInitialized()) {
        await this.scraper.initialize();
      }

      await job.progress(25);

      // Perform the scraping
      const scrapingResult = await this.scraper.search({
        query: keyword,
        page: 1,
        resultsPerPage: 10,
      });

      await job.progress(75);

      // Store results in database
      const [searchResult] = await db
        .insert(searchResults)
        .values({
          keywordId: keywordId,
          userId: userId,
          query: keyword,
          searchEngine: searchEngine,
          adsCount: scrapingResult.adsCount,
          totalLinksCount: scrapingResult.totalLinksCount,
          htmlCache: scrapingResult.htmlCache,
          results: JSON.stringify(scrapingResult.results),
          scrapedAt: new Date(),
        })
        .returning();

      // Update keyword status
      await db
        .update(keywords)
        .set({
          status: 'completed',
          updatedAt: new Date(),
        })
        .where(eq(keywords.id, keywordId));

      await job.progress(100);

      this.logger.info(`Keyword processing completed: ${keyword}`, {
        jobId: job.id,
        keywordId,
        adsCount: scrapingResult.adsCount,
        linksCount: scrapingResult.totalLinksCount,
      });

      return {
        keywordId,
        keyword,
        adsCount: scrapingResult.adsCount,
        totalLinksCount: scrapingResult.totalLinksCount,
        searchResultId: searchResult.id,
        status: 'completed',
      };

    } catch (error) {
      this.logger.error(`Keyword processing failed: ${keyword}`, {
        jobId: job.id,
        keywordId,
        error: error.message,
        stack: error.stack,
      });

      // Update keyword with failure
      await db
        .update(keywords)
        .set({
          status: job.attemptsMade >= (job.opts.attempts || 3) ? 'failed' : 'pending',
          retryCount: job.attemptsMade,
          updatedAt: new Date(),
        })
        .where(eq(keywords.id, keywordId));

      throw error; // Re-throw to trigger Bull's retry mechanism
    }
  }
}
```

#### Step 8: Updated Worker Entry Point

**Worker Main File**:
```typescript
// apps/worker/src/index.ts
import { createLogger, format, transports } from 'winston';
import { scraperConfig, workerConfig } from './config';
import { QueueConsumer } from './queue/queue-consumer';

const logger = createLogger({
  level: workerConfig.logLevel,
  format: format.combine(
    format.timestamp(),
    format.errors({ stack: true }),
    format.json()
  ),
  defaultMeta: { service: 'worker' },
  transports: [
    new transports.File({ filename: 'logs/worker-error.log', level: 'error' }),
    new transports.File({ filename: 'logs/worker-combined.log' }),
    new transports.Console({
      format: format.combine(
        format.colorize(),
        format.simple()
      )
    })
  ],
});

let queueConsumer: QueueConsumer;

async function main() {
  logger.info('Worker starting...', {
    mode: workerConfig.testMode ? 'TEST' : 'PRODUCTION',
    config: {
      headless: scraperConfig.browser.headless,
      requestsPerHour: scraperConfig.scraping.requestsPerHour,
      concurrency: workerConfig.concurrency
    }
  });

  try {
    queueConsumer = new QueueConsumer(scraperConfig, workerConfig);
    await queueConsumer.start();

    logger.info('Worker started successfully and waiting for jobs...');

    // Keep the process running
    process.on('SIGTERM', gracefulShutdown);
    process.on('SIGINT', gracefulShutdown);

  } catch (error) {
    logger.error('Failed to start worker:', error);
    process.exit(1);
  }
}

async function gracefulShutdown() {
  logger.info('Received shutdown signal, shutting down gracefully...');
  
  if (queueConsumer) {
    await queueConsumer.stop();
  }
  
  logger.info('Worker shutdown complete');
  process.exit(0);
}

main().catch((error) => {
  logger.error('Fatal error:', error);
  process.exit(1);
});
```

### Phase 4: Error Handling & Monitoring (Sprint 4)

#### Step 9: Retry Logic Implementation

**Enhanced Job Options**:
```typescript
// Exponential backoff configuration
const jobOptions = {
  attempts: 3,
  backoff: {
    type: 'exponential',
    settings: {
      delay: 60000, // Start with 1 minute
      multiplier: 2, // Double each time
      maxDelay: 900000, // Cap at 15 minutes
    },
  },
  removeOnComplete: 100,
  removeOnFail: 50,
};

// Error-specific retry logic
export class RetryHandler {
  static shouldRetry(error: Error, attemptsMade: number): boolean {
    const maxAttempts = 3;
    
    if (attemptsMade >= maxAttempts) {
      return false;
    }

    // Network errors - always retry
    if (error.message.includes('ECONNREFUSED') || 
        error.message.includes('ETIMEDOUT')) {
      return true;
    }

    // Rate limiting - retry with longer delay
    if (error.message.includes('rate limit') ||
        error.message.includes('429')) {
      return true;
    }

    // Scraping failures - retry with different user agent
    if (error.message.includes('blocked') ||
        error.message.includes('captcha')) {
      return true;
    }

    // Invalid keywords - don't retry
    if (error.message.includes('invalid keyword')) {
      return false;
    }

    // Default: retry for unknown errors
    return true;
  }

  static getRetryDelay(attemptsMade: number): number {
    const baseDelay = 60000; // 1 minute
    return Math.min(baseDelay * Math.pow(2, attemptsMade), 900000); // Max 15 minutes
  }
}
```

#### Step 10: Monitoring Dashboard

**Bull Dashboard Integration**:
```typescript
// apps/api/src/core/queue/dashboard/dashboard.module.ts
import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { createBullBoard } from '@bull-board/api';
import { BullAdapter } from '@bull-board/api/bullAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { QUEUE_NAMES } from '@search-keywords-scraper/queue';

@Module({})
export class DashboardModule {
  static forRoot() {
    const serverAdapter = new ExpressAdapter();
    serverAdapter.setBasePath('/admin/queues');

    createBullBoard({
      queues: [
        new BullAdapter(BullModule.getQueue(QUEUE_NAMES.SCRAPER_KEYWORD)),
        new BullAdapter(BullModule.getQueue(QUEUE_NAMES.SCRAPER_BATCH)),
      ],
      serverAdapter,
    });

    return {
      module: DashboardModule,
      serverAdapter,
    };
  }
}
```

**Health Check Endpoints**:
```typescript
// apps/api/src/core/queue/health/queue-health.controller.ts
import { Controller, Get } from '@nestjs/common';
import { QueueService } from '../queue.service';

@Controller('health/queue')
export class QueueHealthController {
  constructor(private queueService: QueueService) {}

  @Get()
  async getQueueHealth() {
    try {
      const status = await this.queueService.getQueueStatus();
      
      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        queues: status,
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Get('redis')
  async getRedisHealth() {
    // Redis connection health check
    try {
      // Implementation to check Redis connectivity
      return { status: 'connected', timestamp: new Date().toISOString() };
    } catch (error) {
      return { status: 'disconnected', error: error.message };
    }
  }
}
```

### Phase 5: Testing & Production (Sprint 5)

#### Step 11: Integration Testing

**Queue Test Suite**:
```typescript
// apps/api/src/core/queue/__tests__/queue.integration.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { QueueService } from '../queue.service';
import { QueueModule } from '../queue.module';

describe('Queue Integration Tests', () => {
  let service: QueueService;
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [QueueModule],
    }).compile();

    service = module.get<QueueService>(QueueService);
  });

  afterEach(async () => {
    await module.close();
  });

  describe('Job Creation', () => {
    it('should create and enqueue keyword job', async () => {
      const jobData = {
        keywordId: 'test-keyword-id',
        batchId: 'test-batch-id',
        userId: 'test-user-id',
        keyword: 'test keyword',
        searchEngine: 'google' as const,
        retryAttempt: 0,
      };

      const job = await service.enqueueKeywordJob(jobData);
      expect(job.id).toBeDefined();
      expect(job.data).toEqual(jobData);
    });
  });

  describe('Queue Status', () => {
    it('should return queue statistics', async () => {
      const status = await service.getQueueStatus();
      
      expect(status).toHaveProperty('keyword');
      expect(status.keyword).toHaveProperty('waiting');
      expect(status.keyword).toHaveProperty('active');
    });
  });
});
```

**Worker Integration Tests**:
```typescript
// apps/worker/src/__tests__/worker.integration.spec.ts
import { QueueConsumer } from '../queue/queue-consumer';
import { scraperConfig, workerConfig } from '../config';

describe('Worker Integration Tests', () => {
  let queueConsumer: QueueConsumer;

  beforeEach(() => {
    queueConsumer = new QueueConsumer(scraperConfig, workerConfig);
  });

  afterEach(async () => {
    await queueConsumer.stop();
  });

  describe('Queue Processing', () => {
    it('should process keyword jobs successfully', async () => {
      // Mock job processing test
      await queueConsumer.start();
      
      // Add test job and verify processing
      // Implementation depends on test setup
    });

    it('should handle job failures with retry logic', async () => {
      // Test retry mechanism
    });
  });
});
```

#### Step 12: Production Configuration

**Production Environment Variables**:
```env
# Production .env
NODE_ENV=production

# Redis Configuration
REDIS_URL=redis://your-production-redis:6379
REDIS_HOST=your-production-redis
REDIS_PORT=6379
REDIS_PASSWORD=your-secure-password

# Queue Configuration
QUEUE_CONCURRENCY=5
QUEUE_MAX_ATTEMPTS=3
QUEUE_CLEANUP_INTERVAL=3600000

# Worker Configuration  
WORKER_CONCURRENCY=3
WORKER_LOG_LEVEL=info
```

**Docker Configuration**:
```dockerfile
# apps/worker/Dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY pnpm-lock.yaml ./

# Install dependencies
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Build application
RUN pnpm build

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node ./dist/health-check.js

# Start worker
CMD ["node", "dist/index.js"]
```

**Production Deployment Script**:
```bash
#!/bin/bash
# scripts/deploy-workers.sh

set -e

echo "Deploying Bull Queue Workers..."

# Build all packages
pnpm build

# Deploy API with queue support
echo "Deploying API..."
cd apps/api
docker build -t scraper-api:latest .
# Deploy to your infrastructure

# Deploy workers
echo "Deploying workers..."
cd ../worker
docker build -t scraper-worker:latest .
# Deploy multiple worker instances

echo "Deployment complete!"
```

## Technical Specifications

### Queue Configuration

**Queue Names**: Following `{service}-{action}` convention:
- `scraper-keyword`: Individual keyword scraping jobs
- `scraper-batch`: Batch processing and completion tracking

**Job Priorities**:
- `HIGH (1)`: Retry jobs, urgent processing
- `NORMAL (5)`: Standard new jobs  
- `LOW (10)`: Background maintenance tasks

**Job Options**:
```javascript
{
  attempts: 3,
  backoff: { 
    type: 'exponential',
    delay: 60000,
    settings: { multiplier: 2, maxDelay: 900000 }
  },
  removeOnComplete: 100,
  removeOnFail: 50,
  timeout: 300000, // 5 minutes
}
```

### Error Handling Strategy

**Error Categories**:
1. **Retryable Errors**:
   - Network timeouts (`ETIMEDOUT`, `ECONNREFUSED`)
   - Rate limiting (`429`, `rate limit exceeded`)
   - Temporary scraping blocks (`blocked`, `captcha`)

2. **Non-Retryable Errors**:
   - Invalid keyword format
   - User authentication failures
   - Permanent bans

**Retry Logic**:
- **Attempt 1**: Immediate retry with same configuration
- **Attempt 2**: 1-minute delay, different user agent
- **Attempt 3**: 5-minute delay, different proxy (if available)
- **Failed**: Mark as permanently failed, update database

### Database Integration

**Status Synchronization**:
- Job created → Keyword status: `pending`
- Job started → Keyword status: `processing` 
- Job completed → Keyword status: `completed` + results stored
- Job failed (final) → Keyword status: `failed`

**Batch Progress Tracking**:
```sql
-- Automatic batch status calculation
UPDATE keyword_batches 
SET 
  completed_keywords = (SELECT COUNT(*) FROM keywords WHERE batch_id = $1 AND status = 'completed'),
  failed_keywords = (SELECT COUNT(*) FROM keywords WHERE batch_id = $1 AND status = 'failed'),
  status = CASE 
    WHEN (SELECT COUNT(*) FROM keywords WHERE batch_id = $1 AND status IN ('pending', 'processing')) = 0 THEN 'completed'
    ELSE 'processing'
  END
WHERE id = $1;
```

### Performance Considerations

**Worker Scaling**:
- **Development**: 1 worker, concurrency 1
- **Staging**: 2 workers, concurrency 2  
- **Production**: 3-5 workers, concurrency 3-5

**Rate Limiting Coordination**:
- Share rate limit counter across workers using Redis
- Implement distributed throttling for scraper requests
- Queue jobs with delays to respect API limits

**Memory Management**:
- Limit job retention (100 completed, 50 failed)
- Clean up stale jobs automatically
- Monitor Redis memory usage

## Success Criteria

### Functional Requirements

1. **Job Processing**: 
   - ✅ Keywords submitted via API are queued and processed asynchronously
   - ✅ Database status accurately reflects processing pipeline state
   - ✅ Multiple workers can process jobs concurrently without conflicts

2. **Error Handling**:
   - ✅ Failed jobs retry automatically with exponential backoff (max 3 attempts)
   - ✅ Different error types handled appropriately (retry vs permanent failure)
   - ✅ Job failures don't crash workers or corrupt data

3. **Monitoring**:
   - ✅ Queue status and job progress visible in Bull Dashboard
   - ✅ Health check endpoints report system status
   - ✅ Comprehensive logging of job lifecycle events

### Non-Functional Requirements

1. **Performance**:
   - ✅ System processes keyword batches concurrently
   - ✅ Worker scaling doesn't require code changes
   - ✅ Rate limiting respected across distributed workers

2. **Reliability**:
   - ✅ Jobs persist across Redis restarts
   - ✅ Workers handle graceful shutdown during deployments  
   - ✅ Data consistency maintained under concurrent processing

3. **Scalability**:
   - ✅ Additional workers can be added horizontally
   - ✅ Queue handles increased load without degradation
   - ✅ Auto-scaling possible based on queue depth

### Issue Resolution Mapping

- **Issue #6** (Bull Queue + Redis): ✅ Complete queue infrastructure with Redis persistence
- **Issue #7** (Retry Logic): ✅ Exponential backoff with configurable attempts and error categorization
- **Issue #14** (Auto-scaling): ✅ Worker architecture supports horizontal scaling and load balancing

## Risk Mitigation

### Technical Risks

1. **Redis Connection Failures**:
   - **Mitigation**: Connection retry logic, health checks, fallback to sync processing
   - **Monitoring**: Redis connectivity alerts, connection pool metrics

2. **Job Data Corruption**:
   - **Mitigation**: Job data validation, schema versioning, backwards compatibility
   - **Testing**: Comprehensive integration tests, data validation tests

3. **Worker Crashes**:
   - **Mitigation**: Graceful shutdown handling, job recovery, worker health monitoring
   - **Deployment**: Rolling deployments, container restart policies

4. **Database Consistency**:
   - **Mitigation**: Database transactions, status reconciliation jobs, consistency checks
   - **Testing**: Concurrent processing tests, data integrity validation

### Operational Risks

1. **Performance Degradation**:
   - **Mitigation**: Performance monitoring, queue depth alerts, auto-scaling triggers
   - **Testing**: Load testing with realistic batch sizes, performance benchmarking

2. **Rate Limit Violations**:
   - **Mitigation**: Distributed rate limiting, request throttling, backoff strategies
   - **Monitoring**: Rate limit violation alerts, request rate dashboards

3. **Deployment Issues**:
   - **Mitigation**: Feature flags, gradual rollout, rollback procedures
   - **Testing**: Staging environment validation, deployment automation

### Rollback Strategy

1. **Feature Flag Implementation**:
   ```typescript
   // Environment variable to control processing mode
   const USE_ASYNC_PROCESSING = process.env.USE_ASYNC_PROCESSING === 'true';
   
   if (USE_ASYNC_PROCESSING) {
     // Use queue-based processing
     return await this.queueService.enqueueKeywordJob(jobData);
   } else {
     // Fall back to synchronous processing
     return await this.processSynchronously(data);
   }
   ```

2. **Gradual Migration**:
   - Start with small percentage of traffic on queue processing
   - Monitor error rates and performance metrics
   - Gradually increase traffic to queue processing
   - Full rollback possible by setting feature flag to false

## Next Steps

### Immediate Actions (Week 1)

1. **Environment Setup**:
   ```bash
   # Install Redis locally
   brew install redis
   redis-server
   
   # Create packages/queue package
   mkdir -p packages/queue/src
   # Initialize package.json and implement shared types
   ```

2. **Dependency Installation**:
   ```bash
   # Add dependencies to API
   cd apps/api
   pnpm add @nestjs/bull bull ioredis
   
   # Add queue package to both API and worker
   pnpm add @search-keywords-scraper/queue@workspace:*
   ```

3. **Initial Implementation**:
   - Create shared queue types package
   - Implement basic queue service in API
   - Set up Redis connection configuration

### Sprint Planning

**Sprint 1 (Foundation)**: Redis setup, shared types, basic configuration
**Sprint 2 (API Producer)**: Queue service, job creation, event handling
**Sprint 3 (Worker Consumer)**: Queue consumer, job processing, scraping integration
**Sprint 4 (Reliability)**: Error handling, retry logic, monitoring dashboard
**Sprint 5 (Production)**: Testing, optimization, deployment configuration

### Validation Checkpoints

1. **Milestone 1**: Jobs can be created and queued successfully
2. **Milestone 2**: Workers can consume and process jobs end-to-end
3. **Milestone 3**: Failed jobs retry with exponential backoff
4. **Milestone 4**: Multiple workers can process jobs concurrently
5. **Milestone 5**: System handles production load with comprehensive monitoring

### Success Metrics

- **Functional**: All keyword processing happens asynchronously through queues
- **Performance**: 95%+ job success rate, <5 minute average processing time
- **Reliability**: Zero data loss, graceful handling of failures and restarts
- **Scalability**: Linear performance improvement with additional workers

---

**Document Version**: 1.0  
**Last Updated**: 2025-08-21  
**Next Review**: After Sprint 1 completion