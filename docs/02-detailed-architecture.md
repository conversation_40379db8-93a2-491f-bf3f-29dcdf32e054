# Detailed Architecture

## Technology Stack Deep Dive

### Frontend Stack (Next.js App)

#### Core Technologies
- **Next.js 15+**: React framework with App Router, Server Components, and Streaming
- **TypeScript 5.0+**: Type safety across the entire application
- **Supabase Auth**: Authentication with JWT tokens
- **Tailwind CSS 3.4+**: Utility-first CSS framework
- **SASS/SCSS**: CSS preprocessor for complex styling
- **shadcn/ui**: High-quality React components built on Radix UI

#### State Management & Data Fetching
- **TanStack Query (React Query) 5.0+**: Server state management with caching
- **Zustand 4.4+**: Lightweight client-side state management
- **React Hook Form 7.45+**: Performant forms with validation
- **Zod 3.22+**: TypeScript-first schema validation

#### Development Tools
- **Biome**: Fast formatter and linter for JavaScript, TypeScript, JSX, and JSON
- **Husky**: Git hooks for pre-commit validation

### Backend API Stack (NestJS)

#### Core Framework
- **NestJS 10.0+**: Enterprise-grade Node.js framework
- **Express.js**: Underlying HTTP server (NestJS default)
- **TypeScript 5.0+**: Full type safety in backend

#### Database & ORM
- **Drizzle ORM 0.29+**: Type-safe SQL ORM with excellent TypeScript support
- **PostgreSQL 15+**: Primary database (hosted on Supabase)
- **Drizzle Kit**: Database migrations and schema management

#### Authentication & Security
- **Supabase Auth**: JWT token verification middleware
- **@supabase/ssr**: Server-side rendering support for auth
- **helmet**: Security headers middleware
- **cors**: Cross-origin resource sharing configuration

#### Job Queue & Processing
- **Bull 4.12+**: Redis-based job queue
- **Redis 7.0+**: In-memory data store (Upstash hosted)
- **Bull-Board**: Queue monitoring dashboard

### Worker Stack (Node.js TypeScript)

#### Core Technologies
- **Node.js 18+**: Runtime environment
- **TypeScript 5.0+**: Type safety in worker processes
- **Bull**: Job queue consumer
- **Drizzle ORM**: Database operations

#### Web Scraping Stack
- **Playwright 1.40+**: Browser automation (enhanced version of POC)
- **patchright**: Playwright fork with additional stealth features
- **playwright-extra**: Stealth plugins and anti-detection
- **cheerio 1.1+**: Server-side HTML parsing
- **axios 1.6+**: HTTP client for API requests

#### Monitoring & Logging
- **Winston 3.11+**: Structured logging
- **Prometheus**: Metrics collection (optional)
- **Sentry**: Error tracking and performance monitoring

## Detailed Component Architecture

### 1. Frontend Application Flow

```mermaid
graph TB
    subgraph "Next.js App Router Structure"
        A[app/layout.tsx] --> B[app/page.tsx - Dashboard]
        A --> C[app/auth/signin/page.tsx]
        A --> D[app/auth/signup/page.tsx]
        A --> E[app/upload/page.tsx]
        A --> F[app/keywords/page.tsx]
        A --> G["app/keywords/[id]/page.tsx"]
    end
    
    subgraph "Component Hierarchy"
        B --> H[UploadForm Component]
        B --> I[RecentBatches Component]
        F --> J[KeywordTable Component]
        F --> K[SearchFilter Component]
        G --> L[KeywordDetail Component]
        G --> M[SearchResults Component]
    end
    
    subgraph "State Management"
        H --> N[TanStack Query - Upload Mutation]
        I --> O[TanStack Query - Batches Query]
        J --> P[TanStack Query - Keywords Query]
        L --> Q[TanStack Query - Results Query]
    end
    
    subgraph "Authentication Flow"
        C --> R[Supabase Auth Provider]
        R --> S[Supabase Auth Backend]
        S --> T[Custom JWT from Backend]
        T --> U[Protected Route Middleware]
    end
```

### 2. API Architecture (NestJS)

```mermaid
graph TB
    subgraph "NestJS Application Structure"
        A[main.ts] --> B[app.module.ts]
        B --> C[auth.module.ts]
        B --> D[keywords.module.ts]
        B --> E[upload.module.ts]
        B --> F[queue.module.ts]
    end
    
    subgraph "Module Details"
        C --> C1[auth.middleware.ts - JWT Verification]
        D --> D1[keywords.controller.ts]
        D --> D2[keywords.service.ts]
        D --> D3[keywords.dto.ts]
        E --> E1[keywords.controller.ts - Array Input]
        E --> E2[keywords.service.ts]
        F --> F1[queue.service.ts]
        F --> F2[queue.processor.ts]
    end
    
    subgraph "Database Layer"
        D2 --> G[Drizzle ORM]
        E2 --> G
        F2 --> G
        G --> H[(PostgreSQL Database)]
    end
    
    subgraph "Queue Integration"
        F1 --> I[Bull Queue Manager]
        I --> J[(Redis - Upstash)]
        F2 --> K[Job Creation & Status Updates]
    end
```

### 3. Worker Process Architecture

```mermaid
graph TB
    subgraph "Worker Application"
        A[worker.ts] --> B[QueueConsumer]
        B --> C[KeywordProcessor]
        C --> D[EnhancedGoogleScraper]
    end
    
    subgraph "Scraping Pipeline"
        D --> E[ProxyManager]
        D --> F[BrowserManager]
        D --> G[StealthConfig]
        E --> H[Proxy Pool Rotation]
        F --> I[Browser Context Pool]
        G --> J[Anti-Detection Measures]
    end
    
    subgraph "Data Processing"
        D --> K[SearchResultParser]
        K --> L[Ad Counter]
        K --> M[Link Counter]
        K --> N[HTML Cache]
        L --> O[Database Storage]
        M --> O
        N --> O
    end
    
    subgraph "Error Handling & Retry"
        D --> P[Circuit Breaker]
        P --> Q[Retry Logic]
        Q --> R[Dead Letter Queue]
        R --> S[Manual Review Queue]
    end
```

### 4. Database Schema

The database schema uses Drizzle ORM with PostgreSQL and is designed for optimal performance and data integrity. For detailed schema definitions, ERD, and implementation details, see [Database Schema Documentation](./07-database-schema.md).

**Key Entities:**
- **users**: User accounts with Supabase Auth integration
- **keyword_batches**: Groups of keywords uploaded together
- **keywords**: Individual keywords with processing status
- **search_results**: Scraped data including ads count, links count, and HTML cache

**Key Features:**
- Row-level security (RLS) for data isolation
- Optimized indexes for common query patterns
- Cascade deletes for data consistency
- JSONB metadata storage for flexible scraping data

### 5. Scraper Architecture

```mermaid
sequenceDiagram
    participant Q as Bull Queue
    participant W as Worker Process
    participant PM as ProxyManager
    participant BM as BrowserManager
    participant GS as GoogleScraper
    participant P as SearchResultParser
    participant DB as Database
    
    Q->>W: Dequeue keyword job
    W->>PM: Get available proxy
    PM-->>W: Return proxy config
    W->>BM: Create browser context with proxy
    BM-->>W: Return page instance
    
    W->>GS: Execute search with stealth config
    GS->>GS: Navigate to Google with human simulation
    GS->>GS: Wait for results & handle captcha
    GS->>P: Parse HTML content
    
    P->>P: Count ads using multiple selectors
    P->>P: Count total links on page
    P->>P: Cache full HTML content
    P-->>GS: Return parsed data
    
    GS-->>W: Return search results
    W->>DB: Store results with metadata
    W->>Q: Update job status to completed
    
    alt On Error
        GS->>W: Throw error
        W->>Q: Update job status to failed
        W->>Q: Schedule retry with different proxy
    end
```

### 6. API Request Flow

```mermaid
sequenceDiagram
    participant C as Client (Next.js)
    participant MW as Auth Middleware
    participant API as NestJS Controller
    participant S as Service Layer
    participant DB as Drizzle ORM
    participant Q as Bull Queue
    
    C->>MW: POST /api/keywords/batch (with JWT)
    MW->>MW: Verify JWT token
    MW->>API: Forward request with user context
    
    API->>S: Call keywords service
    S->>S: Validate keywords array
    S->>DB: Create batch record
    S->>DB: Create keyword records
    
    loop For each keyword
        S->>Q: Enqueue scraping job
    end
    
    S-->>API: Return batch ID and status
    API-->>C: Return success response
    
    Note over C,Q: Async processing begins
    
    C->>API: GET /api/batches/:id (polling for updates)
    API->>S: Get batch status
    S->>DB: Query batch progress
    DB-->>S: Return progress data
    S-->>API: Return progress
    API-->>C: Return real-time status
```

### 7. Frontend CSV Processing & Validation

The CSV upload, validation, and parsing are handled entirely on the Next.js frontend before sending validated keywords array to the API.

#### CSV Processing Flow
```mermaid
sequenceDiagram
    participant U as User
    participant F as Upload Form
    participant V as CSV Validator
    participant P as CSV Parser
    participant API as Backend API
    
    U->>F: Select CSV file
    F->>V: Validate file format
    V->>V: Check file size, extension, MIME type
    V->>P: Parse CSV content
    P->>P: Extract keywords from CSV
    P->>P: Validate and sanitize keywords
    P->>F: Return validated keywords array
    F->>API: POST /api/keywords/batch with keywords[]
    API->>API: Create batch and enqueue jobs
    API->>F: Return batch confirmation
```

#### Frontend CSV Implementation

**CSV Processing Architecture:**
- **File Validation**: Size limits (5MB max), format validation (CSV only), MIME type checking
- **Parsing Engine**: Papa Parse library for robust CSV parsing with error handling
- **Data Validation**: Zod schemas for keyword validation (length, character restrictions)
- **Deduplication**: Remove duplicate keywords while preserving order
- **Limits Enforcement**: Maximum 100 keywords per batch
- **Error Handling**: Comprehensive error types for validation failures

**Processing Pipeline:**
1. File upload and initial validation
2. CSV content parsing with Papa Parse
3. Keyword extraction from first column
4. Individual keyword validation with Zod
5. Duplicate removal and final validation
6. Preview generation for user confirmation

#### Upload Component Design

**Component Features:**
- **File Selection**: Drag-and-drop or click to upload CSV files
- **Real-time Processing**: Immediate validation and parsing on file selection
- **Progress Indicators**: Loading states during CSV processing
- **Preview Display**: Shows parsed keywords, validation results, and statistics
- **Error Handling**: User-friendly error messages for validation failures
- **Upload Confirmation**: Final review before sending to backend

**User Experience Flow:**
1. User selects CSV file through file input
2. Immediate client-side processing begins
3. Progress indicator shows processing status
4. Preview shows parsed results with statistics
5. User reviews and confirms upload
6. Keywords array sent to backend API

### 8. Component Communication Patterns

#### Frontend State Management

**TanStack Query Patterns:**
- **Query Keys**: Hierarchical structure for cache invalidation (`['keywords'], ['keywordBatch', id]`)
- **Polling Strategy**: 5-second intervals for batch progress updates
- **Optimistic Updates**: Immediate UI updates before server confirmation
- **Cache Management**: Automatic invalidation and refetching on mutations
- **Background Refetch**: Continue polling when window loses focus

**State Synchronization:**
- Server state managed by TanStack Query
- Client state (UI, forms) managed by Zustand
- Form state managed by React Hook Form
- Real-time updates through polling (WebSocket in future phase)

#### Backend Service Architecture

**NestJS Service Patterns:**
- **Dependency Injection**: Constructor injection for database and queue services
- **Transaction Management**: Database transactions for atomic batch creation
- **Validation Layer**: Server-side validation of keywords array
- **Job Queue Integration**: Automatic job creation for each keyword
- **Error Handling**: Comprehensive error handling with custom exceptions
- **DTO Mapping**: Clean separation between database entities and API responses

**Service Workflow:**
1. Receive validated keywords array from frontend
2. Create batch record in database transaction
3. Create individual keyword records
4. Enqueue scraping jobs for each keyword
5. Return batch confirmation with tracking ID

### 9. Error Handling & Resilience Patterns

```mermaid
graph TB
    subgraph "Error Handling Layers"
        A[Client Error Boundary] --> B[API Error Middleware]
        B --> C[Service Error Handling]
        C --> D[Queue Error Handling]
        D --> E[Worker Error Handling]
    end
    
    subgraph "Recovery Strategies"
        F[Retry with Exponential Backoff] --> G[Circuit Breaker Pattern]
        G --> H[Graceful Degradation]
        H --> I[Dead Letter Queue]
        I --> J[Manual Review Process]
    end
    
    subgraph "Monitoring & Alerting"
        K[Winston Logging] --> L[Error Aggregation]
        L --> M[Metrics Collection]
        M --> N[Alert Thresholds]
        N --> O[Notification System]
    end
```

### 10. Performance Optimization Strategies

#### Database Optimization
- **Indexing Strategy**: Composite indexes on frequently queried columns
- **Connection Pooling**: Drizzle with connection pool configuration
- **Query Optimization**: Use of database views for complex aggregations
- **Pagination**: Cursor-based pagination for large result sets

#### Caching Strategy
- **Redis Caching**: Cache frequently accessed keyword batches and results
- **Browser Context Reuse**: Pool browser contexts to reduce initialization overhead
- **CDN Integration**: Static assets served through Vercel CDN
- **API Response Caching**: Cache stable API responses with appropriate TTL

#### Scaling Considerations
- **Horizontal Worker Scaling**: Auto-scaling based on queue depth
- **Database Read Replicas**: Separate read operations for better performance
- **Load Balancing**: Multiple API instances behind load balancer
- **Queue Partitioning**: Separate queues for different priority levels

This detailed architecture provides the technical foundation for implementing a robust, scalable search keyword scraper while maintaining the flexibility to adapt to changing requirements and scale as needed.