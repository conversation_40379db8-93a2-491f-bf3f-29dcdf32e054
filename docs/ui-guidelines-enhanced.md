# Enhanced UI Guidelines for Keyword Scraper Application

## Overview
This document establishes a comprehensive design system for the keyword scraping application, focusing on consistency, accessibility, and developer experience using Next.js 15, Tailwind CSS v4, and shadcn/ui.

## I. Design Philosophy & Setup

### Core Principles
- **Minimalist & Functional**: Clean, intuitive interfaces that prioritize user tasks
- **Accessibility First**: WCAG 2.1 AA compliance with proper focus management
- **Performance Optimized**: Fast loading with efficient component rendering
- **Developer Experience**: Consistent patterns and reusable components

### Technology Stack
- **Next.js 15+**: App Router, Server Components, Streaming
- **Tailwind CSS v4**: Latest version with improved performance
- **shadcn/ui**: High-quality accessible components
- **TypeScript**: Full type safety
- **SASS**: For complex styling needs

### Initial Setup Steps

1. **Install shadcn/ui**:
```bash
npx shadcn@latest init
```

2. **Install additional dependencies**:
```bash
npm install class-variance-authority clsx tailwind-merge lucide-react
npm install -D sass
```

3. **Font Configuration**: 
   - Keep Geist fonts (already optimized for your setup)
   - Add Inter as fallback for better compatibility

## II. Enhanced Color Palette & Theming

### Primary Color System
```css
:root {
  /* Primary - Indigo for actions and focus */
  --primary: 239 68% 68%;
  --primary-foreground: 0 0% 98%;
  
  /* Secondary - Slate for text and UI elements */
  --secondary: 210 40% 98%;
  --secondary-foreground: 222.2 84% 4.9%;
  
  /* Muted - For subtle backgrounds and disabled states */
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  
  /* Accent - For highlights and special elements */
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  
  /* Status Colors */
  --success: 142 76% 36%;
  --success-foreground: 355 100% 97%;
  
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 98%;
  
  /* Borders and backgrounds */
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 239 68% 68%;
  
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
}

.dark {
  --primary: 239 68% 68%;
  --primary-foreground: 0 0% 98%;
  
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 239 68% 68%;
  
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
}
```

## III. Typography System

### Font Configuration
```typescript
// app/layout.tsx
import { Geist, Geist_Mono, Inter } from 'next/font/google'

const geist = Geist({
  subsets: ['latin'],
  variable: '--font-geist',
})

const geistMono = Geist_Mono({
  subsets: ['latin'],
  variable: '--font-geist-mono',
})

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  fallback: ['system-ui', 'arial'],
})
```

### Typography Scale
| Element | Class | Size | Weight | Usage |
|---------|-------|------|--------|-------|
| Display | `text-4xl` | 2.25rem | 700 | Hero sections, main titles |
| H1 | `text-3xl` | 1.875rem | 600 | Page titles |
| H2 | `text-2xl` | 1.5rem | 600 | Section headers |
| H3 | `text-xl` | 1.25rem | 500 | Subsection headers |
| Body | `text-base` | 1rem | 400 | Primary content |
| Small | `text-sm` | 0.875rem | 400 | Secondary content |
| Caption | `text-xs` | 0.75rem | 400 | Helper text, labels |

## IV. Component Architecture

### Core shadcn/ui Components to Install
```bash
npx shadcn@latest add button card input label table badge tabs toast dialog sheet dropdown-menu select textarea checkbox radio-group switch slider progress alert-dialog
```

### Custom Component Extensions

#### 1. Enhanced Button Component
- Extends shadcn/ui Button with loading states
- Custom variants for keyword scraper context
- Proper accessibility attributes

#### 2. Data Display Components
- **KeywordTable**: Enhanced table with sorting, filtering, pagination
- **StatusBadge**: Consistent status indicators with animations
- **ProgressIndicator**: Real-time scraping progress
- **SearchResultsViewer**: Tabbed interface for HTML/summary views

#### 3. Form Components
- **FileUploadZone**: Drag-and-drop CSV upload with validation
- **SearchForm**: Keyword search with debounced input
- **FilterForm**: Advanced filtering options

#### 4. Layout Components
- **AppShell**: Main layout with sidebar and header
- **DashboardNav**: Navigation with active states
- **PageHeader**: Consistent page titles and actions

## V. Layout & Spacing System

### Container Sizes
```css
.container-sm { max-width: 640px; }
.container-md { max-width: 768px; }
.container-lg { max-width: 1024px; }
.container-xl { max-width: 1280px; }
.container-2xl { max-width: 1536px; }
```

### Spacing Scale
- Use Tailwind's default spacing scale (4px base unit)
- Standard page padding: `px-4 md:px-6 lg:px-8`
- Component spacing: `space-y-4` or `gap-4`
- Section spacing: `space-y-8` or `gap-8`

### Grid System
```css
/* Main layout grid */
.app-grid {
  display: grid;
  grid-template-columns: 240px 1fr;
  grid-template-rows: auto 1fr;
  min-height: 100vh;
}

/* Responsive sidebar */
@media (max-width: 768px) {
  .app-grid {
    grid-template-columns: 1fr;
  }
}
```

## VI. Animation & Transitions

### Standard Transitions
```css
.transition-standard { transition: all 150ms ease-in-out; }
.transition-fast { transition: all 100ms ease-in-out; }
.transition-slow { transition: all 300ms ease-in-out; }
```

### Loading States
- Skeleton loaders for data tables
- Spinner animations for buttons
- Progress bars for file uploads
- Pulse animations for real-time updates

## VII. Accessibility Guidelines

### Focus Management
- Visible focus indicators on all interactive elements
- Logical tab order throughout the application
- Skip links for keyboard navigation

### Color Contrast
- Minimum 4.5:1 contrast ratio for normal text
- Minimum 3:1 contrast ratio for large text
- Status colors meet accessibility requirements

### Screen Reader Support
- Proper ARIA labels and descriptions
- Semantic HTML structure
- Live regions for dynamic content updates

## VIII. Responsive Design

### Breakpoints
```css
sm: 640px   /* Mobile landscape */
md: 768px   /* Tablet */
lg: 1024px  /* Desktop */
xl: 1280px  /* Large desktop */
2xl: 1536px /* Extra large */
```

### Mobile-First Approach
- Design for mobile first, enhance for larger screens
- Touch-friendly button sizes (minimum 44px)
- Responsive typography and spacing
- Collapsible navigation for mobile

## IX. Performance Considerations

### Code Splitting
- Lazy load non-critical components
- Dynamic imports for heavy features
- Separate bundles for admin features

### Image Optimization
- Use Next.js Image component
- Proper alt text for accessibility
- Responsive image sizes

### CSS Optimization
- Purge unused Tailwind classes
- Critical CSS inlining
- Efficient component re-renders

## X. Development Workflow

### Component Development
1. Start with shadcn/ui base component
2. Extend with custom variants if needed
3. Add proper TypeScript types
4. Include accessibility attributes
5. Write component documentation
6. Add to Storybook (if implemented)

### Testing Strategy
- Unit tests for component logic
- Integration tests for user flows
- Accessibility testing with axe-core
- Visual regression testing

### Documentation
- Component API documentation
- Usage examples
- Design tokens reference
- Accessibility guidelines
