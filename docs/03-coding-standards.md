# Coding Standards & Best Practices

## General Principles

### 1. Code Quality Standards
- **Readability First**: Code should be self-documenting and easy to understand
- **Consistency**: Follow established patterns throughout the codebase
- **Type Safety**: Leverage TypeScript's type system to prevent runtime errors
- **Single Responsibility**: Each function, class, and module should have one clear purpose
- **DRY (Don't Repeat Yourself)**: Extract common logic into reusable utilities
- **SOLID Principles**: Apply object-oriented design principles where appropriate

### 2. TypeScript Standards

#### Type Definitions
```typescript
// ✅ Good: Explicit, descriptive types
interface KeywordBatchCreateRequest {
  filename: string;
  keywords: string[];
  userId: string;
}

type BatchStatus = 'pending' | 'processing' | 'completed' | 'failed';

// ❌ Bad: Any types or unclear interfaces
interface Request {
  data: any;
}
```

#### Strict TypeScript Configuration
```json
// tsconfig.json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true
  }
}
```

#### Union Types and Type Guards
```typescript
// ✅ Good: Proper type guards
function isSuccessResponse(response: ApiResponse): response is SuccessResponse {
  return response.status === 'success' && 'data' in response;
}

// ✅ Good: Discriminated unions
type JobResult = 
  | { status: 'success'; data: SearchResults }
  | { status: 'error'; error: string }
  | { status: 'retry'; retryCount: number };
```

### 3. Naming Conventions

#### Variables and Functions
```typescript
// ✅ Good: Descriptive camelCase
const keywordBatchProcessor = new KeywordBatchProcessor();
const processedKeywordCount = await countProcessedKeywords();

// ❌ Bad: Unclear or abbreviated names
const kbp = new KBP();
const cnt = await getCnt();
```

#### Constants
```typescript
// ✅ Good: SCREAMING_SNAKE_CASE for constants
const MAX_KEYWORDS_PER_BATCH = 100;
const SCRAPER_TIMEOUT_MS = 30000;
const DEFAULT_RETRY_ATTEMPTS = 3;
```

#### Types and Interfaces
```typescript
// ✅ Good: PascalCase with descriptive names
interface SearchResultMetrics {
  totalAds: number;
  totalLinks: number;
  processingTimeMs: number;
}

type ScraperConfiguration = {
  maxRetries: number;
  timeoutMs: number;
  proxies: ProxyConfig[];
};
```

#### Files and Directories
```bash
# ✅ Good: kebab-case for files, camelCase for directories
components/
  keyword-batch-uploader.tsx
  search-result-viewer.tsx
services/
  keywordProcessor/
    keyword-processor.service.ts
    keyword-processor.types.ts
```

## Frontend Standards (Next.js + React)

### 1. Component Structure

#### Functional Components with TypeScript
```typescript
// ✅ Good: Proper component structure
interface KeywordTableProps {
  keywords: Keyword[];
  onKeywordSelect: (keyword: Keyword) => void;
  loading?: boolean;
}

export function KeywordTable({ 
  keywords, 
  onKeywordSelect, 
  loading = false 
}: KeywordTableProps) {
  // Component logic here
  
  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="keyword-table">
      {keywords.map((keyword) => (
        <KeywordRow 
          key={keyword.id} 
          keyword={keyword} 
          onSelect={onKeywordSelect}
        />
      ))}
    </div>
  );
}
```

#### Custom Hooks
```typescript
// ✅ Good: Custom hooks for reusable logic
export function useKeywordBatch(batchId: string) {
  return useQuery({
    queryKey: ['keywordBatch', batchId],
    queryFn: () => fetchKeywordBatch(batchId),
    refetchInterval: 5000,
    enabled: !!batchId,
  });
}

export function useUploadKeywords() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  return useMutation({
    mutationFn: uploadKeywordsFile,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['keywordBatches'] });
      toast({ title: 'Upload successful', description: `${data.totalKeywords} keywords queued for processing` });
    },
    onError: (error) => {
      toast({ title: 'Upload failed', description: error.message, variant: 'destructive' });
    },
  });
}
```

### 2. State Management Patterns

#### TanStack Query for Server State
```typescript
// ✅ Good: Organized query definitions
export const keywordQueries = {
  all: () => ['keywords'] as const,
  batches: () => [...keywordQueries.all(), 'batches'] as const,
  batch: (id: string) => [...keywordQueries.batches(), id] as const,
  results: (keywordId: string) => [...keywordQueries.all(), 'results', keywordId] as const,
};

export function useKeywordBatches(userId: string) {
  return useQuery({
    queryKey: keywordQueries.batches(),
    queryFn: () => fetchKeywordBatches(userId),
    staleTime: 30000, // 30 seconds
  });
}
```

#### Zustand for Client State
```typescript
// ✅ Good: Typed Zustand store
interface AppState {
  sidebarOpen: boolean;
  currentView: 'upload' | 'keywords' | 'results';
  toggleSidebar: () => void;
  setCurrentView: (view: AppState['currentView']) => void;
}

export const useAppStore = create<AppState>((set) => ({
  sidebarOpen: false,
  currentView: 'upload',
  toggleSidebar: () => set((state) => ({ sidebarOpen: !state.sidebarOpen })),
  setCurrentView: (view) => set({ currentView: view }),
}));
```

### 3. Form Handling
```typescript
// ✅ Good: React Hook Form with Zod validation
const uploadSchema = z.object({
  file: z.custom<File>((val) => val instanceof File, 'Please select a file'),
  description: z.string().optional(),
});

type UploadFormData = z.infer<typeof uploadSchema>;

export function UploadForm() {
  const { register, handleSubmit, formState: { errors } } = useForm<UploadFormData>({
    resolver: zodResolver(uploadSchema),
  });

  const uploadMutation = useUploadKeywords();

  const onSubmit = handleSubmit((data) => {
    uploadMutation.mutate(data);
  });

  return (
    <form onSubmit={onSubmit}>
      <input
        type="file"
        accept=".csv"
        {...register('file')}
      />
      {errors.file && <span>{errors.file.message}</span>}
      
      <button type="submit" disabled={uploadMutation.isPending}>
        {uploadMutation.isPending ? 'Uploading...' : 'Upload'}
      </button>
    </form>
  );
}
```

## Backend Standards (NestJS)

### 1. Module Organization

#### Controller Structure
```typescript
// ✅ Good: Proper controller with validation and error handling
@Controller('keywords')
@UseGuards(JwtAuthGuard)
export class KeywordsController {
  constructor(private readonly keywordsService: KeywordsService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadKeywords(
    @UploadedFile() file: Express.Multer.File,
    @CurrentUser() user: User,
    @Body() uploadDto: UploadKeywordsDto,
  ): Promise<KeywordBatchResponseDto> {
    if (!file) {
      throw new BadRequestException('File is required');
    }

    try {
      const result = await this.keywordsService.uploadKeywords(user.id, file, uploadDto);
      return result;
    } catch (error) {
      if (error instanceof InvalidFileFormatError) {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException('Failed to process upload');
    }
  }

  @Get()
  async getKeywords(
    @CurrentUser() user: User,
    @Query() paginationDto: PaginationDto,
  ): Promise<PaginatedKeywordsResponseDto> {
    return this.keywordsService.getUserKeywords(user.id, paginationDto);
  }
}
```

#### Service Layer Pattern
```typescript
// ✅ Good: Service with proper error handling and logging
@Injectable()
export class KeywordsService {
  private readonly logger = new Logger(KeywordsService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly queueService: QueueService,
    private readonly csvParser: CsvParserService,
  ) {}

  async uploadKeywords(
    userId: string,
    file: Express.Multer.File,
    uploadDto: UploadKeywordsDto,
  ): Promise<KeywordBatchResponseDto> {
    this.logger.log(`Starting keyword upload for user ${userId}`);

    // Validate file format
    if (!this.isValidCsvFile(file)) {
      throw new InvalidFileFormatError('File must be a valid CSV');
    }

    const transaction = await this.drizzle.db.transaction(async (tx) => {
      // Parse keywords from CSV
      const keywords = await this.csvParser.parseKeywords(file);
      
      if (keywords.length === 0) {
        throw new InvalidFileFormatError('CSV file contains no valid keywords');
      }

      if (keywords.length > MAX_KEYWORDS_PER_BATCH) {
        throw new InvalidFileFormatError(`Too many keywords. Maximum allowed: ${MAX_KEYWORDS_PER_BATCH}`);
      }

      // Create batch record
      const [batch] = await tx.insert(keywordBatches).values({
        userId,
        filename: file.originalname,
        totalKeywords: keywords.length,
        originalSize: file.size,
      }).returning();

      // Create keyword records
      const keywordRecords = keywords.map(keyword => ({
        batchId: batch.id,
        userId,
        keyword: keyword.trim(),
      }));

      const insertedKeywords = await tx
        .insert(keywordsTable)
        .values(keywordRecords)
        .returning();

      return { batch, keywords: insertedKeywords };
    });

    // Enqueue processing jobs
    await this.enqueueKeywordJobs(transaction.keywords);

    this.logger.log(`Successfully created batch ${transaction.batch.id} with ${transaction.keywords.length} keywords`);

    return this.mapBatchToResponseDto(transaction.batch);
  }

  private async enqueueKeywordJobs(keywords: Keyword[]): Promise<void> {
    const jobs = keywords.map(keyword => ({
      name: 'process-keyword',
      data: {
        keywordId: keyword.id,
        keyword: keyword.keyword,
        userId: keyword.userId,
        batchId: keyword.batchId,
      },
      opts: {
        attempts: 3,
        backoff: 'exponential',
        delay: Math.floor(Math.random() * 10000), // Random delay to spread load
      },
    }));

    await this.queueService.addBulkJobs(jobs);
  }
}
```

### 2. Data Transfer Objects (DTOs)

#### Request DTOs
```typescript
// ✅ Good: Proper validation with class-validator
export class UploadKeywordsDto {
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @IsOptional()
  @IsEnum(SearchEngine)
  searchEngine?: SearchEngine;
}

export class PaginationDto {
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @IsString()
  search?: string;
}
```

#### Response DTOs
```typescript
// ✅ Good: Consistent response structure
export class KeywordBatchResponseDto {
  id: string;
  filename: string;
  totalKeywords: number;
  processedKeywords: number;
  successfulKeywords: number;
  failedKeywords: number;
  status: BatchStatus;
  createdAt: Date;
  updatedAt: Date;
}

export class PaginatedResponseDto<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

### 3. Error Handling

#### Custom Exceptions
```typescript
// ✅ Good: Domain-specific exceptions
export class InvalidFileFormatError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'InvalidFileFormatError';
  }
}

export class ScrapingError extends Error {
  constructor(
    message: string,
    public readonly keywordId: string,
    public readonly retryable: boolean = true,
  ) {
    super(message);
    this.name = 'ScrapingError';
  }
}
```

#### Global Error Filter
```typescript
// ✅ Good: Comprehensive error handling
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      message = exception.message;
    } else if (exception instanceof InvalidFileFormatError) {
      status = HttpStatus.BAD_REQUEST;
      message = exception.message;
    }

    const errorResponse = {
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
      message,
    };

    this.logger.error(
      `${request.method} ${request.url} - ${status} - ${message}`,
      exception instanceof Error ? exception.stack : exception,
    );

    response.status(status).json(errorResponse);
  }
}
```

## Worker Standards

### 1. Job Processing Pattern

```typescript
// ✅ Good: Robust job processor
@Processor('keyword-processing')
export class KeywordProcessor {
  private readonly logger = new Logger(KeywordProcessor.name);

  constructor(
    private readonly scraperService: ScraperService,
    private readonly drizzle: DrizzleService,
  ) {}

  @Process('process-keyword')
  async processKeyword(job: Job<KeywordJobData>): Promise<void> {
    const { keywordId, keyword, userId, batchId } = job.data;
    
    this.logger.log(`Processing keyword: ${keyword} (${keywordId})`);

    try {
      // Update status to processing
      await this.updateKeywordStatus(keywordId, 'processing', {
        processingStartedAt: new Date(),
      });

      // Perform scraping
      const startTime = Date.now();
      const results = await this.scraperService.scrapeKeyword(keyword);
      const processingTimeMs = Date.now() - startTime;

      // Store results
      await this.storeSearchResults(keywordId, results, processingTimeMs);

      // Update status to completed
      await this.updateKeywordStatus(keywordId, 'completed', {
        processingCompletedAt: new Date(),
      });

      // Update batch progress
      await this.updateBatchProgress(batchId);

      this.logger.log(`Successfully processed keyword: ${keyword}`);

    } catch (error) {
      this.logger.error(`Failed to process keyword: ${keyword}`, error);

      const isRetryable = this.isRetryableError(error);
      const retryCount = job.attemptsMade;

      if (isRetryable && retryCount < 3) {
        // Update retry information
        await this.updateKeywordStatus(keywordId, 'retrying', {
          retryCount,
          lastRetryAt: new Date(),
          errorMessage: error.message,
        });
        
        throw error; // This will trigger Bull's retry mechanism
      } else {
        // Mark as permanently failed
        await this.updateKeywordStatus(keywordId, 'failed', {
          retryCount,
          errorMessage: error.message,
        });

        await this.updateBatchProgress(batchId);
      }
    }
  }

  private isRetryableError(error: Error): boolean {
    // Network errors, timeouts, and rate limits are retryable
    return error.name === 'TimeoutError' || 
           error.name === 'NetworkError' ||
           error.message.includes('rate limit') ||
           error.message.includes('captcha');
  }

  private async updateKeywordStatus(
    keywordId: string, 
    status: KeywordStatus,
    updates: Partial<Keyword> = {},
  ): Promise<void> {
    await this.drizzle.db
      .update(keywordsTable)
      .set({ status, updatedAt: new Date(), ...updates })
      .where(eq(keywordsTable.id, keywordId));
  }
}
```

## Database Standards (Drizzle ORM)

### 1. Schema Definitions

```typescript
// ✅ Good: Well-structured schema with proper constraints
export const keywordBatches = pgTable('keyword_batches', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id')
    .references(() => users.id, { onDelete: 'cascade' })
    .notNull(),
  filename: varchar('filename', { length: 255 }).notNull(),
  totalKeywords: integer('total_keywords').notNull(),
  processedKeywords: integer('processed_keywords').default(0).notNull(),
  status: statusEnum('status').default('pending').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  userIdIdx: index('keyword_batches_user_id_idx').on(table.userId),
  statusIdx: index('keyword_batches_status_idx').on(table.status),
  createdAtIdx: index('keyword_batches_created_at_idx').on(table.createdAt),
}));
```

### 2. Query Patterns

```typescript
// ✅ Good: Efficient queries with proper filtering
export class KeywordRepository {
  constructor(private readonly db: DrizzleDatabase) {}

  async findUserKeywords(
    userId: string,
    pagination: PaginationOptions,
    filters: KeywordFilters = {},
  ): Promise<PaginatedResult<Keyword>> {
    const baseQuery = this.db
      .select()
      .from(keywordsTable)
      .where(eq(keywordsTable.userId, userId));

    // Apply filters
    const conditions = [eq(keywordsTable.userId, userId)];
    
    if (filters.status) {
      conditions.push(eq(keywordsTable.status, filters.status));
    }
    
    if (filters.search) {
      conditions.push(ilike(keywordsTable.keyword, `%${filters.search}%`));
    }
    
    if (filters.batchId) {
      conditions.push(eq(keywordsTable.batchId, filters.batchId));
    }

    const whereClause = and(...conditions);

    // Get total count
    const [{ count }] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(keywordsTable)
      .where(whereClause);

    // Get paginated results
    const keywords = await this.db
      .select()
      .from(keywordsTable)
      .where(whereClause)
      .orderBy(desc(keywordsTable.createdAt))
      .limit(pagination.limit)
      .offset((pagination.page - 1) * pagination.limit);

    return {
      data: keywords,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total: count,
        totalPages: Math.ceil(count / pagination.limit),
      },
    };
  }
}
```

## Testing Standards

### 1. Unit Tests

```typescript
// ✅ Good: Comprehensive unit test
describe('KeywordsService', () => {
  let service: KeywordsService;
  let mockDrizzle: MockedDrizzleService;
  let mockQueueService: MockedQueueService;

  beforeEach(() => {
    const module = Test.createTestingModule({
      providers: [
        KeywordsService,
        { provide: DrizzleService, useValue: mockDrizzle },
        { provide: QueueService, useValue: mockQueueService },
      ],
    }).compile();

    service = module.get<KeywordsService>(KeywordsService);
  });

  describe('uploadKeywords', () => {
    it('should successfully upload valid CSV file', async () => {
      // Arrange
      const userId = 'user-123';
      const mockFile: Express.Multer.File = {
        originalname: 'keywords.csv',
        buffer: Buffer.from('keyword1\nkeyword2\nkeyword3'),
        size: 100,
        mimetype: 'text/csv',
      } as Express.Multer.File;

      mockDrizzle.db.transaction.mockImplementation(async (callback) => {
        return callback(mockDrizzle.db);
      });

      // Act
      const result = await service.uploadKeywords(userId, mockFile, {});

      // Assert
      expect(result).toMatchObject({
        filename: 'keywords.csv',
        totalKeywords: 3,
        status: 'pending',
      });
      expect(mockQueueService.addBulkJobs).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            data: expect.objectContaining({
              keyword: 'keyword1',
            }),
          }),
        ]),
      );
    });

    it('should reject file with too many keywords', async () => {
      // Arrange
      const userId = 'user-123';
      const keywords = Array.from({ length: 101 }, (_, i) => `keyword${i}`);
      const mockFile = createMockFile(keywords.join('\n'));

      // Act & Assert
      await expect(
        service.uploadKeywords(userId, mockFile, {}),
      ).rejects.toThrow(InvalidFileFormatError);
    });
  });
});
```

### 2. Integration Tests

```typescript
// ✅ Good: Integration test with real database
describe('Keywords API (e2e)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Create test user and get auth token
    authToken = await createTestUserAndGetToken(app);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /keywords/upload', () => {
    it('should upload CSV file and create keywords', async () => {
      const csvContent = 'keyword1\nkeyword2\nkeyword3';
      
      const response = await request(app.getHttpServer())
        .post('/keywords/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', Buffer.from(csvContent), 'test.csv')
        .expect(201);

      expect(response.body).toMatchObject({
        filename: 'test.csv',
        totalKeywords: 3,
        status: 'pending',
      });

      // Verify keywords were created in database
      const keywords = await findKeywordsByBatchId(response.body.id);
      expect(keywords).toHaveLength(3);
    });
  });
});
```

## Code Review Guidelines

### 1. Review Checklist

#### Security
- [ ] No hardcoded secrets or API keys
- [ ] Proper input validation and sanitization
- [ ] Authentication and authorization checks
- [ ] SQL injection prevention
- [ ] XSS prevention

#### Performance
- [ ] Efficient database queries
- [ ] Proper caching strategies
- [ ] No memory leaks
- [ ] Appropriate use of async/await
- [ ] Bundle size considerations

#### Maintainability
- [ ] Clear and descriptive naming
- [ ] Proper error handling
- [ ] Adequate test coverage
- [ ] Documentation for complex logic
- [ ] Consistent code style

#### Architecture
- [ ] Follows established patterns
- [ ] Proper separation of concerns
- [ ] Scalable design decisions
- [ ] Backward compatibility

### 2. Review Process

1. **Automated Checks**: Ensure all CI checks pass (linting, tests, build)
2. **Code Review**: At least one team member reviews the code
3. **Testing**: Manual testing of new features
4. **Documentation**: Update relevant documentation
5. **Deployment**: Deploy to staging before production

## Documentation Standards

### 1. Code Comments

```typescript
// ✅ Good: Meaningful comments explaining why, not what
/**
 * Implements exponential backoff retry logic for scraping operations.
 * This is necessary because search engines implement sophisticated
 * rate limiting that requires intelligent retry strategies.
 */
export class RetryStrategy {
  /**
   * Calculates the delay before the next retry attempt.
   * Uses exponential backoff with jitter to prevent thundering herd.
   */
  calculateDelay(attempt: number): number {
    const baseDelay = Math.pow(2, attempt) * 1000;
    const jitter = Math.random() * 1000;
    return Math.min(baseDelay + jitter, 30000);
  }
}
```

### 2. API Documentation

```typescript
// ✅ Good: Comprehensive API documentation
/**
 * @swagger
 * /keywords/upload:
 *   post:
 *     summary: Upload CSV file containing keywords for processing
 *     description: |
 *       Accepts a CSV file with keywords (one per line) and creates
 *       a batch for processing. Maximum 100 keywords per batch.
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: CSV file containing keywords
 *     responses:
 *       201:
 *         description: Batch created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/KeywordBatch'
 */
@Post('upload')
async uploadKeywords() {
  // Implementation
}
```

These coding standards ensure consistency, maintainability, and quality across the entire codebase while following modern TypeScript and web development best practices.