# Deployment Strategy

## Overview

The Search Keywords Scraper uses a modern cloud-native deployment strategy leveraging managed services for maximum reliability, scalability, and cost-effectiveness. The deployment is designed for easy CI/CD integration and supports both staging and production environments.

## Infrastructure Overview

```mermaid
graph TB
    subgraph "Frontend Deployment"
        VERCEL[Vercel Platform] --> CDN[Global CDN]
        CDN --> NEXT[Next.js Application]
    end
    
    subgraph "Backend Services"
        DO[DigitalOcean App Platform] --> API[NestJS API]
        DO --> WORKER[Worker Processes]
    end
    
    subgraph "Managed Services"
        SUPABASE[Supabase] --> DB[(PostgreSQL)]
        SUPABASE --> AUTH[Authentication]
        UPSTASH[Upstash] --> REDIS[(Redis Queue)]
    end
    
    subgraph "External Services"
        PROXY[Proxy Providers]
        MONITORING[Monitoring & Alerts]
    end
    
    NEXT --> API
    API --> DB
    API --> REDIS
    WORKER --> REDIS
    WORKER --> DB
    WORKER --> PROXY
    API --> MONITORING
    WORKER --> MONITORING
```

## Environment Strategy

### 1. Development Environment

**Local Development Setup:**
```bash
# Clone repository
git clone https://github.com/username/search-keywords-scraper.git
cd search-keywords-scraper

# Install dependencies
pnpm install

# Set up environment variables
cp .env.example .env.local

# Start development servers
pnpm dev
```

**Local Services:**
- **Database**: Local PostgreSQL or Supabase development instance
- **Redis**: Local Redis server or Upstash development instance
- **Proxy**: Mock proxy service for testing

**Development URLs:**
- Frontend: `http://localhost:3000`
- API: `http://localhost:3001`
- Worker: Background process

### 2. Staging Environment

**Purpose**: Pre-production testing and client demonstrations

**Infrastructure:**
- **Frontend**: Vercel preview deployment
- **Backend**: DigitalOcean App Platform staging
- **Database**: Supabase staging project
- **Redis**: Upstash staging instance

**Domain:**
- Frontend: `https://staging.search-scraper.com`
- API: `https://api-staging.search-scraper.com`

### 3. Production Environment

**Infrastructure:**
- **Frontend**: Vercel production deployment
- **Backend**: DigitalOcean App Platform production with auto-scaling
- **Database**: Supabase production
- **Redis**: Upstash production

**Domain:**
- Frontend: `https://search-scraper.com`
- API: `https://api.search-scraper.com`

## Deployment Platforms

### 1. Frontend Deployment (Vercel)

#### Configuration (vercel.json)
```json
{
  "framework": "nextjs",
  "buildCommand": "pnpm build",
  "outputDirectory": ".next",
  "installCommand": "pnpm install",
  "envPrefix": "NEXT_PUBLIC_",
  "regions": ["iad1", "sfo1", "fra1"],
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "*"
        }
      ]
    }
  ],
  "rewrites": [
    {
      "source": "/api/v1/:path*",
      "destination": "https://api.search-scraper.com/api/:path*"
    }
  ]
}
```

#### Environment Variables
```bash
# Authentication (handled by Supabase)
# No additional environment variables needed for NextAuth.js

# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# API Configuration
NEXT_PUBLIC_API_URL=https://api.search-scraper.com

# Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
```

#### Deployment Features
- **Automatic deployments** from main branch
- **Preview deployments** for pull requests
- **Global CDN** with edge caching
- **Custom domains** with SSL certificates
- **Environment-specific builds**

### 2. Backend API Deployment (DigitalOcean App Platform)

#### Configuration (.do/app.yaml)
```yaml
name: keywords-scraper-api
services:
- name: api
  source_dir: /
  github:
    repo: username/search-keywords-scraper
    branch: main
  run_command: node dist/main.js
  build_command: pnpm build
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  http_port: 3001
  envs:
  - key: NODE_ENV
    value: production
```

#### Environment Variables
```bash
# Database
DATABASE_URL=postgresql://user:pass@host:port/db

# Redis
REDIS_URL=redis://default:pass@host:port

# Authentication
JWT_SECRET=production-jwt-secret

# External Services
PROXY_PROVIDER_API_KEY=your-proxy-key

# Monitoring
SENTRY_DSN=https://your-sentry-dsn

# Application
PORT=3001
NODE_ENV=production
LOG_LEVEL=info
```

#### Auto-Scaling Configuration
```yaml
# Auto-scaling configuration in .do/app.yaml
services:
- name: api
  instance_count: 1
  instance_size_slug: basic-xxs
  autoscaling:
    min_instance_count: 1
    max_instance_count: 3
    metrics:
      cpu:
        percent: 80
```

### 3. Worker Deployment (DigitalOcean App Platform)

#### Separate Worker Service
```yaml
# Worker service in .do/app.yaml
workers:
- name: scraper-worker
  source_dir: /
  run_command: node dist/worker/index.js
  build_command: pnpm build:worker
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xs
  autoscaling:
    min_instance_count: 1
    max_instance_count: 3
    metrics:
      cpu:
        percent: 75
```

#### Worker-Specific Environment
```bash
# Queue Configuration
REDIS_URL=redis://default:pass@host:port
QUEUE_CONCURRENCY=3
MAX_JOB_ATTEMPTS=3

# Scraping Configuration
MAX_BROWSER_CONTEXTS=5
SCRAPING_TIMEOUT=30000
PROXY_ROTATION_ENABLED=true

# Performance
WORKER_MEMORY_LIMIT=2048
WORKER_CPU_LIMIT=1000
```

## Database Management (Supabase)

### 1. Database Setup

#### Project Configuration
```sql
-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE keyword_batches ENABLE ROW LEVEL SECURITY;
ALTER TABLE keywords ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_results ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can only access their own data" ON keyword_batches
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own keywords" ON keywords
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own results" ON search_results
    FOR ALL USING (
        keyword_id IN (
            SELECT id FROM keywords WHERE user_id = auth.uid()
        )
    );
```

#### Migration Strategy
```typescript
// packages/database/migrations/deploy.ts
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import { db } from '../src/connection';

async function runMigrations() {
  console.log('Running migrations...');
  await migrate(db, { migrationsFolder: './migrations' });
  console.log('Migrations completed!');
}

if (require.main === module) {
  runMigrations().catch(console.error);
}
```

### 2. Backup Strategy

#### Automated Backups
- **Daily backups** with 30-day retention
- **Weekly backups** with 3-month retention
- **Monthly backups** with 1-year retention

#### Point-in-Time Recovery
- **7-day PITR** for production database
- **1-day PITR** for staging database

## CI/CD Pipeline

### 1. GitHub Actions Workflow

#### Main Deployment Pipeline (.github/workflows/deploy.yml)
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
  TURBO_TEAM: ${{ secrets.TURBO_TEAM }}

jobs:
  changes:
    runs-on: ubuntu-latest
    outputs:
      web: ${{ steps.changes.outputs.web }}
      api: ${{ steps.changes.outputs.api }}
      worker: ${{ steps.changes.outputs.worker }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            web:
              - 'apps/web/**'
              - 'packages/**'
            api:
              - 'apps/api/**'
              - 'packages/**'
            worker:
              - 'apps/worker/**'
              - 'packages/**'

  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v2
        with:
          version: 8.6.10
      - uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Run tests
        run: pnpm test
      
      - name: Run type checking
        run: pnpm type-check
      
      - name: Run linting
        run: pnpm lint

  deploy-web:
    needs: [changes, test]
    if: needs.changes.outputs.web == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./apps/web
          vercel-args: ${{ github.ref == 'refs/heads/main' && '--prod' || '' }}

  deploy-api:
    needs: [changes, test]
    if: needs.changes.outputs.api == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: digitalocean/app_action@v1.1.5
        with:
          app_name: keywords-scraper-api
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

  deploy-worker:
    needs: [changes, test]
    if: needs.changes.outputs.worker == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: digitalocean/app_action@v1.1.5
        with:
          app_name: keywords-scraper-worker
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

  notify:
    needs: [deploy-web, deploy-api, deploy-worker]
    if: always()
    runs-on: ubuntu-latest
    steps:
      - name: Notify deployment status
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

### 2. Preview Deployments

#### Pull Request Pipeline
```yaml
name: Preview Deployment

on:
  pull_request:
    types: [opened, synchronize]

jobs:
  deploy-preview:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Deploy to Vercel Preview
        uses: amondnet/vercel-action@v25
        id: vercel-deploy
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./apps/web
      
      - name: Comment Preview URL
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## 🚀 Preview Deployment
              
              Your changes have been deployed to: ${{ steps.vercel-deploy.outputs.preview-url }}
              
              ### What's included:
              - ✅ Frontend application
              - ✅ API integration (staging)
              - ✅ Database (staging)
              
              This preview will be updated automatically with new commits.`
            });
```

## Monitoring & Observability

### 1. Application Monitoring

#### Performance Monitoring
```typescript
// apps/api/src/monitoring/metrics.ts
import { createPrometheusMetrics } from '@prometheus/client';

export const metrics = {
  httpRequestDuration: new Histogram({
    name: 'http_request_duration_seconds',
    help: 'HTTP request duration in seconds',
    labelNames: ['method', 'route', 'status'],
  }),
  
  scrapingJobDuration: new Histogram({
    name: 'scraping_job_duration_seconds',
    help: 'Scraping job duration in seconds',
    labelNames: ['keyword', 'status'],
  }),
  
  activeWorkers: new Gauge({
    name: 'active_workers_total',
    help: 'Number of active worker processes',
  }),
  
  queueSize: new Gauge({
    name: 'queue_size_total',
    help: 'Number of jobs in queue',
    labelNames: ['queue', 'status'],
  }),
};
```

#### Health Check Endpoints
```typescript
// apps/api/src/health/health.controller.ts
@Controller('health')
export class HealthController {
  @Get()
  async check(): Promise<HealthCheckResult> {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version,
      checks: {
        database: await this.checkDatabase(),
        redis: await this.checkRedis(),
        external: await this.checkExternalServices(),
      },
    };
  }
  
  @Get('ready')
  async ready(): Promise<{ ready: boolean }> {
    // Kubernetes readiness probe
    const dbReady = await this.checkDatabase();
    const redisReady = await this.checkRedis();
    
    return {
      ready: dbReady && redisReady,
    };
  }
}
```

### 2. Error Tracking (Sentry)

#### Configuration
```typescript
// apps/api/src/main.ts
import * as Sentry from '@sentry/node';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 0.1,
  integrations: [
    new Sentry.Integrations.Http({ tracing: true }),
    new Sentry.Integrations.Postgres(),
  ],
});
```

### 3. Logging Strategy

#### Structured Logging
```typescript
// packages/utils/src/logger.ts
import winston from 'winston';

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: {
    service: process.env.SERVICE_NAME,
    version: process.env.npm_package_version,
    environment: process.env.NODE_ENV,
  },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
  ],
});
```

## Security Configuration

### 1. Environment Security

#### Secret Management
```bash
# Production secrets stored in platform-specific secret managers
# Vercel: Project settings -> Environment Variables
# DigitalOcean: App settings -> Environment Variables
# Supabase: Project settings -> API keys

# Development secrets in .env.local (gitignored)
# Never commit secrets to version control
```

#### CORS Configuration
```typescript
// apps/api/src/main.ts
app.enableCors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://search-scraper.com']
    : ['http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
});
```

### 2. Infrastructure Security

#### Network Security
- **HTTPS enforcement** on all endpoints
- **API rate limiting** to prevent abuse
- **DDoS protection** via Cloudflare (optional)
- **WAF rules** for common attack patterns

#### Data Protection
- **Row-level security** in Supabase
- **Encrypted database** connections
- **Sensitive data encryption** at rest
- **PII data handling** compliance

## Scaling Strategy

### 1. Horizontal Scaling

#### Auto-scaling Configuration
```yaml
# DigitalOcean App Platform auto-scaling
services:
- name: api
  autoscaling:
    min_instance_count: 1
    max_instance_count: 5
    metrics:
      cpu:
        percent: 70
```

#### Load Balancing
```typescript
// apps/api/src/cluster.ts
import cluster from 'cluster';
import os from 'os';

if (cluster.isPrimary && process.env.NODE_ENV === 'production') {
  const numCPUs = os.cpus().length;
  
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }
  
  cluster.on('exit', (worker) => {
    console.log(`Worker ${worker.process.pid} died`);
    cluster.fork();
  });
} else {
  // Start the application
  require('./main');
}
```

### 2. Database Scaling

#### Read Replicas
```typescript
// packages/database/src/connection.ts
export const dbConfig = {
  primary: {
    host: process.env.DATABASE_HOST,
    // Write operations
  },
  replica: {
    host: process.env.DATABASE_REPLICA_HOST,
    // Read operations
  },
};
```

#### Connection Pooling
```typescript
// packages/database/src/pool.ts
import { Pool } from 'pg';

export const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false },
  max: 20, // Maximum pool size
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});
```

## Cost Optimization

### 1. Resource Optimization

#### Efficient Resource Usage
- **Serverless functions** for low-traffic endpoints
- **Auto-scaling workers** based on queue depth
- **Database connection pooling** to reduce overhead
- **CDN caching** for static assets

#### Cost Monitoring
```typescript
// Monitor resource usage and costs
export const costOptimization = {
  // Use smaller instances for development
  development: {
    api: 'shared-cpu-1x',
    worker: 'shared-cpu-1x',
  },
  
  // Scale up for production
  production: {
    api: 'shared-cpu-2x',
    worker: 'shared-cpu-4x',
  },
};
```

### 2. Platform Costs

#### Estimated Monthly Costs
```
Production Environment:
- Vercel Pro: $20/month (includes CDN, SSL)
- DigitalOcean App Platform: $12-48/month (depending on usage)
- Supabase Pro: $25/month (includes auth, database)
- Upstash: $10-30/month (Redis usage-based)
- Total: ~$67-123/month

Staging Environment:
- Vercel: Free tier
- DigitalOcean: $5-12/month
- Supabase: Free tier
- Upstash: Free tier
- Total: ~$5-12/month
```

This deployment strategy provides a robust, scalable, and cost-effective foundation for the Search Keywords Scraper platform, ensuring reliable operation while maintaining development velocity and operational excellence.