# Docker Environment Variables
# Copy this file to .env and fill in your actual values

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_JWT_SECRET=your-supabase-jwt-secret

# Production Database & Redis (for docker-compose.prod.yml)
DATABASE_URL=********************************************/scraper_prod
REDIS_URL=redis://your-redis-host:6379

# API Configuration
JWT_SECRET=your-secure-jwt-secret-key-min-32-chars
NEXT_PUBLIC_API_URL=https://your-api-domain.com

# Worker Configuration
WORKER_REPLICAS=2

# Optional: Proxy Configuration for Workers
PROXY_LIST=http://proxy1:port,http://proxy2:port