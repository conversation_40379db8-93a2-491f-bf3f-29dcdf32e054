# Search Keywords Scraper

A sophisticated web application that extracts large amounts of data from Google and Bing search results pages while working around anti-scraping limitations. Built with modern technologies and designed for scalability, reliability, and user experience.

## Key Features

- **Intelligent Web Scraping**: Advanced anti-detection measures with proxy rotation and stealth browser configurations
- **CSV Batch Processing**: Upload and process 1-100 keywords per batch with real-time progress tracking
- **Distributed Architecture**: Scalable queue-based processing with automatic retry mechanisms
- **Real-time Updates**: Live progress monitoring and instant result availability
- **Secure Authentication**: User isolation with JWT-based authentication and row-level security
- **Comprehensive Analytics**: Extract ad counts, link counts, and HTML cache for each keyword

## Architecture Overview

This is a **Turborepo monorepo** built with modern technologies:

- **Frontend**: Next.js 15+ with App Router, TypeScript, Tailwind CSS, shadcn/ui
- **Backend API**: NestJS with Drizzle ORM, Bull Queue, JWT authentication
- **Workers**: Node.js TypeScript workers with Playwright browser automation
- **Database**: PostgreSQL (Supabase) with Redis queue management
- **Infrastructure**: Docker-ready with cloud deployment configurations

```mermaid
graph TB
    subgraph "Client Layer"
        U[Users] --> WEB[Next.js Web App]
    end
    
    subgraph "API Layer"
        WEB --> API[NestJS API Server]
    end
    
    subgraph "Processing Layer"
        API --> QUEUE[Bull Queue + Redis]
        QUEUE --> WORKERS[Worker Processes]
    end
    
    subgraph "Data Layer"
        API --> DB[(PostgreSQL)]
        WORKERS --> DB
    end
    
    subgraph "External Services"
        WORKERS --> GOOGLE[Google Search]
        WORKERS --> BING[Bing Search]
    end
```

## Project Structure

```
search-keywords-scraper/
 apps/
   web/          # Next.js frontend application
   api/          # NestJS backend API
   worker/       # Node.js worker processes
 packages/
   types/        # Shared TypeScript types
   config/       # Shared configuration
   database/     # Drizzle ORM schema
   utils/        # Shared utilities
 docs/             # Comprehensive documentation
 tools/            # Development tools and scripts
```

## 🚀 Quick Start

### Prerequisites

Ensure you have the following installed on your development machine:

- **Node.js 18+** - [Download from nodejs.org](https://nodejs.org/)
- **pnpm 8+** - Fast, disk space efficient package manager
  ```bash
  npm install -g pnpm
  ```
- **Git** - Version control system

### External Services Required

- **PostgreSQL Database** - We recommend [Supabase](https://supabase.io) for managed PostgreSQL
- **Redis Instance** - We recommend [Upstash](https://upstash.com) for managed Redis

### Development Setup

1. **Clone the Repository**
   ```bash
   git clone https://github.com/tuandinh0801/search-keywords-scraper.git
   cd search-keywords-scraper
   ```

2. **Install Dependencies**
   ```bash
   pnpm install
   ```

3. **Environment Configuration**
   
   Copy environment files and configure for each app:
   ```bash
   # API Environment
   cp apps/api/.env.example apps/api/.env
   
   # Web Environment  
   cp apps/web/.env.example apps/web/.env.local
   
   # Worker Environment
   cp apps/worker/.env.example apps/worker/.env
   ```

   **Required Environment Variables:**
   
   For `apps/api/.env`:
   ```env
   # Database
   DATABASE_URL="postgresql://user:password@localhost:5432/scraper_db"
   
   # Redis Queue
   REDIS_URL="redis://localhost:6379"
   
   # Supabase Auth
   SUPABASE_URL="https://your-project.supabase.co"
   SUPABASE_ANON_KEY="your-anon-key"
   SUPABASE_JWT_SECRET="your-jwt-secret"
   
   # API Configuration
   PORT=3001
   JWT_SECRET="your-jwt-secret-key"
   ```

   For `apps/web/.env.local`:
   ```env
   # API Endpoints
   NEXT_PUBLIC_API_URL="http://localhost:3001"
   
   # Supabase Auth
   NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co"
   NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"
   ```

4. **Database Setup**
   ```bash
   # Run database migrations
   pnpm db:migrate
   
   # Optional: Seed with test data
   pnpm db:seed
   ```

5. **Start Development Servers**
   ```bash
   # Start all applications (web, api, worker)
   pnpm dev
   ```

   Or start individually:
   ```bash
   # Frontend only (http://localhost:3000)
   cd apps/web && pnpm dev
   
   # API only (http://localhost:3001)
   cd apps/api && pnpm dev
   
   # Worker processes only
   cd apps/worker && pnpm dev
   ```

### 🧪 Testing Setup

Run the comprehensive test suite:

```bash
# Run all tests across packages
pnpm test

# Run tests for specific packages
cd apps/api && pnpm test        # API unit tests
cd apps/worker && pnpm test     # Worker unit tests
cd packages/scraper && pnpm test # Scraper unit tests

# Run tests with coverage
pnpm test:coverage
```

### Available Scripts

```bash
# 🚀 Development
pnpm dev              # Start all apps in development mode
pnpm build            # Build all applications for production
pnpm start            # Start production build
pnpm clean            # Clean all build outputs

# 🧪 Testing & Quality
pnpm test             # Run all test suites
pnpm test:watch       # Run tests in watch mode
pnpm test:coverage    # Run tests with coverage report
pnpm type-check       # TypeScript validation across all packages
pnpm lint             # Code linting with Biome
pnpm format           # Code formatting

# 🗄️ Database
pnpm db:migrate       # Run database migrations
pnpm db:seed          # Seed database with test data
pnpm db:studio        # Open database studio (if available)
pnpm db:reset         # Reset database (caution: deletes all data)

# 📦 Package Management
pnpm install          # Install all dependencies
pnpm update           # Update all dependencies
pnpm outdated         # Check for outdated packages
```

### 🐳 Docker Development (Alternative)

For containerized development with PostgreSQL and Redis included:

```bash
# Quick start
cp .env.docker .env  # Edit with your credentials
docker-compose up -d

# Access services
# Web: http://localhost:3000
# API: http://localhost:3001

# Helper script for common operations
./scripts/docker-dev.sh start
./scripts/docker-dev.sh logs
./scripts/docker-dev.sh stop
```

## 🔧 Troubleshooting

### Common Development Issues

**Port Already in Use**
```bash
# Kill processes on specific ports
npx kill-port 3000 3001

# Or find and kill manually
lsof -ti:3000 | xargs kill
```

**Database Connection Issues**
```bash
# Check database connection
psql $DATABASE_URL -c "SELECT 1"

# Reset database schema
pnpm db:reset
pnpm db:migrate
```

**Redis Connection Issues**
```bash
# Test Redis connection
redis-cli -u $REDIS_URL ping

# Clear Redis cache if needed
redis-cli -u $REDIS_URL flushall
```

**Module Resolution Errors**
```bash
# Clear all node_modules and reinstall
pnpm clean
rm -rf node_modules */*/node_modules
pnpm install

# Build shared packages
pnpm build
```

**Test Failures**
```bash
# Run tests with verbose output
pnpm test --verbose

# Clear Jest cache
pnpm test --clearCache
```

**Docker Issues**
```bash
# Container won't start
docker-compose logs service-name

# Database connection failed
docker-compose exec api pnpm db:migrate
docker-compose restart api

# Port conflicts with Docker
docker-compose down
# Edit docker-compose.yml ports if needed
docker-compose up -d

# Reset Docker environment
docker-compose down -v
docker-compose build --no-cache
docker-compose up -d

# Check container health
docker-compose ps
docker-compose exec api curl localhost:3001/health
```

### Performance Issues

- **Slow scraping**: Check proxy health and adjust retry delays in worker configuration
- **High memory usage**: Monitor browser context cleanup in worker processes  
- **Queue backup**: Scale worker processes or optimize job processing logic
- **Docker performance**: Increase Docker memory limits or optimize container resource allocation

### Getting Help

1. Check [GitHub Issues](https://github.com/tuandinh0801/search-keywords-scraper/issues) for similar problems
2. Review [Documentation](docs/) for detailed guides
3. Join our community discussions for support

## 📚 Documentation

Our comprehensive documentation is organized for easy navigation:

### 🏗️ Architecture & Design
- **[High-Level Architecture](docs/01-high-level-architecture.md)** - System overview, components, and design decisions
- **[Detailed Architecture](docs/02-detailed-architecture.md)** - Technology stack deep dive and component interactions
- **[Project Structure](docs/04-project-structure.md)** - Monorepo organization and file structure

### 👨‍💻 Development Guidelines  
- **[Coding Standards](docs/03-coding-standards.md)** - TypeScript standards, naming conventions, and best practices
- **[Testing Strategy](docs/08-testing-strategy.md)** - Comprehensive testing approach with priority-based coverage

### 📋 Technical Specifications
- **[API Specifications](docs/05-api-specifications.md)** - REST API endpoints, request/response schemas
- **[Database Schema](docs/07-database-schema.md)** - Data models, relationships, and migration strategy

### 🚀 Operations & Deployment
- **[Deployment Strategy](docs/06-deployment-strategy.md)** - Production deployment, scaling, and monitoring

## 🗺️ Feature Roadmap

Our development roadmap is organized by priority and tracked through [GitHub Issues](https://github.com/tuandinh0801/search-keywords-scraper/issues) and [Project Milestones](https://github.com/tuandinh0801/search-keywords-scraper/milestones).

### ✅ Phase 1: Core Foundation (COMPLETED)
**Milestone: [v1.0 - Core Foundation](https://github.com/tuandinh0801/search-keywords-scraper/milestone/1)**

Essential features that define the application's core value proposition.

#### 🔐 User Authentication & Security
- [x] Supabase Auth integration with JWT tokens ([#1](https://github.com/tuandinh0801/search-keywords-scraper/issues/1))
- [x] Protected API endpoints and route guards
- [x] Session management and token refresh

#### 📄 CSV Upload & Processing
- [x] File upload with validation (1-100 keywords) ([#3](https://github.com/tuandinh0801/search-keywords-scraper/issues/3))
- [x] CSV parsing with error handling
- [x] Batch creation and keyword management
- [x] Input sanitization and security

#### 🤖 Web Scraping Engine
- [x] Playwright browser automation with stealth configuration ([#4](https://github.com/tuandinh0801/search-keywords-scraper/issues/4))
- [x] Proxy rotation and health management ([#5](https://github.com/tuandinh0801/search-keywords-scraper/issues/5))
- [x] Anti-detection measures (fingerprinting, behavior simulation)
- [x] Robust retry logic with exponential backoff ([#7](https://github.com/tuandinh0801/search-keywords-scraper/issues/7))

#### ⚙️ Queue-Based Job Processing
- [x] Bull Queue with Redis backend ([#6](https://github.com/tuandinh0801/search-keywords-scraper/issues/6))
- [x] Distributed worker processes
- [x] Job failure handling and retry mechanisms
- [x] Progress tracking and status updates

#### 🧪 Testing Infrastructure
- [x] Comprehensive unit testing with 28+ tests passing
- [x] Test-utils package with factories and mocks
- [x] Jest configuration for monorepo workspace resolution

### 🟡 Priority 2: Enhanced User Experience (High)
**Milestone: [v2.0 - Enhanced UX](https://github.com/tuandinh0801/search-keywords-scraper/milestone/2)**

Features that significantly improve usability and provide competitive advantages.

#### 📊 Real-time Progress Monitoring
- [ ] Live batch processing status ([#8](https://github.com/tuandinh0801/search-keywords-scraper/issues/8))
- [ ] Individual keyword progress tracking
- [ ] Error reporting and retry status
- [ ] WebSocket-based real-time updates (upgrade from polling)

#### 🔍 Search Results Analytics
- [x] Google/Bing ads count extraction ([#9](https://github.com/tuandinh0801/search-keywords-scraper/issues/9))
- [x] Total links count analysis
- [x] HTML page caching
- [ ] Advanced metrics (SERP position tracking, competitor analysis)
- [ ] Result comparison and trending

#### 🔎 Advanced Search & Filtering
- [ ] Basic keyword search and filtering ([#10](https://github.com/tuandinh0801/search-keywords-scraper/issues/10))
- [ ] Advanced filtering by status, date range, metrics
- [ ] Batch comparison and analytics
- [ ] Export functionality (CSV, PDF reports)

### 🟢 Priority 3: Platform Enhancement (Medium)
**Milestone: [v3.0 - Platform Enhancement](https://github.com/tuandinh0801/search-keywords-scraper/milestone/3)**

Features that add significant value and differentiation.

#### 📈 Analytics Dashboard
- [ ] Processing success rate metrics ([#11](https://github.com/tuandinh0801/search-keywords-scraper/issues/11))
- [ ] Performance analytics and trends
- [ ] Resource usage monitoring
- [ ] Custom reporting and insights

#### 🚀 Advanced Scraping Features
- [ ] Multi-search engine support (expand beyond Google/Bing)
- [ ] Geographic location targeting
- [ ] Device-specific scraping (mobile vs desktop results)
- [ ] Historical data tracking and comparison

#### 🎨 UI/UX Improvements
- [ ] Dark/light theme toggle ([#12](https://github.com/tuandinh0801/search-keywords-scraper/issues/12))
- [ ] Responsive mobile optimization
- [ ] Keyboard shortcuts and power user features
- [ ] Accessibility compliance (WCAG 2.1)
- [ ] Export functionality (CSV, PDF reports) ([#13](https://github.com/tuandinh0801/search-keywords-scraper/issues/13))


## Technology Stack

### Frontend
- **Framework**: Next.js 15+ with App Router
- **Language**: TypeScript 5.0+
- **Styling**: Tailwind CSS + SASS
- **UI Components**: shadcn/ui (Radix UI)
- **State Management**: TanStack Query + Zustand
- **Forms**: React Hook Form + Zod validation

### Backend
- **Framework**: NestJS 10.0+
- **Language**: TypeScript 5.0+
- **Database**: PostgreSQL 15+ with Drizzle ORM
- **Queue**: Bull Queue with Redis 7.0+
- **Authentication**: Supabase Auth with JWT
- **API Documentation**: Swagger/OpenAPI

### Workers & Scraping
- **Runtime**: Node.js 18+
- **Browser Automation**: Playwright with stealth plugins
- **Proxy Management**: Custom proxy rotation system
- **Job Processing**: Bull Queue consumer
- **Monitoring**: Winston logging + Prometheus metrics

### Infrastructure
- **Monorepo**: Turborepo for build optimization
- **Package Manager**: pnpm with workspace support
- **Code Quality**: Biome (formatting/linting)
- **Testing**: Jest + Playwright + Testing Library
- **CI/CD**: GitHub Actions
- **Deployment**: Docker + Digital Ocean / Vercel

## >� Testing Strategy

We follow a priority-based testing approach focused on high-value coverage:

- **60% Unit Tests**: Business logic, validation, algorithms
- **30% Integration Tests**: API endpoints, database operations, queue processing
- **10% E2E Tests**: Critical user journeys and workflows

For detailed testing guidelines, see our [Testing Strategy](docs/08-testing-strategy.md).

## 🚀 Development Workflow

### Issue-Driven Development

We follow an **issue-driven development** approach where all features are tracked through GitHub Issues:

1. **Browse Issues**: Check [open issues](https://github.com/tuandinh0801/search-keywords-scraper/issues) for available work
2. **Pick an Issue**: Choose an issue from the current milestone ([v1.0 Core Foundation](https://github.com/tuandinh0801/search-keywords-scraper/milestone/1))
3. **Create Branch**: Create feature branch from `main` using pattern: `feature/issue-{number}-{short-description}`
4. **Development**: Follow our [Coding Standards](docs/03-coding-standards.md) and [Testing Strategy](docs/08-testing-strategy.md)
5. **Pull Request**: Submit PR linking to the issue with `Closes #issue-number`
6. **Review & Merge**: Code review, testing, and merge to `main`

### Branch Naming Convention

```bash
# Feature branches
feature/issue-1-auth-integration
feature/issue-3-csv-upload
feature/issue-4-playwright-scraper

# Bug fix branches  
fix/issue-17-proxy-timeout
fix/issue-23-memory-leak

# Enhancement branches
enhancement/issue-12-dark-theme
```

### Commit Message Format

```bash
# Link commits to issues
feat: implement Supabase auth integration (#1)
fix: resolve proxy timeout issue (#17)
docs: update API documentation (#25)
test: add CSV validation tests (#3)
```

## 🤝 Contributing

### Development Workflow

1. **Pick an Issue**: Choose from [available issues](https://github.com/tuandinh0801/search-keywords-scraper/issues?q=is%3Aissue+is%3Aopen+label%3A%22good+first+issue%22)
2. **Fork and Clone**: Create your own fork of the repository
3. **Branch Strategy**: Create feature branches from `main` following naming conventions above
4. **Development**: Follow our [Coding Standards](docs/03-coding-standards.md)
5. **Testing**: Ensure all tests pass and add tests for new features
6. **Pull Request**: Submit PR with clear description linking to the issue

### Code Quality Requirements

- TypeScript strict mode compliance
- 80%+ test coverage for new features
- All linting and formatting checks must pass
- Documentation updates for public APIs

## Requirements & Compliance

This project fulfills the following technical requirements:

- **Web Framework**: Next.js with TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Secure user authentication and session management
- **File Processing**: CSV upload with 1-100 keyword validation
- **Web Scraping**: Creative solutions for anti-scraping limitations
- **Real-time Updates**: Live progress tracking and result display
- **Testing**: Comprehensive test suite with multiple test types
- **Git Workflow**: Regular commits and pull request workflow

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.