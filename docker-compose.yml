version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: scraper-postgres
    environment:
      POSTGRES_DB: scraper_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for Queue Management
  redis:
    image: redis:7-alpine
    container_name: scraper-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Next.js Web Application
  web:
    build:
      context: .
      dockerfile: apps/web/Dockerfile.dev
    container_name: scraper-web
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:3001
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
    volumes:
      - .:/app
      - /app/node_modules
      - /app/apps/web/node_modules
    depends_on:
      - api
    command: pnpm dev
    stdin_open: true
    tty: true

  # NestJS API Server
  api:
    build:
      context: .
      dockerfile: apps/api/Dockerfile.dev
    container_name: scraper-api
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/scraper_dev
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=dev-jwt-secret-key
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_JWT_SECRET=${SUPABASE_JWT_SECRET}
      - PORT=3001
    volumes:
      - .:/app
      - /app/node_modules
      - /app/apps/api/node_modules
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: pnpm dev
    stdin_open: true
    tty: true

  # Worker Processes
  worker:
    build:
      context: .
      dockerfile: apps/worker/Dockerfile.dev
    container_name: scraper-worker
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/scraper_dev
      - REDIS_URL=redis://redis:6379
    volumes:
      - .:/app
      - /app/node_modules
      - /app/apps/worker/node_modules
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      api:
        condition: service_started
    command: pnpm dev
    stdin_open: true
    tty: true
    # Scale workers for better performance
    deploy:
      replicas: 2

  # Redis Commander (Optional - Redis Web UI)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: scraper-redis-commander
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    profiles:
      - tools

  # pgAdmin (Optional - PostgreSQL Web UI)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: scraper-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    depends_on:
      - postgres
    profiles:
      - tools

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    name: scraper-network