# Icon Usage Guide

This guide shows how to use the Icon component system in the Search Keywords Scraper application.

## Overview

We use **Lucide React** as our icon library, wrapped in a convenient `Icon` component for consistent usage throughout the app.

## Basic Usage

### Using the Icon Component

```tsx
import { Icon } from '@/components/ui'

// Basic usage
<Icon name="search" />

// With custom size
<Icon name="dashboard" size={24} />

// With custom styling
<Icon name="settings" className="text-blue-500 hover:text-blue-700" />
```

### Using SizedIcon for Predefined Sizes

```tsx
import { SizedIcon } from '@/components/ui'

<SizedIcon name="user" size="xs" />   // 12px
<SizedIcon name="menu" size="sm" />   // 14px
<SizedIcon name="edit" size="md" />   // 16px (default)
<SizedIcon name="view" size="lg" />   // 20px
<SizedIcon name="plus" size="xl" />   // 24px
<SizedIcon name="star" size="2xl" />  // 32px
```

### Using DirectIcon for Lucide Icons

```tsx
import { DirectIcon, Search, Settings } from '@/components/ui'

// When you want to use Lucide icons directly
<DirectIcon icon={Search} size={20} />
<DirectIcon icon={Settings} className="text-gray-500" />
```

## Available Icons

### Navigation Icons
- `dashboard` - LayoutDashboard
- `search` - Search
- `keywords` - FileText
- `upload` - Upload
- `reports` - BarChart3
- `settings` - Settings
- `menu` - Menu
- `close` - X

### Action Icons
- `plus` - Plus
- `minus` - Minus
- `edit` - Edit
- `delete` - Trash2
- `copy` - Copy
- `download` - Download
- `view` - Eye
- `externalLink` - ExternalLink

### Status Icons
- `success` - CheckCircle
- `error` - XCircle
- `warning` - AlertCircle
- `info` - Info
- `pending` - Clock
- `check` - Check

### User & Auth Icons
- `user` - User
- `logout` - LogOut

### Loading & Data Icons
- `loading` - Loader2
- `refresh` - RefreshCw
- `filter` - Filter
- `sortAsc` - SortAsc
- `sortDesc` - SortDesc

### Common Icons
- `calendar` - Calendar
- `mail` - Mail
- `phone` - Phone
- `location` - MapPin
- `globe` - Globe
- `star` - Star
- `heart` - Heart
- `bookmark` - Bookmark
- `share` - Share
- `moreHorizontal` - MoreHorizontal
- `moreVertical` - MoreVertical

## Examples in Components

### Button with Icon

```tsx
import { Button, Icon } from '@/components/ui'

<Button>
  <Icon name="plus" size={16} />
  Add New
</Button>

<Button variant="outline">
  <Icon name="download" size={16} />
  Export
</Button>
```

### Card Header with Icon

```tsx
import { Card, CardHeader, CardTitle, Icon } from '@/components/ui'

<Card>
  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
    <CardTitle className="text-sm font-medium">Total Keywords</CardTitle>
    <Icon name="keywords" className="text-muted-foreground" />
  </CardHeader>
  {/* ... */}
</Card>
```

### Navigation Item with Icon

```tsx
import { Icon } from '@/components/ui'

<Link href="/dashboard" className="flex items-center gap-3">
  <Icon name="dashboard" size={20} />
  <span>Dashboard</span>
</Link>
```

### Status Badge with Icon

```tsx
import { Icon } from '@/components/ui'

<div className="flex items-center gap-2">
  <Icon name="success" size={16} className="text-green-500" />
  <span>Completed</span>
</div>
```

## Adding New Icons

To add new icons to the registry:

1. Import the icon from `lucide-react`:
```tsx
import { NewIcon } from 'lucide-react'
```

2. Add it to the `iconRegistry` in `apps/web/src/components/ui/icon.tsx`:
```tsx
export const iconRegistry = {
  // ... existing icons
  newIcon: NewIcon,
} as const
```

3. Export it at the bottom of the file:
```tsx
export {
  // ... existing exports
  NewIcon,
}
```

## Best Practices

1. **Use consistent sizes**: Stick to the predefined sizes when possible
2. **Semantic naming**: Use descriptive names in the icon registry
3. **Accessibility**: Always provide proper `aria-label` when icons are standalone
4. **Performance**: The icon registry is tree-shakeable, so only used icons are bundled
5. **Styling**: Use Tailwind classes for consistent styling

## TypeScript Support

The Icon component provides full TypeScript support:

```tsx
import { Icon, IconName } from '@/components/ui'

// IconName type includes all available icon names
const iconName: IconName = 'dashboard' // ✅ Valid
const invalidIcon: IconName = 'invalid' // ❌ TypeScript error
```

## Migration from SVG

Replace inline SVG with Icon components:

```tsx
// Before
<svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
  <path strokeLinecap="round" strokeLinejoin="round" d="..." />
</svg>

// After
<Icon name="search" size={16} />
```
