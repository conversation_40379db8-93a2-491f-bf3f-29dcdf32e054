{"name": "@search-keywords-scraper/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write ."}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.55.0", "@tanstack/react-query": "^5.85.3", "@tanstack/react-query-devtools": "^5.85.3", "@types/papaparse": "^5.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.540.0", "next": "15.4.6", "papaparse": "^5.5.3", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "sass": "^1.90.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.17", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5"}}