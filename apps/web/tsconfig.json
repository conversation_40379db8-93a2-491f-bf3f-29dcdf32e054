{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@search-keywords-scraper/types": ["../../packages/types/src"], "@search-keywords-scraper/database": ["../../packages/database/src"], "@search-keywords-scraper/config": ["../../packages/config/src"], "@search-keywords-scraper/utils": ["../../packages/utils/src"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"], "references": [{"path": "../../packages/types"}, {"path": "../../packages/database"}, {"path": "../../packages/config"}, {"path": "../../packages/utils"}]}