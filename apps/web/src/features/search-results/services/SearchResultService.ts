import { apiClient } from '@/lib/api-client';
import type { SearchResultDetail } from '../types';

export class SearchResultService {
  /**
   * Get search result detail by ID
   */
  static async getSearchResult(searchResultId: string): Promise<SearchResultDetail> {
    return await apiClient.get(`/api/v1/search-results/${searchResultId}`);
  }

  /**
   * Get search result detail by keyword ID
   */
  static async getSearchResultByKeyword(keywordId: string): Promise<SearchResultDetail> {
    return await apiClient.get(`/api/v1/search-results/keyword/${keywordId}`);
  }

  /**
   * Get HTML content for a search result (lazy loaded)
   */
  static async getSearchResultHtml(searchResultId: string): Promise<string> {
    const response = await apiClient.get<{ htmlContent: string }>(`/api/v1/search-results/${searchResultId}/html`);
    return response.htmlContent;
  }
}