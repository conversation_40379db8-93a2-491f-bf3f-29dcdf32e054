export interface LinkDetail {
  url: string;
  title: string;
  position: number;
  type: 'ad' | 'organic' | 'navigation' | 'footer' | 'other';
  domain: string;
  anchorText?: string;
}

export interface SearchResultDetail {
  id: string;
  keywordId: string;
  keyword: string;
  searchEngine: 'google' | 'bing';
  batchId: string;
  batchName: string;
  totalAds: number;
  totalLinks: number;
  adsLinks: LinkDetail[];
  organicLinks: LinkDetail[];
  otherLinks: LinkDetail[];
  metadata: Record<string, unknown>;
  htmlContent: string | null;
  scrapedAt: string;
}

export interface SearchResultSummary {
  totalAds: number;
  totalOrganic: number;
  totalOther: number;
  totalLinks: number;
  topDomains: { domain: string; count: number }[];
}