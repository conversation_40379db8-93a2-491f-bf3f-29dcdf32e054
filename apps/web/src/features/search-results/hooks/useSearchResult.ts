import { useQuery } from '@tanstack/react-query';
import { SearchResultService } from '../services/SearchResultService';
import type { SearchResultDetail } from '../types';

export function useSearchResult(searchResultId: string | null) {
  return useQuery<SearchResultDetail | null>({
    queryKey: ['search-result', searchResultId],
    queryFn: async () => {
      if (!searchResultId) {
        return null;
      }
      try {
        const result = await SearchResultService.getSearchResult(searchResultId);
        // Ensure we always return a value, never undefined
        return result ?? null;
      } catch (error) {
        // Return null instead of throwing for 404 errors
        if ((error as Error)?.message?.includes('404') || (error as {response?: {status?: number}})?.response?.status === 404) {
          return null;
        }
        // For other errors, still return null to avoid undefined
        console.error('Error fetching search result:', error);
        return null;
      }
    },
    enabled: !!searchResultId,
  });
}

export function useSearchResultByKeyword(keywordId: string | null) {
  return useQuery<SearchResultDetail | null>({
    queryKey: ['search-result-by-keyword', keywordId],
    queryFn: async () => {
      if (!keywordId) {
        return null;
      }
      try {
        const result = await SearchResultService.getSearchResultByKeyword(keywordId);
        // Ensure we always return a value, never undefined
        return result ?? null;
      } catch (error) {
        // Return null instead of throwing for 404 errors
        if ((error as Error)?.message?.includes('404') || (error as {response?: {status?: number}})?.response?.status === 404) {
          return null;
        }
        // For other errors, still return null to avoid undefined
        console.error('Error fetching search result by keyword:', error);
        return null;
      }
    },
    enabled: !!keywordId,
  });
}

export function useSearchResultHtml(searchResultId: string | null) {
  return useQuery<string | null>({
    queryKey: ['search-result-html', searchResultId],
    queryFn: async () => {
      if (!searchResultId) {
        return null;
      }
      try {
        return await SearchResultService.getSearchResultHtml(searchResultId);
      } catch (error) {
        // Return null instead of throwing for 404 errors
        if ((error as {response?: {status?: number}})?.response?.status === 404) {
          return null;
        }
        throw error;
      }
    },
    enabled: !!searchResultId,
    // Don't refetch automatically since HTML content is static
    staleTime: Infinity,
    gcTime: 5 * 60 * 1000, // Cache for 5 minutes
  });
}