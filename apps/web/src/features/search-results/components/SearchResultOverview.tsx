'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { ExternalLink, Globe, Clock } from 'lucide-react';
import type { SearchResultDetail } from '../types';

interface SearchResultOverviewProps {
  searchResult: SearchResultDetail;
}

export function SearchResultOverview({ searchResult }: SearchResultOverviewProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-6">
      {/* Header Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl">{searchResult.keyword}</CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="flex items-center gap-1">
                <Globe className="h-3 w-3" />
                {searchResult.searchE<PERSON><PERSON>}
              </Badge>
              <Badge variant="secondary" className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {formatDate(searchResult.scrapedAt)}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">
                {searchResult.totalAds}
              </div>
              <div className="text-sm text-gray-600">Total Ads</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {searchResult.organicLinks.length}
              </div>
              <div className="text-sm text-gray-600">Organic Results</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {searchResult.otherLinks.length}
              </div>
              <div className="text-sm text-gray-600">Other Links</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {searchResult.totalLinks}
              </div>
              <div className="text-sm text-gray-600">Total Links</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Batch Context */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Batch Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">{searchResult.batchName}</p>
              <p className="text-sm text-gray-600">Batch ID: {searchResult.batchId}</p>
            </div>
            <a 
              href={`/dashboard/batches/${searchResult.batchId}`}
              className="flex items-center gap-1 text-blue-600 hover:text-blue-800 transition-colors"
            >
              View Batch <ExternalLink className="h-4 w-4" />
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}