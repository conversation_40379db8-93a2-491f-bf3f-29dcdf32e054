'use client';

import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { ExternalLink, Copy, Eye, EyeOff } from 'lucide-react';
import { useState } from 'react';
import type { LinkDetail } from '../types';

interface LinksListProps {
  title: string;
  links: LinkDetail[];
  variant: 'ads' | 'organic' | 'other';
}

const variantStyles = {
  ads: {
    bgColor: 'bg-red-50',
    textColor: 'text-red-700',
    badgeVariant: 'destructive' as const,
  },
  organic: {
    bgColor: 'bg-green-50',
    textColor: 'text-green-700',
    badgeVariant: 'default' as const,
  },
  other: {
    bgColor: 'bg-blue-50',
    textColor: 'text-blue-700',
    badgeVariant: 'secondary' as const,
  },
};

export function LinksList({ title, links, variant }: LinksListProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [copiedUrl, setCopiedUrl] = useState<string | null>(null);

  const styles = variantStyles[variant];
  const displayLinks = isExpanded ? links : links.slice(0, 5);

  const copyToClipboard = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      setCopiedUrl(url);
      setTimeout(() => setCopiedUrl(null), 2000);
    } catch (err) {
      console.error('Failed to copy URL:', err);
    }
  };

  const getDomainCounts = () => {
    const domainCounts = links.reduce((acc, link) => {
      acc[link.domain] = (acc[link.domain] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(domainCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5);
  };

  if (links.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            {title}
            <Badge variant={styles.badgeVariant}>0 links</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500 text-center py-4">No {variant} links found</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          {title}
          <Badge variant={styles.badgeVariant}>{links.length} links</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Domain Summary */}
        <div className="mb-4">
          <h4 className="text-sm font-medium mb-2">Top Domains:</h4>
          <div className="flex flex-wrap gap-2">
            {getDomainCounts().map(([domain, count]) => (
              <Badge key={domain} variant="outline" className="text-xs">
                {domain} ({count})
              </Badge>
            ))}
          </div>
        </div>

        {/* Links List */}
        <div className="space-y-3">
          {displayLinks.map((link, index) => (
            <div
              key={`${link.url}-${index}`}
              className={`p-3 rounded-lg border ${styles.bgColor}`}
            >
              <div className="flex items-start justify-between gap-3">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-xs font-mono bg-gray-100 px-2 py-1 rounded">
                      #{link.position}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {link.type}
                    </Badge>
                  </div>
                  
                  <h5 className={`font-medium ${styles.textColor} mb-1 line-clamp-2`}>
                    {link.title || 'Untitled'}
                  </h5>
                  
                  <p className="text-sm text-gray-600 mb-2 break-all">
                    <span className="font-medium">{link.domain}</span>
                  </p>
                  
                  <p className="text-xs text-gray-500 break-all">
                    {link.url}
                  </p>
                  
                  {link.anchorText && link.anchorText !== link.title && (
                    <p className="text-xs text-gray-400 mt-1">
                      Anchor: &ldquo;{link.anchorText}&rdquo;
                    </p>
                  )}
                </div>
                
                <div className="flex flex-col gap-1">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(link.url, '_blank')}
                    className="h-8 w-8 p-0"
                  >
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                  
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(link.url)}
                    className="h-8 w-8 p-0"
                  >
                    <Copy className={`h-3 w-3 ${copiedUrl === link.url ? 'text-green-600' : ''}`} />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Show More/Less Button */}
        {links.length > 5 && (
          <div className="mt-4 text-center">
            <Button
              variant="outline"
              onClick={() => setIsExpanded(!isExpanded)}
              className="flex items-center gap-2"
            >
              {isExpanded ? (
                <>
                  <EyeOff className="h-4 w-4" />
                  Show Less
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4" />
                  Show All ({links.length - 5} more)
                </>
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}