'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Code, Download, Eye } from 'lucide-react';
import { useState } from 'react';
import { SearchResultOverview } from './SearchResultOverview';
import { LinksList } from './LinksList';
import type { SearchResultDetail as SearchResultDetailType } from '../types';

interface SearchResultDetailProps {
  searchResult: SearchResultDetailType;
}

export function SearchResultDetail({ searchResult }: SearchResultDetailProps) {
  const [showHtmlContent, setShowHtmlContent] = useState(false);

  const downloadHtml = () => {
    if (!searchResult.htmlContent) return;

    const blob = new Blob([searchResult.htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${searchResult.keyword}_${searchResult.searchEngine}_${new Date(searchResult.scrapedAt).toISOString().split('T')[0]}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const formatMetadata = () => {
    if (!searchResult.metadata) return null;
    return JSON.stringify(searchResult.metadata, null, 2);
  };

  return (
    <div className="space-y-6">
      {/* Overview Section */}
      <SearchResultOverview searchResult={searchResult} />

      {/* Tabs for Different Views */}
      <Tabs defaultValue="links" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="links">Link Analysis</TabsTrigger>
          <TabsTrigger value="metadata">Metadata & Raw Data</TabsTrigger>
          <TabsTrigger value="html">HTML Content</TabsTrigger>
        </TabsList>

        {/* Links Analysis Tab */}
        <TabsContent value="links" className="space-y-6">
          <div className="grid gap-6">
            <LinksList
              title="Advertisement Links"
              links={searchResult.adsLinks}
              variant="ads"
            />
            
            <LinksList
              title="Organic Search Results"
              links={searchResult.organicLinks}
              variant="organic"
            />
            
            <LinksList
              title="Other Links"
              links={searchResult.otherLinks}
              variant="other"
            />
          </div>
        </TabsContent>

        {/* Metadata Tab */}
        <TabsContent value="metadata" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Search Results Metadata</CardTitle>
            </CardHeader>
            <CardContent>
              {searchResult.metadata ? (
                <ScrollArea className="h-96 w-full">
                  <pre className="text-sm bg-gray-50 p-4 rounded-lg overflow-auto">
                    {formatMetadata()}
                  </pre>
                </ScrollArea>
              ) : (
                <p className="text-gray-500">No metadata available</p>
              )}
            </CardContent>
          </Card>

          {/* Raw Data Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Raw Data Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-medium">Search Result ID:</span>
                  <p className="text-gray-600 break-all">{searchResult.id}</p>
                </div>
                <div>
                  <span className="font-medium">Keyword ID:</span>
                  <p className="text-gray-600 break-all">{searchResult.keywordId}</p>
                </div>
                <div>
                  <span className="font-medium">Batch ID:</span>
                  <p className="text-gray-600 break-all">{searchResult.batchId}</p>
                </div>
                <div>
                  <span className="font-medium">Scraped At:</span>
                  <p className="text-gray-600">{new Date(searchResult.scrapedAt).toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* HTML Content Tab */}
        <TabsContent value="html" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Cached HTML Content
                <div className="flex gap-2">
                  {searchResult.htmlContent && (
                    <>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={downloadHtml}
                        className="flex items-center gap-2"
                      >
                        <Download className="h-4 w-4" />
                        Download HTML
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setShowHtmlContent(!showHtmlContent)}
                        className="flex items-center gap-2"
                      >
                        {showHtmlContent ? <Eye className="h-4 w-4" /> : <Code className="h-4 w-4" />}
                        {showHtmlContent ? 'Hide' : 'Show'} HTML
                      </Button>
                    </>
                  )}
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {searchResult.htmlContent ? (
                <div>
                  <div className="mb-4">
                    <Badge variant="outline">
                      {Math.round(searchResult.htmlContent.length / 1024)} KB
                    </Badge>
                  </div>
                  
                  {showHtmlContent && (
                    <ScrollArea className="h-96 w-full">
                      <pre className="text-xs bg-gray-50 p-4 rounded-lg overflow-auto whitespace-pre-wrap">
                        {searchResult.htmlContent}
                      </pre>
                    </ScrollArea>
                  )}
                  
                  {!showHtmlContent && (
                    <p className="text-gray-500 text-center py-8">
                      Click &ldquo;Show HTML&rdquo; to view the cached page content
                    </p>
                  )}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">
                  No HTML content cached for this search result
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}