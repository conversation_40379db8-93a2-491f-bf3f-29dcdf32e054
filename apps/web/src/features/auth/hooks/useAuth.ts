'use client'

import { useSignInMutation, useSignUpMutation, useSignOutMutation } from './useAuthMutations'
import { useAuthStore } from '@/stores/authStore'
import type { LoginCredentials, RegisterCredentials } from '../services/AuthService'

export function useAuth() {
  const { user, isAuthenticated, isInitialized } = useAuthStore()

  const signInMutation = useSignInMutation()
  const signUpMutation = useSignUpMutation()
  const signOutMutation = useSignOutMutation()

  const signIn = async (credentials: LoginCredentials) => {
    try {
      await signInMutation.mutateAsync(credentials)
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Sign in failed' 
      }
    }
  }

  const signUp = async (credentials: RegisterCredentials) => {
    try {
      await signUpMutation.mutateAsync(credentials)
      return { 
        success: true, 
        message: 'Check your email for the confirmation link!' 
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Sign up failed' 
      }
    }
  }

  const signOut = async () => {
    try {
      await signOutMutation.mutateAsync()
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Sign out failed' 
      }
    }
  }

  return {
    // State
    user,
    isAuthenticated,
    isInitialized,

    // Actions
    signIn,
    signUp,
    signOut,

    // Loading states from mutations
    isSigningIn: signInMutation.isPending,
    isSigningUp: signUpMutation.isPending,
    isSigningOut: signOutMutation.isPending,

    // Error states from mutations
    signInError: signInMutation.error?.message || null,
    signUpError: signUpMutation.error?.message || null,
    signOutError: signOutMutation.error?.message || null,

    // Reset functions
    resetSignInError: signInMutation.reset,
    resetSignUpError: signUpMutation.reset,
    resetSignOutError: signOutMutation.reset,
  }
}