import { useQuery } from '@tanstack/react-query'
import { apiClient } from '@/lib/api-client'

export function useApiAuth() {
  // Get current user from API (demonstrates API auth integration)
  const { 
    data: apiUser, 
    error: apiUserError, 
    isLoading: isLoadingApiUser,
    refetch: refetchApiUser 
  } = useQuery({
    queryKey: ['api-auth', 'me'],
    queryFn: () => apiClient.getCurrentUser(),
    retry: false, // Don't retry on auth failures
    refetchOnWindowFocus: false,
  })

  // Get user profile from API
  const { 
    data: apiProfile, 
    error: apiProfileError, 
    isLoading: isLoadingApiProfile,
    refetch: refetchApiProfile 
  } = useQuery({
    queryKey: ['api-auth', 'profile'],
    queryFn: () => apiClient.getProfile(),
    retry: false,
    refetchOnWindowFocus: false,
  })

  return {
    // API user data
    apiUser,
    apiProfile,
    
    // Loading states
    isLoadingApiUser,
    isLoadingApiProfile,
    
    // Error states
    apiUserError,
    apiProfileError,
    
    // Refetch functions
    refetchApiUser,
    refetchApiProfile,
    
    // Helper methods
    isApiAuthenticated: !apiUserError && !!apiUser,
    hasApiError: !!(apiUserError || apiProfileError),
  }
}