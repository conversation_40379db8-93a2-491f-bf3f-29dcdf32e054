'use client'

import { useMutation } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/authStore'
import { AuthService, type LoginCredentials, type RegisterCredentials } from '../services/AuthService'

const authService = new AuthService()

export function useSignInMutation() {
  const router = useRouter()
  const setUser = useAuthStore((state) => state.setUser)

  return useMutation({
    mutationFn: async (credentials: LoginCredentials) => {
      const { user, error } = await authService.signIn(credentials)
      
      if (error) {
        throw new Error(error.message)
      }

      if (!user) {
        throw new Error('Sign in failed')
      }

      return user
    },
    onSuccess: (user) => {
      setUser(user)
      router.push('/dashboard')
      router.refresh()
    },
    onError: (error) => {
      console.error('Sign in error:', error)
    },
  })
}

export function useSignUpMutation() {
  return useMutation({
    mutationFn: async (credentials: RegisterCredentials) => {
      const { user, error } = await authService.signUp(credentials)
      
      if (error) {
        throw new Error(error.message)
      }

      return user
    },
    onError: (error) => {
      console.error('Sign up error:', error)
    },
  })
}

export function useSignOutMutation() {
  const router = useRouter()
  const logout = useAuthStore((state) => state.logout)

  return useMutation({
    mutationFn: async () => {
      const { error } = await authService.signOut()
      
      if (error) {
        throw new Error(error.message)
      }

      return true
    },
    onSuccess: () => {
      logout()
      router.push('/auth/login')
      router.refresh()
    },
    onError: (error) => {
      console.error('Sign out error:', error)
    },
  })
}