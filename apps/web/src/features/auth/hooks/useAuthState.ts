'use client'

import { useEffect } from 'react'
import { useAuthStore } from '@/stores/authStore'
import { AuthService } from '../services/AuthService'

const authService = new AuthService()

export function useAuthState() {
  const { user, isAuthenticated, isInitialized, setUser, setInitialized } = useAuthStore()

  useEffect(() => {
    // Get initial user
    const initializeAuth = async () => {
      try {
        const { user } = await authService.getCurrentUser()
        setUser(user)
      } catch (error) {
        console.error('Failed to get initial user:', error)
        setUser(null)
      } finally {
        setInitialized(true)
      }
    }

    initializeAuth()

    // Listen for auth state changes
    const { data: { subscription } } = authService.onAuthStateChange((user) => {
      setUser(user)
      if (!isInitialized) {
        setInitialized(true)
      }
    })

    return () => subscription.unsubscribe()
  }, [setUser, setInitialized, isInitialized])

  return {
    user,
    isAuthenticated,
    isInitialized,
    loading: !isInitialized
  }
}