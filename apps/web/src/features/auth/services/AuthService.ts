import { createClient } from '@/lib/supabase/client'
import type { User, AuthError } from '@supabase/supabase-js'

export interface AuthResult {
  user: User | null
  error: AuthError | null
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterCredentials {
  email: string
  password: string
}

export class AuthService {
  private supabase = createClient()

  async signIn(credentials: LoginCredentials): Promise<AuthResult> {
    try {
      const { data, error } = await this.supabase.auth.signInWithPassword(credentials)
      return {
        user: data.user,
        error: error
      }
    } catch (err) {
      return {
        user: null,
        error: {
          message: 'An unexpected error occurred during sign in',
          name: 'UnexpectedError',
          status: 500
        } as AuthError
      }
    }
  }

  async signUp(credentials: RegisterCredentials): Promise<AuthResult> {
    try {
      const { data, error } = await this.supabase.auth.signUp(credentials)
      return {
        user: data.user,
        error: error
      }
    } catch (err) {
      return {
        user: null,
        error: {
          message: 'An unexpected error occurred during sign up',
          name: 'UnexpectedError',
          status: 500
        } as AuthError
      }
    }
  }

  async signOut(): Promise<{ error: AuthError | null }> {
    try {
      const { error } = await this.supabase.auth.signOut()
      return { error }
    } catch (err) {
      return {
        error: {
          message: 'An unexpected error occurred during sign out',
          name: 'UnexpectedError',
          status: 500
        } as AuthError
      }
    }
  }

  async getCurrentUser(): Promise<{ user: User | null; error: AuthError | null }> {
    try {
      const { data, error } = await this.supabase.auth.getUser()
      return {
        user: data.user,
        error: error
      }
    } catch (err) {
      return {
        user: null,
        error: {
          message: 'An unexpected error occurred while fetching user',
          name: 'UnexpectedError',
          status: 500
        } as AuthError
      }
    }
  }

  onAuthStateChange(callback: (user: User | null) => void) {
    return this.supabase.auth.onAuthStateChange((event, session) => {
      callback(session?.user ?? null)
    })
  }
}