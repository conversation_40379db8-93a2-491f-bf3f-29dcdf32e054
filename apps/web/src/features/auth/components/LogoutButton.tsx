'use client'

import * as React from "react"
import { useRouter } from 'next/navigation'
import { useAuth } from '../hooks/useAuth'
import { Button } from '@/components/ui/Button'

interface LogoutButtonProps {
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  className?: string
  children?: React.ReactNode
}

export function LogoutButton({ 
  variant = "destructive", 
  size = "sm", 
  className,
  children = "Logout" 
}: LogoutButtonProps) {
  const { signOut, isSigningOut } = useAuth()
  const router = useRouter()

  const handleLogout = async () => {
    const result = await signOut()
    if (result.success) {
      router.push('/auth/login')
    }
  }

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={handleLogout}
      disabled={isSigningOut}
    >
      {isSigningOut ? 'Signing out...' : children}
    </Button>
  )
}