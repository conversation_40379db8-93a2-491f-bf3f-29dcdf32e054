'use client'

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON>, Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/Form'
import { Input } from '@/components/ui/Input'
import { signInSchema, signUpSchema, type SignInFormData, type SignUpFormData } from '@/lib/validations/Auth'

interface AuthFormProps {
  mode: "signin" | "signup"
  onSubmit: (data: SignInFormData | SignUpFormData) => Promise<void>
  onModeChange: (mode: "signin" | "signup") => void
  loading?: boolean
  error?: string
}

export function AuthForm({ mode, onSubmit, onModeChange, loading = false, error }: AuthFormProps) {
  const schema = mode === "signin" ? signInSchema : signUpSchema
  const form = useForm<SignInFormData | SignUpFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      email: '',
      password: '',
      ...(mode === "signup" && { confirmPassword: '' })
    }
  })

  React.useEffect(() => {
    form.reset({
      email: '',
      password: '',
      ...(mode === "signup" && { confirmPassword: '' })
    })
  }, [mode, form])

  const handleSubmit = async (data: SignInFormData | SignUpFormData) => {
    await onSubmit(data)
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl text-center">
          {mode === "signin" ? "Sign in to your account" : "Create an account"}
        </CardTitle>
        <CardDescription className="text-center">
          {mode === "signin" 
            ? "Enter your email and password to sign in" 
            : "Enter your details to create a new account"
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {error && (
              <div className="p-3 text-sm text-red-800 bg-red-100 border border-red-200 rounded-md">
                {error}
              </div>
            )}
            
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Enter your email"
                      disabled={loading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="Enter your password"
                      disabled={loading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {mode === "signup" && (
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="Confirm your password"
                        disabled={loading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            
            <Button type="submit" className="w-full" disabled={loading}>
              {loading 
                ? (mode === "signin" ? "Signing in..." : "Creating account...")
                : (mode === "signin" ? "Sign In" : "Create Account")
              }
            </Button>
          </form>
        </Form>
        
        <div className="mt-4 text-center text-sm">
          {mode === "signin" ? (
            <span>
              Don&apos;t have an account?{" "}
              <button
                type="button"
                onClick={() => onModeChange("signup")}
                className="text-primary hover:text-primary/80 font-medium"
                disabled={loading}
              >
                Sign up
              </button>
            </span>
          ) : (
            <span>
              Already have an account?{" "}
              <button
                type="button"
                onClick={() => onModeChange("signin")}
                className="text-primary hover:text-primary/80 font-medium"
                disabled={loading}
              >
                Sign in
              </button>
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  )
}