'use client'

import * as React from "react"
import { AuthForm } from './AuthForm'
import { useAuth } from '../hooks/useAuth'
import type { SignInFormData, SignUpFormData } from '@/lib/validations/Auth'

export function LoginForm() {
  const [mode, setMode] = React.useState<"signin" | "signup">("signin")
  const [showMessage, setShowMessage] = React.useState<string | null>(null)

  const { 
    signIn, 
    signUp, 
    isSigningIn, 
    isSigningUp, 
    signInError, 
    signUpError,
    resetSignInError,
    resetSignUpError
  } = useAuth()

  const loading = isSigningIn || isSigningUp
  const error = signInError || signUpError

  const handleSubmit = async (data: SignInFormData | SignUpFormData) => {
    resetSignInError()
    resetSignUpError()
    setShowMessage(null)

    if (mode === "signin") {
      const result = await signIn({ 
        email: data.email, 
        password: data.password 
      })
      if (!result.success && result.error) {
        // Error is handled by TanStack Query and displayed via error prop
      }
    } else {
      // For signup, we need the confirmPassword field
      const signUpData = data as SignUpFormData
      const result = await signUp({ 
        email: signUpData.email, 
        password: signUpData.password 
      })
      if (result.success && result.message) {
        setShowMessage(result.message)
      }
    }
  }

  const handleModeChange = (newMode: "signin" | "signup") => {
    setMode(newMode)
    setShowMessage(null)
    resetSignInError()
    resetSignUpError()
  }

  return (
    <div className="w-full max-w-md">
      {/* Success Message */}
      {showMessage && (
        <div className="mb-4 p-3 text-sm text-green-800 bg-green-100 border border-green-200 rounded-md">
          {showMessage}
        </div>
      )}
      
      <AuthForm
        mode={mode}
        onSubmit={handleSubmit}
        onModeChange={handleModeChange}
        loading={loading}
        error={error as string}
      />
    </div>
  )
}
