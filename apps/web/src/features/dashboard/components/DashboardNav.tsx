'use client'

import { User } from '@supabase/supabase-js'
import { LogoutButton } from '@/features/auth/components/LogoutButton'

interface DashboardNavProps {
  user: User
}

export function DashboardNav({ user }: DashboardNavProps) {
  return (
    <nav className="bg-white shadow">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <h1 className="text-xl font-semibold text-gray-900">
              Search Keywords Scraper
            </h1>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-700">
              Welcome, {user.email}
            </span>
            <LogoutButton />
          </div>
        </div>
      </div>
    </nav>
  )
}