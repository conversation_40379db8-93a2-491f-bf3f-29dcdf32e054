'use client'

import * as React from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle, Button } from '@/components/ui'
import { StatusBadge } from '@/components/ui'
import { Badge } from '@/components/ui/Badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/Table'
import { useKeywordBatches } from '@/features/keywords'
import { BatchStatus } from '@search-keywords-scraper/types'

// Map BatchStatus to StatusBadge status
function mapBatchStatusToStatusBadge(status: BatchStatus | string): "pending" | "processing" | "completed" | "error" | "cancelled" {
  // Handle both enum values and string values
  const statusString = typeof status === 'string' ? status : String(status)
  
  switch (statusString) {
    case 'pending':
    case BatchStatus.PENDING:
      return 'pending'
    case 'processing':
    case BatchStatus.PROCESSING:
      return 'processing'
    case 'completed':
    case BatchStatus.COMPLETED:
      return 'completed'
    case 'failed':
    case BatchStatus.FAILED:
      return 'error'
    default:
      return 'pending'
  }
}

export function BatchList() {
  const router = useRouter()
  const [page, setPage] = React.useState(1)
  const [statusFilter, setStatusFilter] = React.useState<string>('')
  
  const { data, isLoading, error } = useKeywordBatches({
    page,
    limit: 20,
    status: statusFilter || undefined
  })

  const batches = data?.batches || []
  const pagination = data?.pagination

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="h-6 bg-muted rounded animate-pulse w-32 mb-2" />
          <div className="h-4 bg-muted rounded animate-pulse w-48" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-muted rounded animate-pulse" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center space-y-2">
            <p className="text-destructive">Failed to load batches</p>
            <p className="text-sm text-muted-foreground">{error.message}</p>
            <Button onClick={() => window.location.reload()} variant="outline" size="sm">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const isEmpty = !batches || batches.length === 0

  if (isEmpty) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <div className="mx-auto w-24 h-24 text-muted-foreground">
              <svg fill="none" viewBox="0 0 24 24" strokeWidth="1" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
              </svg>
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">No keyword batches found</h3>
              <p className="text-muted-foreground">
                {statusFilter ? `No batches with status "${statusFilter}"` : 'Get started by uploading your first CSV file with keywords to scrape.'}
              </p>
            </div>
            <Button asChild size="lg">
              <Link href="/upload">
                <svg className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
                </svg>
                Upload Your First CSV
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Filters and Actions */}
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="flex gap-2">
          <select
            value={statusFilter}
            onChange={(e) => {
              setStatusFilter(e.target.value)
              setPage(1)
            }}
            className="px-3 py-2 border rounded-md text-sm"
          >
            <option value="">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="processing">Processing</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
          </select>
        </div>
        <Button asChild>
          <Link href="/upload">
            <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
            </svg>
            Upload Keywords
          </Link>
        </Button>
      </div>

      {/* Batches Table */}
      <Card>
        <CardHeader>
          <CardTitle>Keyword Batches</CardTitle>
          <CardDescription>
            {pagination && `${pagination.total} total batches`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Keywords</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {batches.map((batch) => {
                const progress = batch.totalKeywords > 0 
                  ? Math.round((batch.completedKeywords / batch.totalKeywords) * 100) 
                  : 0

                return (
                  <TableRow 
                    key={batch.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => router.push(`/dashboard/batches/${batch.id}`)}
                  >
                    <TableCell className="font-medium">{batch.name}</TableCell>
                    <TableCell>
                      <StatusBadge 
                        status={mapBatchStatusToStatusBadge(batch.status)} 
                        animated={batch.status === 'processing'} 
                      />
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary" className="text-xs">
                        {batch.totalKeywords}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="w-20 bg-muted rounded-full h-2">
                          <div 
                            className="h-2 rounded-full bg-primary" 
                            style={{ width: `${progress}%` }}
                          />
                        </div>
                        <span className="text-xs text-muted-foreground">{progress}%</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {new Date(batch.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          router.push(`/dashboard/batches/${batch.id}`)
                        }}
                      >
                        View
                      </Button>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            disabled={!pagination.hasPrev}
            onClick={() => setPage(page - 1)}
          >
            Previous
          </Button>
          <span className="px-4 py-2 text-sm text-muted-foreground">
            Page {pagination.page} of {pagination.totalPages}
          </span>
          <Button
            variant="outline"
            disabled={!pagination.hasNext}
            onClick={() => setPage(page + 1)}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}