'use client'

import * as React from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle, Button } from '@/components/ui'
import { StatusBadge } from '@/components/ui'
import { Badge } from '@/components/ui/Badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/Table'
import { useKeywordBatch } from '@/features/keywords'
import { BatchStatus } from '@search-keywords-scraper/types'
import { Eye, ExternalLink } from 'lucide-react'

// Map BatchStatus to StatusBadge status
function mapBatchStatusToStatusBadge(status: BatchStatus | string): "pending" | "processing" | "completed" | "error" | "cancelled" {
  const statusString = typeof status === 'string' ? status : String(status)
  
  switch (statusString) {
    case 'pending':
    case BatchStatus.PENDING:
      return 'pending'
    case 'processing':
    case BatchStatus.PROCESSING:
      return 'processing'
    case 'completed':
    case BatchStatus.COMPLETED:
      return 'completed'
    case 'failed':
    case BatchStatus.FAILED:
      return 'error'
    default:
      return 'pending'
  }
}

interface BatchDetailProps {
  batchId: string
}

export function BatchDetail({ batchId }: BatchDetailProps) {
  const router = useRouter()
  const { data, isLoading, error } = useKeywordBatch(batchId)

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="h-6 bg-muted rounded animate-pulse w-48 mb-2" />
            <div className="h-4 bg-muted rounded animate-pulse w-32" />
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-16 bg-muted rounded animate-pulse" />
              ))}
            </div>
            <div className="h-64 bg-muted rounded animate-pulse" />
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center space-y-2">
            <p className="text-destructive">Failed to load batch details</p>
            <p className="text-sm text-muted-foreground">{error.message}</p>
            <div className="flex gap-2 justify-center">
              <Button onClick={() => window.location.reload()} variant="outline" size="sm">
                Try Again
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/dashboard/batches">Back to Batches</Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!data?.id) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center space-y-2">
            <p className="text-muted-foreground">Batch not found</p>
            <Button asChild variant="outline" size="sm">
              <Link href="/dashboard/batches">Back to Batches</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Data is already the batch object with keywords array
  const batch = data
  const keywords = data.keywords || []
  const progress = batch.totalKeywords > 0 
    ? Math.round((batch.completedKeywords / batch.totalKeywords) * 100) 
    : 0

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" asChild>
          <Link href="/dashboard/batches">
            <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
            </svg>
            Back to Batches
          </Link>
        </Button>
      </div>

      {/* Batch Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                {batch.name}
                <StatusBadge 
                  status={mapBatchStatusToStatusBadge(batch.status)} 
                  animated={batch.status === 'processing'} 
                />
              </CardTitle>
              <CardDescription>
                Created {new Date(batch.createdAt).toLocaleDateString()} at {new Date(batch.createdAt).toLocaleTimeString()}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold">{batch.totalKeywords}</div>
              <div className="text-sm text-muted-foreground">Total Keywords</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-green-600">{batch.completedKeywords}</div>
              <div className="text-sm text-muted-foreground">Completed</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-red-600">{batch.failedKeywords}</div>
              <div className="text-sm text-muted-foreground">Failed</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold">{progress}%</div>
              <div className="text-sm text-muted-foreground">Progress</div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{batch.completedKeywords} of {batch.totalKeywords} completed</span>
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div 
                className="h-2 rounded-full bg-primary transition-all duration-300" 
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Keywords Table */}
      {keywords.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Keywords</CardTitle>
            <CardDescription>
              {keywords.length} keywords in this batch
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Keyword</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Search Engine</TableHead>
                  <TableHead>Retry Count</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {keywords.map((keyword) => (
                  <TableRow key={keyword.id}>
                    <TableCell className="font-medium">{keyword.keyword}</TableCell>
                    <TableCell>
                      <StatusBadge 
                        status={mapBatchStatusToStatusBadge(keyword.status)} 
                        size="sm"
                        animated={keyword.status === 'processing'}
                      />
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="text-xs">
                        {keyword.searchEngine.toUpperCase()}
                      </Badge>
                    </TableCell>
                    <TableCell>{keyword.retryCount}</TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {new Date(keyword.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      {keyword.status === 'completed' ? (
                        <Button
                          asChild
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0"
                        >
                          <Link href={`/dashboard/keywords/${keyword.id}/result`}>
                            <Eye className="h-4 w-4" />
                            <span className="sr-only">View search result details</span>
                          </Link>
                        </Button>
                      ) : (
                        <div className="h-8 w-8 flex items-center justify-center">
                          <span className="text-xs text-muted-foreground">-</span>
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  )
}