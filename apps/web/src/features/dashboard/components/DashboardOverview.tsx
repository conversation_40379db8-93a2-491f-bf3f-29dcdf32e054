'use client'

import * as React from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle, StatusBadge, Icon, Button } from '@/components/ui'
import { Badge } from '@/components/ui/Badge'
import { useRecentBatches, useDashboardStats } from '@/features/keywords'
import { BatchStatus } from '@search-keywords-scraper/types'

// Map BatchStatus to StatusBadge status
function mapBatchStatusToStatusBadge(status: BatchStatus | string): "pending" | "processing" | "completed" | "error" | "cancelled" {
  // Handle both enum values and string values
  const statusString = typeof status === 'string' ? status : String(status)
  
  switch (statusString) {
    case 'pending':
    case BatchStatus.PENDING:
      return 'pending'
    case 'processing':
    case BatchStatus.PROCESSING:
      return 'processing'
    case 'completed':
    case BatchStatus.COMPLETED:
      return 'completed'
    case 'failed':
    case BatchStatus.FAILED:
      return 'error'
    default:
      return 'pending'
  }
}

export function DashboardOverview() {
  const router = useRouter()
  const { data: recentBatches, isLoading: batchesLoading, error: batchesError } = useRecentBatches(5)
  const { data: stats, isLoading: statsLoading, error: statsError } = useDashboardStats()
  
  const isLoading = batchesLoading || statsLoading
  const error = batchesError || statsError

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-muted rounded animate-pulse w-24" />
                <div className="h-4 w-4 bg-muted rounded animate-pulse" />
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-muted rounded animate-pulse w-16 mb-2" />
                <div className="h-3 bg-muted rounded animate-pulse w-32" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Card>
          <CardHeader>
            <div className="h-6 bg-muted rounded animate-pulse w-32 mb-2" />
            <div className="h-4 bg-muted rounded animate-pulse w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-16 bg-muted rounded animate-pulse" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center space-y-2">
            <p className="text-destructive">Failed to load dashboard data</p>
            <p className="text-sm text-muted-foreground">{error.message}</p>
            <Button onClick={() => window.location.reload()} variant="outline" size="sm">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const isEmpty = Boolean(!recentBatches || (Array.isArray(recentBatches) && recentBatches.length === 0))

  return (
    <div className="space-y-6">
      {/* Upload Button - prominently placed */}
      {!isEmpty && (
        <div className="flex justify-end">
          <Button asChild>
            <Link href="/upload">
              <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
              </svg>
              Upload Keywords
            </Link>
          </Button>
        </div>
      )}

      {!!isEmpty ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="mx-auto w-24 h-24 text-muted-foreground">
                <svg fill="none" viewBox="0 0 24 24" strokeWidth="1" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                </svg>
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">No keyword batches yet</h3>
                <p className="text-muted-foreground">Get started by uploading your first CSV file with keywords to scrape.</p>
              </div>
              <Button asChild size="lg">
                <Link href="/upload">
                  <svg className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
                  </svg>
                  Upload Your First CSV
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : null}

      {!isEmpty && stats && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Keywords</CardTitle>
              <Icon name="keywords" className="text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.totalKeywords?.toLocaleString() || 0}</div>
              <p className="text-xs text-muted-foreground">
                Across {stats?.totalBatches || 0} batch{(stats?.totalBatches || 0) !== 1 ? 'es' : ''}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Processing</CardTitle>
              <StatusBadge status="processing" size="sm" animated={(stats.processingKeywords || 0) > 0} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.processingKeywords || 0}</div>
              <p className="text-xs text-muted-foreground">Currently being scraped</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <StatusBadge status="completed" size="sm" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.completedKeywords?.toLocaleString() || 0}</div>
              <p className="text-xs text-muted-foreground">Successfully scraped</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Search Results</CardTitle>
              <Icon name="reports" className="text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalSearchResults || 0}</div>
              <p className="text-xs text-muted-foreground">
                {(stats.failedKeywords || 0) > 0 ? `${stats.failedKeywords} failed` : 'Successfully scraped'}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Recent Activity */}
      {!isEmpty && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Recent Batches</CardTitle>
              <CardDescription>Latest keyword scraping batches</CardDescription>
            </div>
            {recentBatches && recentBatches.length >= 5 && (
              <Button variant="ghost" size="sm" asChild>
                <Link href="/dashboard/batches">View All</Link>
              </Button>
            )}
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentBatches?.map((batch) => {
                const progress = batch.totalKeywords > 0 
                  ? Math.round((batch.completedKeywords / batch.totalKeywords) * 100) 
                  : 0

                return (
                  <div
                    key={batch.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
                    onClick={() => router.push(`/dashboard/batches/${batch.id}`)}
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="font-medium">{batch.name}</p>
                        <Badge variant="secondary" className="text-xs">
                          {batch.totalKeywords} keywords
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {batch.status === 'completed' && `Completed ${batch.completedKeywords} keywords`}
                        {batch.status === 'processing' && `${batch.completedKeywords} of ${batch.totalKeywords} completed (${progress}%)`}
                        {batch.status === 'pending' && 'Waiting to start processing'}
                        {batch.status === 'failed' && `Failed after ${batch.completedKeywords} keywords`}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(batch.createdAt).toLocaleDateString()} at {new Date(batch.createdAt).toLocaleTimeString()}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      {batch.status === 'processing' && (
                        <div className="text-xs text-muted-foreground">{progress}%</div>
                      )}
                      <StatusBadge 
                        status={mapBatchStatusToStatusBadge(batch.status)} 
                        animated={batch.status === 'processing'} 
                      />
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}