import Papa from 'papaparse'

export interface CsvParseResult {
  keywords: string[]
  totalRows: number
  errors: string[]
}

export interface CsvColumn {
  index: number
  name: string
  sampleValues: string[]
}

const KEYWORD_COLUMN_NAMES = [
  'keyword', 'keywords', 'search term', 'search_term', 'query', 'term', 'phrase'
]

const MAX_KEYWORDS = 100
const MIN_KEYWORD_LENGTH = 1
const MAX_KEYWORD_LENGTH = 500

/**
 * Parse CSV file and extract keywords with validation
 */
export function parseCsvFile(file: File): Promise<CsvParseResult> {
  return new Promise((resolve) => {
    const result: CsvParseResult = {
      keywords: [],
      totalRows: 0,
      errors: []
    }

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      encoding: 'UTF-8',
      transformHeader: (header: string) => header.toLowerCase().trim(),
      complete: (parseResult) => {
        try {
          // Check for parsing errors
          if (parseResult.errors.length > 0) {
            result.errors.push(...parseResult.errors.map(err => `Row ${(err.row ?? 0) + 1}: ${err.message}`))
          }

          const data = parseResult.data as Array<Record<string, string>>
          result.totalRows = data.length

          if (data.length === 0) {
            result.errors.push('CSV file is empty or contains no data rows')
            resolve(result)
            return
          }

          // Detect keyword column
          const headers = Object.keys(data[0] || {})
          const keywordColumn = detectKeywordColumn(headers, data)

          if (!keywordColumn) {
            result.errors.push(
              `No keyword column found. Expected one of: ${KEYWORD_COLUMN_NAMES.join(', ')}. Found: ${headers.join(', ')}`
            )
            resolve(result)
            return
          }

          // Extract and validate keywords
          const keywords = new Set<string>() // Use Set for deduplication
          let rowIndex = 0

          for (const row of data) {
            rowIndex++
            const keyword = (row[keywordColumn] || '').toString().trim()

            if (!keyword) {
              continue // Skip empty keywords
            }

            // Validate keyword length
            if (keyword.length < MIN_KEYWORD_LENGTH) {
              result.errors.push(`Row ${rowIndex}: Keyword too short (minimum ${MIN_KEYWORD_LENGTH} characters)`)
              continue
            }

            if (keyword.length > MAX_KEYWORD_LENGTH) {
              result.errors.push(`Row ${rowIndex}: Keyword too long (maximum ${MAX_KEYWORD_LENGTH} characters)`)
              continue
            }

            keywords.add(keyword)

            // Check max keywords limit
            if (keywords.size > MAX_KEYWORDS) {
              result.errors.push(`Too many keywords. Maximum ${MAX_KEYWORDS} allowed, found ${keywords.size}`)
              break
            }
          }

          result.keywords = Array.from(keywords)

          // Final validation
          if (result.keywords.length === 0) {
            result.errors.push('No valid keywords found in the CSV file')
          }

          resolve(result)
        } catch (error) {
          result.errors.push(`Failed to parse CSV: ${error instanceof Error ? error.message : 'Unknown error'}`)
          resolve(result)
        }
      },
      error: (error) => {
        result.errors.push(`CSV parsing failed: ${error.message}`)
        resolve(result)
      }
    })
  })
}

/**
 * Detect which column contains keywords
 */
function detectKeywordColumn(headers: string[], data: Array<Record<string, string>>): string | null {
  // First try exact matches with common keyword column names
  for (const header of headers) {
    if (KEYWORD_COLUMN_NAMES.includes(header.toLowerCase())) {
      return header
    }
  }

  // Try partial matches
  for (const header of headers) {
    if (KEYWORD_COLUMN_NAMES.some(name => header.toLowerCase().includes(name))) {
      return header
    }
  }

  // If no obvious match, look for columns with text data that looks like keywords
  for (const header of headers) {
    const sampleValues = data.slice(0, 5).map(row => (row[header] || '').toString().trim()).filter(Boolean)
    
    if (sampleValues.length > 0) {
      // Check if values look like keywords (not numbers, not URLs, reasonable length)
      const looksLikeKeywords = sampleValues.every(value => {
        return value.length >= MIN_KEYWORD_LENGTH && 
               value.length <= MAX_KEYWORD_LENGTH && 
               !value.match(/^https?:\/\//) && // Not a URL
               !value.match(/^\d+$/) // Not just numbers
      })

      if (looksLikeKeywords) {
        return header
      }
    }
  }

  return null
}

/**
 * Get column information for preview
 */
export function getCsvColumns(file: File): Promise<CsvColumn[]> {
  return new Promise((resolve) => {
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      preview: 10, // Only parse first 10 rows for preview
      transformHeader: (header: string) => header.toLowerCase().trim(),
      complete: (parseResult) => {
        const data = parseResult.data as Array<Record<string, string>>
        const headers = Object.keys(data[0] || {})
        
        const columns: CsvColumn[] = headers.map((header, index) => ({
          index,
          name: header,
          sampleValues: data.slice(0, 3).map(row => (row[header] || '').toString().trim()).filter(Boolean)
        }))

        resolve(columns)
      },
      error: () => {
        resolve([])
      }
    })
  })
}

/**
 * Validate a single keyword
 */
export function validateKeyword(keyword: string): { valid: boolean; error?: string } {
  const trimmed = keyword.trim()
  
  if (!trimmed) {
    return { valid: false, error: 'Keyword cannot be empty' }
  }
  
  if (trimmed.length < MIN_KEYWORD_LENGTH) {
    return { valid: false, error: `Keyword must be at least ${MIN_KEYWORD_LENGTH} characters` }
  }
  
  if (trimmed.length > MAX_KEYWORD_LENGTH) {
    return { valid: false, error: `Keyword must be no more than ${MAX_KEYWORD_LENGTH} characters` }
  }
  
  return { valid: true }
}

/**
 * Validate keywords array
 */
export function validateKeywords(keywords: string[]): { valid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (keywords.length === 0) {
    errors.push('At least one keyword is required')
  }
  
  if (keywords.length > MAX_KEYWORDS) {
    errors.push(`Maximum ${MAX_KEYWORDS} keywords allowed, found ${keywords.length}`)
  }
  
  // Remove duplicates and validate each
  const uniqueKeywords = new Set<string>()
  keywords.forEach((keyword, index) => {
    const validation = validateKeyword(keyword)
    if (!validation.valid) {
      errors.push(`Keyword ${index + 1}: ${validation.error}`)
    } else {
      uniqueKeywords.add(keyword.trim())
    }
  })
  
  if (uniqueKeywords.size !== keywords.length) {
    errors.push(`Found ${keywords.length - uniqueKeywords.size} duplicate keywords`)
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}