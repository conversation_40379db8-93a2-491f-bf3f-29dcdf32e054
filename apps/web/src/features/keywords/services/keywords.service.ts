import { apiClient } from '@/lib/api-client';

export interface KeywordWithResults {
  id: string;
  keyword: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  retryCount: number;
  searchEngine: 'google' | 'bing';
  createdAt: string;
  updatedAt: string;
  batch: {
    id: string;
    name: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
  };
  searchResult: {
    id: string;
    totalAds: number;
    totalLinks: number;
    scrapedAt: string;
  } | null;
}

export interface KeywordsListResponse {
  keywords: KeywordWithResults[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: {
    search: string | null;
    status: string | null;
    batchId: string | null;
    searchEngine: string | null;
  };
}

export interface KeywordsListParams {
  search?: string;
  page?: number;
  limit?: number;
  status?: 'pending' | 'processing' | 'completed' | 'failed';
  batchId?: string;
  searchEngine?: 'google' | 'bing';
}

export const keywordsService = {
  /**
   * Fetch paginated keywords with optional search and filters
   */
  async fetchKeywords(params: KeywordsListParams = {}): Promise<KeywordsListResponse> {
    const searchParams = new URLSearchParams();
    
    if (params.search?.trim()) searchParams.append('search', params.search.trim());
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.status) searchParams.append('status', params.status);
    if (params.batchId) searchParams.append('batchId', params.batchId);
    if (params.searchEngine) searchParams.append('searchEngine', params.searchEngine);

    const queryString = searchParams.toString();
    const url = `/keywords${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response;
  },

  /**
   * Search keywords with fuzzy matching
   */
  async searchKeywords(searchQuery: string, filters: Omit<KeywordsListParams, 'search'> = {}): Promise<KeywordsListResponse> {
    return this.fetchKeywords({ ...filters, search: searchQuery });
  },
};