'use client'

import * as React from "react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/Button"
import { ProgressIndicator } from "@/components/ui/ProgressIndicator"

interface CsvUploadZoneProps extends React.HTMLAttributes<HTMLDivElement> {
  onFileSelect: (file: File) => void
  loading?: boolean
  error?: string
  disabled?: boolean
}

export function CsvUploadZone({ 
  className, 
  onFileSelect, 
  loading = false,
  error,
  disabled = false,
  ...props 
}: CsvUploadZoneProps) {
  const [isDragOver, setIsDragOver] = React.useState(false)
  const fileInputRef = React.useRef<HTMLInputElement>(null)

  const handleDragOver = React.useCallback((e: React.DragEvent) => {
    e.preventDefault()
    if (!disabled && !loading) {
      setIsDragOver(true)
    }
  }, [disabled, loading])

  const handleDragLeave = React.useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = React.useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)

    if (disabled || loading) return

    const files = Array.from(e.dataTransfer.files)
    const file = files[0]

    if (file && validateFile(file)) {
      onFileSelect(file)
    }
  }, [disabled, loading, onFileSelect])

  const handleFileInputChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file && validateFile(file)) {
      onFileSelect(file)
    }
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [onFileSelect])

  const validateFile = (file: File): boolean => {
    const maxSize = 5 * 1024 * 1024 // 5MB
    
    // Check file size
    if (file.size > maxSize) {
      console.error(`File size exceeds 5MB limit`)
      return false
    }

    // Check file type
    if (!file.type.includes('csv') && !file.name.toLowerCase().endsWith('.csv')) {
      console.error(`Only CSV files are allowed`)
      return false
    }

    return true
  }

  const handleClick = () => {
    if (!disabled && !loading) {
      fileInputRef.current?.click()
    }
  }

  return (
    <div
      className={cn(
        "relative border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer",
        "hover:border-primary/50 hover:bg-primary/5",
        "focus-within:border-primary focus-within:ring-2 focus-within:ring-primary/20",
        isDragOver && "border-primary bg-primary/10",
        disabled && "opacity-50 cursor-not-allowed",
        loading && "opacity-75 cursor-wait",
        error && "border-destructive bg-destructive/5",
        !error && !disabled && "border-border",
        className
      )}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={handleClick}
      {...props}
    >
      <input
        ref={fileInputRef}
        type="file"
        accept=".csv,text/csv"
        onChange={handleFileInputChange}
        disabled={disabled || loading}
        className="sr-only"
        aria-describedby={error ? "upload-error" : undefined}
      />

      <div className="space-y-4">
        {/* Upload Icon */}
        <div className="mx-auto w-16 h-16 text-muted-foreground">
          {loading ? (
            <svg className="animate-spin w-full h-full" fill="none" viewBox="0 0 24 24">
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
          ) : (
            <svg fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5"
              />
            </svg>
          )}
        </div>

        {/* Upload Text */}
        <div className="space-y-2">
          <p className="text-lg font-medium">
            {loading ? "Processing CSV..." : "Drop your CSV file here"}
          </p>
          <p className="text-sm text-muted-foreground">
            or{" "}
            <Button
              variant="link"
              size="sm"
              className="p-0 h-auto font-medium text-primary"
              disabled={disabled || loading}
              type="button"
            >
              browse files
            </Button>
          </p>
          <p className="text-xs text-muted-foreground">
            CSV files up to 5MB • 1-100 keywords per batch
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div id="upload-error" className="text-sm text-destructive font-medium bg-destructive/10 p-3 rounded-md border border-destructive/20">
            {error}
          </div>
        )}
      </div>
    </div>
  )
}