'use client'

import * as React from 'react'
import { useRouter } from 'next/navigation'
import { CsvUploadZone } from './CsvUploadZone'
import { KeywordsPreview } from './KeywordsPreview'
import { Batch<PERSON>reationForm } from './BatchCreationForm'
import { <PERSON><PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { useCsvParsing } from '../hooks/useCsvParsing'
import { useCreateKeywordBatch, type CreateBatchRequest } from '../hooks/useKeywordBatch'
import { type BatchCreationFormData } from '@/lib/validations/UploadSchemas'

enum UploadStep {
  UPLOAD = 'upload',
  PREVIEW = 'preview',
  CONFIGURE = 'configure',
  SUCCESS = 'success'
}

export function KeywordUploadFlow() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = React.useState<UploadStep>(UploadStep.UPLOAD)
  const [selectedFile, setSelectedFile] = React.useState<File | null>(null)
  const [processedKeywords, setProcessedKeywords] = React.useState<string[]>([])

  const csvParsing = useCsvParsing()
  const createBatch = useCreateKeywordBatch()

  // Handle file upload
  const handleFileSelect = async (file: File) => {
    setSelectedFile(file)
    await csvParsing.parseFile(file)
  }

  // Handle CSV parsing result
  React.useEffect(() => {
    if (csvParsing.result && csvParsing.result.keywords.length > 0) {
      setProcessedKeywords(csvParsing.result.keywords)
      setCurrentStep(UploadStep.PREVIEW)
    }
  }, [csvParsing.result])

  // Handle keyword removal in preview
  const handleRemoveKeyword = (index: number) => {
    setProcessedKeywords(prev => prev.filter((_, i) => i !== index))
  }

  // Handle proceeding to configuration
  const handleProceedToConfig = () => {
    setCurrentStep(UploadStep.CONFIGURE)
  }

  // Handle batch creation
  const handleCreateBatch = async (data: BatchCreationFormData) => {
    try {
      const request: CreateBatchRequest = {
        name: data.name,
        keywords: data.keywords,
        searchEngine: data.searchEngine
      }

      const response = await createBatch.mutateAsync(request)
      
      // Redirect to the batch progress page
      router.push(`/dashboard/batches/${response.batch.id}`)
    } catch (error) {
      console.error('Failed to create batch:', error)
      // Error handling is managed by the mutation hook
    }
  }

  // Handle restarting the flow
  const handleRestart = () => {
    setCurrentStep(UploadStep.UPLOAD)
    setSelectedFile(null)
    setProcessedKeywords([])
    csvParsing.reset()
  }

  // Handle going back to previous step
  const handleBack = () => {
    switch (currentStep) {
      case UploadStep.PREVIEW:
        setCurrentStep(UploadStep.UPLOAD)
        break
      case UploadStep.CONFIGURE:
        setCurrentStep(UploadStep.PREVIEW)
        break
    }
  }

  return (
    <div className="space-y-6">
      {/* Progress Indicator */}
      <div className="flex items-center justify-center space-x-4 mb-8">
        {Object.values(UploadStep).slice(0, -1).map((step, index) => {
          const stepIndex = index + 1
          const isActive = currentStep === step
          const isCompleted = Object.values(UploadStep).indexOf(currentStep) > index
          
          return (
            <React.Fragment key={step}>
              <div className="flex items-center">
                <div
                  className={`
                    flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
                    ${isActive 
                      ? 'border-primary bg-primary text-primary-foreground' 
                      : isCompleted
                        ? 'border-green-500 bg-green-500 text-white'
                        : 'border-muted-foreground bg-muted text-muted-foreground'
                    }
                  `}
                >
                  {isCompleted ? (
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" strokeWidth="2" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                    </svg>
                  ) : (
                    <span className="text-sm font-medium">{stepIndex}</span>
                  )}
                </div>
                <div className="ml-3 text-sm">
                  <p className={`font-medium ${isActive ? 'text-primary' : isCompleted ? 'text-green-600' : 'text-muted-foreground'}`}>
                    {step === UploadStep.UPLOAD && 'Upload'}
                    {step === UploadStep.PREVIEW && 'Preview'}
                    {step === UploadStep.CONFIGURE && 'Configure'}
                  </p>
                </div>
              </div>
              {index < Object.values(UploadStep).length - 2 && (
                <div className={`w-16 h-0.5 ${isCompleted ? 'bg-green-500' : 'bg-muted'}`} />
              )}
            </React.Fragment>
          )
        })}
      </div>

      {/* Step Content */}
      {currentStep === UploadStep.UPLOAD && (
        <Card>
          <CardHeader>
            <CardTitle>Upload CSV File</CardTitle>
            <CardDescription>
              Select a CSV file containing keywords to scrape. The file should have a column with keywords (one per row).
            </CardDescription>
          </CardHeader>
          <CardContent>
            <CsvUploadZone
              onFileSelect={handleFileSelect}
              loading={csvParsing.isLoading}
              error={csvParsing.error || undefined}
            />
            
            {selectedFile && !csvParsing.isLoading && !csvParsing.error && (
              <div className="mt-4 p-4 bg-muted/50 rounded-lg border">
                <div className="flex items-center gap-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                    <svg className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m6.75 18H3.75V4.5h11.25c.621 0 1.125.504 1.125 1.125v13.5c0 .621-.504 1.125-1.125 1.125z" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-medium">{selectedFile.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB • Processing...
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="mt-6 rounded-lg border bg-muted/50 p-4">
              <h4 className="font-medium mb-2">CSV Format Requirements:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• CSV format with header row</li>
                <li>• Must contain a column with keywords (named: keyword, keywords, search term, etc.)</li>
                <li>• 1-100 keywords per file</li>
                <li>• Each keyword: 1-500 characters</li>
                <li>• Maximum file size: 5MB</li>
                <li>• UTF-8 encoding recommended</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      )}

      {currentStep === UploadStep.PREVIEW && (
        <div className="space-y-6">
          <KeywordsPreview
            keywords={processedKeywords}
            totalRows={csvParsing.result?.totalRows}
            errors={csvParsing.result?.errors}
            onRemoveKeyword={handleRemoveKeyword}
          />
          
          <div className="flex justify-between">
            <Button variant="outline" onClick={handleBack}>
              Back to Upload
            </Button>
            <Button 
              onClick={handleProceedToConfig}
              disabled={processedKeywords.length === 0}
            >
              Continue to Configuration
            </Button>
          </div>
        </div>
      )}

      {currentStep === UploadStep.CONFIGURE && (
        <div className="space-y-6">
          <BatchCreationForm
            keywords={processedKeywords}
            onSubmit={handleCreateBatch}
            loading={createBatch.isPending}
            error={createBatch.error?.message}
          />
          
          <div className="flex justify-between">
            <Button variant="outline" onClick={handleBack}>
              Back to Preview
            </Button>
            <Button variant="outline" onClick={handleRestart}>
              Start Over
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}