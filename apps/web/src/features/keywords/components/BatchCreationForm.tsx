'use client'

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { SearchEngine } from "@search-keywords-scraper/types"
import { <PERSON><PERSON> } from "@/components/ui/Button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/Form"
import { Input } from "@/components/ui/Input"
import { batchCreationSchema, type BatchCreationFormData } from "@/lib/validations/UploadSchemas"
import { cn } from "@/lib/utils"

interface BatchCreationFormProps {
  keywords: string[]
  onSubmit: (data: BatchCreationFormData) => Promise<void>
  loading?: boolean
  error?: string
  className?: string
}

export function BatchCreationForm({ 
  keywords, 
  onSubmit, 
  loading = false, 
  error,
  className 
}: BatchCreationFormProps) {
  const form = useForm({
    resolver: zodResolver(batchCreationSchema),
    defaultValues: {
      name: `Keywords Batch ${new Date().toLocaleDateString()}`,
      searchEngine: SearchEngine.GOOGLE,
      keywords: keywords,
    },
  })

  // Update keywords when prop changes
  React.useEffect(() => {
    form.setValue('keywords', keywords)
  }, [keywords, form])

  const handleFormSubmit = async (data: BatchCreationFormData) => {
    try {
      await onSubmit(data)
    } catch {
      // Error handling is done by parent component
    }
  }

  const watchedSearchEngine = form.watch('searchEngine')

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle>Create Keyword Batch</CardTitle>
        <CardDescription>
          Configure your keyword batch settings before starting the scraping process.
        </CardDescription>
      </CardHeader>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleFormSubmit)}>
          <CardContent className="space-y-6">
            {/* Global Error */}
            {error && (
              <div className="p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md">
                {error}
              </div>
            )}

            {/* Batch Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Batch Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter a name for this batch..."
                      disabled={loading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Search Engine Selection */}
            <FormField
              control={form.control}
              name="searchEngine"
              render={({ field }) => {
                // Debug logging to understand the issue
                console.log('Current field value:', field.value)
                console.log('SearchEngine.GOOGLE:', SearchEngine.GOOGLE)
                console.log('SearchEngine.BING:', SearchEngine.BING)
                
                return (
                  <FormItem>
                    <FormLabel>Search Engine</FormLabel>
                    <FormControl>
                      <div className="grid grid-cols-2 gap-3">
                        <button
                          type="button"
                          className={cn(
                            "flex items-center gap-3 p-4 border-2 rounded-lg transition-all duration-200",
                            "hover:border-primary/50 hover:bg-primary/5 focus:outline-none focus:ring-2 focus:ring-primary/20",
                            field.value === SearchEngine.GOOGLE 
                              ? "border-primary bg-primary/10 text-primary shadow-sm ring-1 ring-primary/20" 
                              : "border-muted-foreground/30 hover:border-primary/30"
                          )}
                          onClick={() => {
                            console.log('Clicking Google, setting to:', SearchEngine.GOOGLE)
                            field.onChange(SearchEngine.GOOGLE)
                          }}
                          disabled={loading}
                          aria-pressed={field.value === SearchEngine.GOOGLE}
                        >
                          <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm">
                            <span className="text-sm font-bold text-blue-600">G</span>
                          </div>
                          <div className="text-left">
                            <p className="font-medium text-sm">Google</p>
                            <p className="text-xs text-muted-foreground">Most comprehensive results</p>
                          </div>
                          {field.value === SearchEngine.GOOGLE && (
                            <div className="ml-auto">
                              <svg className="w-4 h-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                              </svg>
                            </div>
                          )}
                        </button>
                        
                        <button
                          type="button"
                          className={cn(
                            "flex items-center gap-3 p-4 border-2 rounded-lg transition-all duration-200",
                            "hover:border-primary/50 hover:bg-primary/5 focus:outline-none focus:ring-2 focus:ring-primary/20",
                            field.value === SearchEngine.BING 
                              ? "border-primary bg-primary/10 text-primary shadow-sm ring-1 ring-primary/20" 
                              : "border-muted-foreground/30 hover:border-primary/30"
                          )}
                          onClick={() => {
                            console.log('Clicking Bing, setting to:', SearchEngine.BING)
                            field.onChange(SearchEngine.BING)
                          }}
                          disabled={loading}
                          aria-pressed={field.value === SearchEngine.BING}
                        >
                          <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm">
                            <span className="text-sm font-bold text-orange-600">B</span>
                          </div>
                          <div className="text-left">
                            <p className="font-medium text-sm">Bing</p>
                            <p className="text-xs text-muted-foreground">Microsoft search engine</p>
                          </div>
                          {field.value === SearchEngine.BING && (
                            <div className="ml-auto">
                              <svg className="w-4 h-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                              </svg>
                            </div>
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                    {/* Debug info - remove in production */}
                    <div className="text-xs text-muted-foreground mt-2">
                      Current selection: {field.value} | Type: {typeof field.value}
                    </div>
                  </FormItem>
                )
              }}
            />

            {/* Batch Summary */}
            <div className="rounded-lg border bg-muted/50 p-4 space-y-2">
              <h4 className="font-medium text-sm">Batch Summary:</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Keywords:</span>
                  <span className="ml-2 font-medium">{keywords.length}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">Search Engine:</span>
                  <span className="ml-2 font-medium capitalize">{watchedSearchEngine}</span>
                </div>
              </div>
              <div className="text-xs text-muted-foreground pt-2 border-t">
                <p>Each keyword will be scraped for ads count, links count, and HTML content.</p>
                <p>Processing time: ~{Math.ceil(keywords.length * 2.5)} seconds estimated</p>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end pt-4">
              <Button 
                type="submit" 
                disabled={loading || keywords.length === 0}
                size="lg"
                className="min-w-40"
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                      <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                    </svg>
                    Creating Batch...
                  </>
                ) : (
                  <>
                    <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.348a1.125 1.125 0 010 1.971l-11.54 6.347a1.125 1.125 0 01-1.667-.985V5.653z" />
                    </svg>
                    Start Scraping
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </form>
      </Form>
    </Card>
  )
}