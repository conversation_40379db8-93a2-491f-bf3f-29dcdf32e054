'use client';

import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Search, Filter, ExternalLink, RefreshCw, Database, Clock } from 'lucide-react';
import { useDebounce } from '@/hooks/useDebounce';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Button,
  Input,
  Badge,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui';
import { keywordsService, type KeywordsListParams, type KeywordWithResults } from '../services/keywords.service';
import Link from 'next/link';

interface KeywordsListProps {
  initialBatchId?: string;
}

export function KeywordsList({ initialBatchId }: KeywordsListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState<Omit<KeywordsListParams, 'search' | 'page'>>({
    limit: 20,
    batchId: initialBatchId,
  });

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Query for keywords data
  const {
    data: keywordsData,
    isLoading,
    isError,
    refetch,
    isFetching
  } = useQuery({
    queryKey: ['keywords', debouncedSearchTerm, page, filters],
    queryFn: () => keywordsService.fetchKeywords({
      search: debouncedSearchTerm || undefined,
      page,
      ...filters,
    }),
    keepPreviousData: true,
  });

  // Reset page when search or filters change
  useEffect(() => {
    setPage(1);
  }, [debouncedSearchTerm, filters]);

  const getStatusBadge = (status: KeywordWithResults['status']) => {
    const statusConfig = {
      pending: { variant: 'secondary' as const, label: 'Pending' },
      processing: { variant: 'default' as const, label: 'Processing' },
      completed: { variant: 'success' as const, label: 'Completed' },
      failed: { variant: 'destructive' as const, label: 'Failed' },
    };
    
    const config = statusConfig[status];
    return (
      <Badge variant={config.variant}>
        {config.label}
      </Badge>
    );
  };

  const getSearchEngineBadge = (engine: KeywordWithResults['searchEngine']) => {
    return (
      <Badge variant="outline" className="capitalize">
        {engine}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleFilterChange = (key: string, value: string) => {
    if (value === 'all' || value === '') {
      const { [key]: removed, ...newFilters } = filters;
      void removed; // Acknowledge we're intentionally ignoring the removed value
      setFilters(newFilters);
    } else {
      setFilters(prev => ({ ...prev, [key]: value }));
    }
  };

  const clearFilters = () => {
    setSearchTerm('');
    setFilters({ limit: 20 });
    setPage(1);
  };

  const pagination = keywordsData?.pagination;
  const keywords = keywordsData?.keywords || [];

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search & Filter Keywords
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search keywords or batch names..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9"
            />
          </div>

          {/* Filters Row */}
          <div className="flex flex-wrap gap-4">
            <div className="min-w-[150px]">
              <select 
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={filters.status || 'all'}
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <option value="all">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="processing">Processing</option>
                <option value="completed">Completed</option>
                <option value="failed">Failed</option>
              </select>
            </div>

            <div className="min-w-[150px]">
              <select 
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={filters.searchEngine || 'all'}
                onChange={(e) => handleFilterChange('searchEngine', e.target.value)}
              >
                <option value="all">All Engines</option>
                <option value="google">Google</option>
                <option value="bing">Bing</option>
              </select>
            </div>

            <Button
              variant="outline"
              onClick={clearFilters}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Clear Filters
            </Button>

            <Button
              variant="outline"
              onClick={() => refetch()}
              disabled={isFetching}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isFetching ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>

          {/* Applied Filters Summary */}
          {(debouncedSearchTerm || filters.status || filters.searchEngine) && (
            <div className="flex flex-wrap gap-2 pt-2 border-t">
              {debouncedSearchTerm && (
                <Badge variant="secondary">
                  Search: &ldquo;{debouncedSearchTerm}&rdquo;
                </Badge>
              )}
              {filters.status && (
                <Badge variant="secondary">
                  Status: {filters.status}
                </Badge>
              )}
              {filters.searchEngine && (
                <Badge variant="secondary">
                  Engine: {filters.searchEngine}
                </Badge>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Results Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Keywords Results
              {pagination && (
                <Badge variant="outline">
                  {pagination.total} total
                </Badge>
              )}
            </div>
            {isLoading && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <RefreshCw className="h-4 w-4 animate-spin" />
                Loading...
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isError ? (
            <div className="text-center py-8 text-red-600">
              <p>Error loading keywords. Please try again.</p>
              <Button variant="outline" onClick={() => refetch()} className="mt-2">
                Retry
              </Button>
            </div>
          ) : keywords.length === 0 && !isLoading ? (
            <div className="text-center py-8 text-muted-foreground">
              <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No keywords found matching your search.</p>
              <p className="text-sm">Try adjusting your search terms or filters.</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Keyword</TableHead>
                    <TableHead>Batch</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Engine</TableHead>
                    <TableHead>Results</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {keywords.map((keyword) => (
                    <TableRow key={keyword.id}>
                      <TableCell className="font-medium">
                        <div className="max-w-xs truncate" title={keyword.keyword}>
                          {keyword.keyword}
                        </div>
                        {keyword.retryCount > 0 && (
                          <div className="text-xs text-muted-foreground mt-1">
                            Retry: {keyword.retryCount}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <Link
                          href={`/dashboard/batches/${keyword.batch.id}`}
                          className="text-blue-600 hover:text-blue-800 hover:underline"
                        >
                          <div className="max-w-xs truncate" title={keyword.batch.name}>
                            {keyword.batch.name}
                          </div>
                        </Link>
                        <div className="text-xs text-muted-foreground mt-1">
                          {getStatusBadge(keyword.batch.status)}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(keyword.status)}
                      </TableCell>
                      <TableCell>
                        {getSearchEngineBadge(keyword.searchEngine)}
                      </TableCell>
                      <TableCell>
                        {keyword.searchResult ? (
                          <div className="text-sm">
                            <div>Ads: {keyword.searchResult.totalAds}</div>
                            <div>Links: {keyword.searchResult.totalLinks}</div>
                            <div className="text-xs text-muted-foreground">
                              <Clock className="h-3 w-3 inline mr-1" />
                              {formatDate(keyword.searchResult.scrapedAt)}
                            </div>
                          </div>
                        ) : (
                          <div className="text-sm text-muted-foreground">
                            No results yet
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {formatDate(keyword.createdAt)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {keyword.searchResult && (
                            <Link href={`/dashboard/search-results/${keyword.searchResult.id}`}>
                              <Button size="sm" variant="outline">
                                <ExternalLink className="h-4 w-4" />
                              </Button>
                            </Link>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="flex items-center justify-between pt-6">
              <div className="text-sm text-muted-foreground">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                {pagination.total} results
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination.hasPrev}
                  onClick={() => setPage(page - 1)}
                >
                  Previous
                </Button>
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    const pageNum = Math.max(1, Math.min(
                      pagination.totalPages - 4,
                      pagination.page - 2
                    )) + i;
                    
                    if (pageNum > pagination.totalPages) return null;
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={pageNum === pagination.page ? "default" : "outline"}
                        size="sm"
                        onClick={() => setPage(pageNum)}
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination.hasNext}
                  onClick={() => setPage(page + 1)}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}