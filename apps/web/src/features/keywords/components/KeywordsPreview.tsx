'use client'

import * as React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card"
import { Badge } from "@/components/ui/Badge"
import { Button } from "@/components/ui/Button"
import { Table } from "@/components/ui/Table"

interface KeywordsPreviewProps {
  keywords: string[]
  totalRows?: number
  errors?: string[]
  onRemoveKeyword?: (index: number) => void
  className?: string
}

export function KeywordsPreview({
  keywords,
  totalRows,
  errors = [],
  onRemoveKeyword,
  className
}: KeywordsPreviewProps) {
  const [showAll, setShowAll] = React.useState(false)
  
  const displayKeywords = showAll ? keywords : keywords.slice(0, 10)
  const hasMore = keywords.length > 10

  if (keywords.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Keywords Preview</CardTitle>
          <CardDescription>No keywords found</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-sm">
            No valid keywords were extracted from the CSV file.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              Keywords Preview
              <Badge variant="secondary" className="text-xs">
                {keywords.length} keyword{keywords.length !== 1 ? 's' : ''}
              </Badge>
            </CardTitle>
            <CardDescription>
              {totalRows && totalRows !== keywords.length && (
                <>Extracted {keywords.length} unique keywords from {totalRows} rows</>
              )}
              {(!totalRows || totalRows === keywords.length) && (
                <>Found {keywords.length} keyword{keywords.length !== 1 ? 's' : ''} ready for processing</>
              )}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Error Messages */}
        {errors.length > 0 && (
          <div className="space-y-2">
            <p className="text-sm font-medium text-destructive">Issues found:</p>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {errors.slice(0, 5).map((error, index) => (
                <p key={index} className="text-xs text-destructive bg-destructive/10 p-2 rounded border">
                  {error}
                </p>
              ))}
              {errors.length > 5 && (
                <p className="text-xs text-muted-foreground text-center py-1">
                  ... and {errors.length - 5} more issue{errors.length - 5 !== 1 ? 's' : ''}
                </p>
              )}
            </div>
          </div>
        )}

        {/* Keywords Table */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <p className="text-sm font-medium">Keywords:</p>
            {hasMore && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAll(!showAll)}
                className="h-auto p-1 text-xs"
              >
                {showAll ? 'Show less' : `Show all ${keywords.length}`}
              </Button>
            )}
          </div>
          
          <div className="border rounded-md">
            <Table>
              <thead>
                <tr className="border-b">
                  <th className="text-left p-3 text-sm font-medium text-muted-foreground">#</th>
                  <th className="text-left p-3 text-sm font-medium text-muted-foreground">Keyword</th>
                  <th className="text-right p-3 text-sm font-medium text-muted-foreground">Length</th>
                  {onRemoveKeyword && (
                    <th className="text-right p-3 text-sm font-medium text-muted-foreground w-16">Action</th>
                  )}
                </tr>
              </thead>
              <tbody>
                {displayKeywords.map((keyword, index) => (
                  <tr key={index} className="border-b last:border-0">
                    <td className="p-3 text-sm text-muted-foreground">{index + 1}</td>
                    <td className="p-3 text-sm font-mono max-w-xs truncate" title={keyword}>
                      {keyword}
                    </td>
                    <td className="p-3 text-sm text-muted-foreground text-right">
                      {keyword.length} char{keyword.length !== 1 ? 's' : ''}
                    </td>
                    {onRemoveKeyword && (
                      <td className="p-3 text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onRemoveKeyword(index)}
                          className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                        >
                          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </Button>
                      </td>
                    )}
                  </tr>
                ))}
              </tbody>
            </Table>
          </div>
          
          {hasMore && !showAll && (
            <p className="text-xs text-muted-foreground text-center py-2">
              ... and {keywords.length - 10} more keywords
            </p>
          )}
        </div>

        {/* Keywords Stats */}
        <div className="flex flex-wrap gap-4 text-xs text-muted-foreground bg-muted/50 p-3 rounded">
          <div>
            <span className="font-medium">Total:</span> {keywords.length}
          </div>
          <div>
            <span className="font-medium">Avg length:</span> {Math.round(keywords.reduce((sum, k) => sum + k.length, 0) / keywords.length)} chars
          </div>
          <div>
            <span className="font-medium">Range:</span> {Math.min(...keywords.map(k => k.length))}-{Math.max(...keywords.map(k => k.length))} chars
          </div>
        </div>
      </CardContent>
    </Card>
  )
}