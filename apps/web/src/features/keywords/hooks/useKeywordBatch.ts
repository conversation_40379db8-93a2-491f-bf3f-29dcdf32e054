'use client'

import * as React from 'react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '@/lib/api-client'
import { 
  KeywordBatch, 
  CreateBatchRequest, 
  CreateBatchResponse, 
  BatchProgressResponse,
  BatchWithKeywords,
  BatchesListResponse 
} from '@search-keywords-scraper/types'

// Query keys
const QUERY_KEYS = {
  batches: ['keyword-batches'] as const,
  batch: (id: string) => ['keyword-batches', id] as const,
  batchProgress: (id: string) => ['keyword-batches', id, 'progress'] as const,
  dashboardStats: ['dashboard-stats'] as const,
  recentBatches: ['recent-batches'] as const,
} as const

// API endpoints
const ENDPOINTS = {
  batches: '/api/v1/keywords/batches',
  createBatch: '/api/v1/keywords',
  batch: (id: string) => `/api/v1/keywords/batches/${id}`,
  dashboardStats: '/api/v1/dashboard/stats',
  recentBatches: '/api/v1/dashboard/recent-batches',
} as const

/**
 * Hook to fetch all keyword batches for the current user
 */
export function useKeywordBatches(params?: { page?: number; limit?: number; status?: string }) {
  const queryParams = new URLSearchParams()
  if (params?.page) queryParams.set('page', params.page.toString())
  if (params?.limit) queryParams.set('limit', params.limit.toString())
  if (params?.status) queryParams.set('status', params.status)

  return useQuery({
    queryKey: [...QUERY_KEYS.batches, params],
    queryFn: async (): Promise<BatchesListResponse> => {
      const url = queryParams.toString() 
        ? `${ENDPOINTS.batches}?${queryParams.toString()}`
        : ENDPOINTS.batches
      return apiClient.get(url)
    },
    staleTime: 30 * 1000, // 30 seconds
  })
}

/**
 * Hook to fetch a specific keyword batch with progress
 */
export function useKeywordBatch(id: string) {
  return useQuery({
    queryKey: QUERY_KEYS.batch(id),
    queryFn: async (): Promise<BatchWithKeywords> => {
      return apiClient.get(ENDPOINTS.batch(id))
    },
    enabled: !!id,
    staleTime: 5 * 1000, // 5 seconds for real-time updates
    refetchInterval: (query) => {
      // Continue polling if batch is still processing
      const data = query.state.data
      if (data?.status === 'processing' || data?.status === 'pending') {
        return 5000 // 5 seconds
      }
      return false // Stop polling when completed/failed
    },
  })
}

/**
 * Hook to create a new keyword batch
 */
export function useCreateKeywordBatch() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: CreateBatchRequest): Promise<CreateBatchResponse> => {
      return apiClient.post(ENDPOINTS.createBatch, data)
    },
    onSuccess: (response) => {
      // Invalidate and refetch batches list
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.batches })
      
      // Add the new batch to cache
      queryClient.setQueryData(
        QUERY_KEYS.batch(response.batch.id), 
        {
          batch: response.batch,
          keywords: [],
          results: []
        }
      )
    },
  })
}

/**
 * Hook to get batch progress with real-time polling
 */
export function useBatchProgress(id: string, enabled = true) {
  return useQuery({
    queryKey: QUERY_KEYS.batchProgress(id),
    queryFn: async (): Promise<BatchWithKeywords> => {
      return apiClient.get(ENDPOINTS.batch(id))
    },
    enabled: !!id && enabled,
    staleTime: 0, // Always fresh for real-time updates
    refetchInterval: (query) => {
      if (!enabled) return false
      
      // Continue polling if batch is still processing
      const data = query.state.data
      if (data?.status === 'processing' || data?.status === 'pending') {
        return 5000 // 5 seconds
      }
      return false // Stop polling when completed/failed
    },
  })
}

/**
 * Hook to get dashboard statistics from API
 */
export function useDashboardStats() {
  return useQuery({
    queryKey: QUERY_KEYS.dashboardStats,
    queryFn: async () => {
      return apiClient.get(ENDPOINTS.dashboardStats)
    },
    staleTime: 30 * 1000, // 30 seconds
  })
}

/**
 * Hook to get recent batches from API
 */
export function useRecentBatches(limit?: number) {
  return useQuery({
    queryKey: [...QUERY_KEYS.recentBatches, limit],
    queryFn: async (): Promise<KeywordBatch[]> => {
      const params = limit ? `?limit=${limit}` : ''
      return apiClient.get(`${ENDPOINTS.recentBatches}${params}`)
    },
    staleTime: 30 * 1000, // 30 seconds
  })
}

/**
 * Hook to get batch statistics (legacy - for backwards compatibility)
 */
export function useBatchStats() {
  const { data: batches } = useKeywordBatches()

  const stats = React.useMemo(() => {
    if (!batches?.batches) return null

    const batchList = batches.batches

    return {
      totalBatches: batchList.length,
      totalKeywords: batchList.reduce((sum, batch) => sum + batch.totalKeywords, 0),
      completedKeywords: batchList.reduce((sum, batch) => sum + batch.completedKeywords, 0),
      failedKeywords: batchList.reduce((sum, batch) => sum + batch.failedKeywords, 0),
      processingBatches: batchList.filter(batch => batch.status === 'processing').length,
      completedBatches: batchList.filter(batch => batch.status === 'completed').length,
      failedBatches: batchList.filter(batch => batch.status === 'failed').length,
      successRate: batchList.length > 0 
        ? Math.round((batchList.filter(batch => batch.status === 'completed').length / batchList.length) * 100)
        : 0
    }
  }, [batches])

  return stats
}

// Re-export types for convenience
export type { KeywordBatch, CreateBatchRequest, CreateBatchResponse, BatchProgressResponse, BatchWithKeywords }