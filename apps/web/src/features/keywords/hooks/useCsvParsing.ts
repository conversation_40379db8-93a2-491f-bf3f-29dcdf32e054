'use client'

import * as React from 'react'
import { parseCsvFile, type CsvParseResult } from '../services/CsvParserService'

interface UseCsvParsingState {
  result: CsvParseResult | null
  isLoading: boolean
  error: string | null
}

interface UseCsvParsingReturn extends UseCsvParsingState {
  parseFile: (file: File) => Promise<void>
  reset: () => void
}

export function useCsvParsing(): UseCsvParsingReturn {
  const [state, setState] = React.useState<UseCsvParsingState>({
    result: null,
    isLoading: false,
    error: null
  })

  const parseFile = React.useCallback(async (file: File) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      const result = await parseCsvFile(file)
      
      setState({
        result,
        isLoading: false,
        error: result.errors.length > 0 && result.keywords.length === 0 
          ? 'Failed to parse CSV file. Please check the format and try again.'
          : null
      })
    } catch (error) {
      setState({
        result: null,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to parse CSV file'
      })
    }
  }, [])

  const reset = React.useCallback(() => {
    setState({
      result: null,
      isLoading: false,
      error: null
    })
  }, [])

  return {
    ...state,
    parseFile,
    reset
  }
}