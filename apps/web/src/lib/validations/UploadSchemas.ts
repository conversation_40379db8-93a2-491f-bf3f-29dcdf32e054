import { z } from "zod"
import { SearchEngine } from "@search-keywords-scraper/types"

// File upload validation schema
export const fileUploadSchema = z.object({
  file: z
    .instanceof(File, { message: "Please select a file" })
    .refine((file) => file.size <= 5 * 1024 * 1024, {
      message: "File size must be less than 5MB",
    })
    .refine((file) => file.type === "text/csv" || file.name.endsWith(".csv"), {
      message: "Only CSV files are allowed",
    }),
})

// Keyword batch creation validation schema
export const batchCreationSchema = z.object({
  name: z
    .string()
    .min(1, "Batch name is required")
    .max(100, "Batch name must be less than 100 characters"),
  searchEngine: z
    .nativeEnum(SearchEngine, {
      message: "Please select a valid search engine"
    })
    .default(SearchEngine.GOOGLE),
  keywords: z
    .array(z.string().min(1).max(500))
    .min(1, "At least one keyword is required")
    .max(100, "Maximum 100 keywords allowed")
    .refine((keywords) => {
      const unique = new Set(keywords.map(k => k.trim().toLowerCase()))
      return unique.size === keywords.length
    }, {
      message: "Duplicate keywords are not allowed"
    }),
})

// Combined CSV upload and batch creation schema
export const csvUploadSchema = z.object({
  name: z
    .string()
    .min(1, "Batch name is required")
    .max(100, "Batch name must be less than 100 characters"),
  searchEngine: z
    .nativeEnum(SearchEngine, {
      message: "Please select a valid search engine"
    })
    .default(SearchEngine.GOOGLE),
  keywords: z
    .array(z.string().min(1).max(500))
    .min(1, "At least one keyword is required")
    .max(100, "Maximum 100 keywords allowed"),
})

// Legacy keyword batch validation schema (keep for compatibility)
export const keywordBatchSchema = z.object({
  name: z
    .string()
    .min(1, "Batch name is required")
    .max(100, "Batch name must be less than 100 characters"),
  description: z
    .string()
    .optional()
    .refine((val) => !val || val.length <= 500, {
      message: "Description must be less than 500 characters",
    }),
  priority: z
    .enum(["low", "normal", "high"], {
      message: "Please select a valid priority"
    })
    .default("normal"),
  tags: z.array(z.string()).default([]),
})

// Search/filter validation schema
export const keywordSearchSchema = z.object({
  query: z.string().optional().default(""),
  status: z.enum(["all", "pending", "processing", "completed", "error", "cancelled"]).optional().default("all"),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
  batchId: z.string().optional(),
  tags: z.array(z.string()).optional().default([]),
}).refine((data) => {
  if (data.dateFrom && data.dateTo) {
    return data.dateFrom <= data.dateTo
  }
  return true
}, {
  message: "End date must be after start date",
  path: ["dateTo"],
})

export type FileUploadFormData = z.infer<typeof fileUploadSchema>
export type BatchCreationFormData = z.infer<typeof batchCreationSchema>
export type CsvUploadFormData = z.infer<typeof csvUploadSchema>
export type KeywordBatchFormData = z.infer<typeof keywordBatchSchema>
export type KeywordSearchFormData = z.infer<typeof keywordSearchSchema>
