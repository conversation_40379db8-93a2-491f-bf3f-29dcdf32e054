'use client'

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON>, Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/Form"
import { Input } from "@/components/ui/Input"
import { FileUploadZone } from "@/components/ui/FileUploadZone"
import { ProgressIndicator } from "@/components/ui/ProgressIndicator"
import { fileUploadSchema, type FileUploadFormData } from "@/lib/validations/UploadSchemas"
import { cn } from "@/lib/utils"

interface FileUploadFormProps {
  onSubmit: (data: FileUploadFormData) => Promise<void>
  loading?: boolean
  progress?: number
  error?: string
  className?: string
}

export function FileUploadForm({ 
  onSubmit, 
  loading = false, 
  progress,
  error, 
  className 
}: FileUploadFormProps) {
  const [selectedFile, setSelectedFile] = React.useState<File | null>(null)

  const form = useForm<FileUploadFormData>({
    resolver: zodResolver(fileUploadSchema),
  })

  const handleFileSelect = (file: File) => {
    setSelectedFile(file)
    form.setValue("file", file)
    form.clearErrors("file")
  }

  const handleFormSubmit = async (data: FileUploadFormData) => {
    try {
      await onSubmit(data)
      // Reset form on successful upload
      form.reset()
      setSelectedFile(null)
    } catch {
      // Error handling is done by parent component
    }
  }

  const removeFile = () => {
    setSelectedFile(null)
    form.setValue("file", null as unknown as File)
  }

  return (
    <Card className={cn("w-full max-w-2xl mx-auto", className)}>
      <CardHeader>
        <CardTitle>Upload Keywords</CardTitle>
        <CardDescription>
          Upload a CSV file containing keywords to be scraped. The file should have a &quot;keyword&quot; column.
        </CardDescription>
      </CardHeader>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleFormSubmit)}>
          <CardContent className="space-y-6">
            {/* Global Error */}
            {error && (
              <div className="p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md">
                {error}
              </div>
            )}

            {/* File Upload Zone */}
            <FormField
              control={form.control}
              name="file"
              render={() => (
                <FormItem>
                  <FormLabel>CSV File</FormLabel>
                  <FormControl>
                    {selectedFile ? (
                      <div className="space-y-4">
                        {/* Selected File Display */}
                        <div className="flex items-center justify-between p-4 border rounded-lg bg-muted/50">
                          <div className="flex items-center gap-3">
                            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                              <svg className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m6.75 18H3.75V4.5h11.25c.621 0 1.125.504 1.125 1.125v13.5c0 .621-.504 1.125-1.125 1.125z" />
                              </svg>
                            </div>
                            <div>
                              <p className="font-medium">{selectedFile.name}</p>
                              <p className="text-sm text-muted-foreground">
                                {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                              </p>
                            </div>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={removeFile}
                            disabled={loading}
                          >
                            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </Button>
                        </div>

                        {/* Upload Progress */}
                        {loading && progress !== undefined && (
                          <ProgressIndicator
                            value={progress}
                            label="Uploading file"
                            showPercentage
                            animated
                          />
                        )}
                      </div>
                    ) : (
                      <FileUploadZone
                        onFileSelect={handleFileSelect}
                        accept=".csv"
                        maxSize={10 * 1024 * 1024} // 10MB
                        disabled={loading}
                        loading={loading}
                      />
                    )}
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />


            {/* File Requirements */}
            <div className="rounded-lg border bg-muted/50 p-4">
              <h4 className="font-medium mb-2">File Requirements:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• CSV format only</li>
                <li>• Maximum file size: 10MB</li>
                <li>• Must contain a keyword column</li>
                <li>• One keyword per row</li>
                <li>• UTF-8 encoding recommended</li>
              </ul>
            </div>
          </CardContent>

          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                form.reset()
                setSelectedFile(null)
              }}
              disabled={loading}
            >
              Clear
            </Button>
            <Button 
              type="submit" 
              disabled={loading || !selectedFile}
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                  Uploading...
                </>
              ) : (
                "Upload Keywords"
              )}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  )
}
