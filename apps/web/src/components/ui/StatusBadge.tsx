'use client'

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { Badge } from "./Badge"
import { cn } from "@/lib/utils"

const statusBadgeVariants = cva(
  "inline-flex items-center gap-1.5 font-medium transition-colors",
  {
    variants: {
      status: {
        pending: "bg-slate-100 text-slate-700 border-slate-200 hover:bg-slate-200",
        processing: "bg-amber-100 text-amber-800 border-amber-200 hover:bg-amber-200",
        completed: "bg-green-100 text-green-800 border-green-200 hover:bg-green-200",
        error: "bg-red-100 text-red-800 border-red-200 hover:bg-red-200",
        cancelled: "bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-200",
      },
      size: {
        sm: "text-xs px-2 py-0.5",
        default: "text-sm px-2.5 py-1",
        lg: "text-base px-3 py-1.5",
      },
      animated: {
        true: "",
        false: "",
      },
    },
    defaultVariants: {
      status: "pending",
      size: "default",
      animated: false,
    },
  }
)

interface StatusBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof statusBadgeVariants> {
  status: "pending" | "processing" | "completed" | "error" | "cancelled"
  animated?: boolean
  showIcon?: boolean
}

const StatusIcon = ({ status, animated }: { status: string; animated?: boolean }) => {
  const iconClass = cn(
    "w-2 h-2 rounded-full",
    animated && status === "processing" && "animate-pulse"
  )

  switch (status) {
    case "pending":
      return <div className={cn(iconClass, "bg-slate-400")} />
    case "processing":
      return <div className={cn(iconClass, "bg-amber-500")} />
    case "completed":
      return (
        <svg className="w-3 h-3" viewBox="0 0 20 20" fill="currentColor">
          <path
            fillRule="evenodd"
            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
            clipRule="evenodd"
          />
        </svg>
      )
    case "error":
      return (
        <svg className="w-3 h-3" viewBox="0 0 20 20" fill="currentColor">
          <path
            fillRule="evenodd"
            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      )
    case "cancelled":
      return (
        <svg className="w-3 h-3" viewBox="0 0 20 20" fill="currentColor">
          <path
            fillRule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
            clipRule="evenodd"
          />
        </svg>
      )
    default:
      return null
  }
}

const StatusBadge = React.forwardRef<HTMLDivElement, StatusBadgeProps>(
  ({ className, status, size, animated, showIcon = true, children, ...props }, ref) => {
    const statusText = children || status.charAt(0).toUpperCase() + status.slice(1)

    return (
      <Badge
        ref={ref}
        className={cn(statusBadgeVariants({ status, size, animated }), className)}
        {...props}
      >
        {showIcon && <StatusIcon status={status} animated={animated} />}
        {statusText}
      </Badge>
    )
  }
)

StatusBadge.displayName = "StatusBadge"

export { StatusBadge, statusBadgeVariants }
export type { StatusBadgeProps }
