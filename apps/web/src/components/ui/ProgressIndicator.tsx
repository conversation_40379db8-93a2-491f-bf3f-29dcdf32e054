'use client'

import * as React from "react"
import { cn } from "@/lib/utils"

interface ProgressIndicatorProps extends React.HTMLAttributes<HTMLDivElement> {
  value: number // 0-100
  max?: number
  size?: "sm" | "default" | "lg"
  variant?: "default" | "success" | "warning" | "error"
  showPercentage?: boolean
  showLabel?: boolean
  label?: string
  animated?: boolean
}

const ProgressIndicator = React.forwardRef<HTMLDivElement, ProgressIndicatorProps>(
  ({
    className,
    value,
    max = 100,
    size = "default",
    variant = "default",
    showPercentage = true,
    showLabel = false,
    label,
    animated = true,
    ...props
  }, ref) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100)

    const sizeClasses = {
      sm: "h-2",
      default: "h-3",
      lg: "h-4",
    }

    const variantClasses = {
      default: "bg-primary",
      success: "bg-green-500",
      warning: "bg-amber-500",
      error: "bg-red-500",
    }

    const backgroundClasses = {
      default: "bg-primary/20",
      success: "bg-green-500/20",
      warning: "bg-amber-500/20",
      error: "bg-red-500/20",
    }

    return (
      <div ref={ref} className={cn("space-y-2", className)} {...props}>
        {/* Label and Percentage */}
        {(showLabel || showPercentage) && (
          <div className="flex items-center justify-between text-sm">
            {showLabel && (
              <span className="font-medium text-foreground">
                {label || "Progress"}
              </span>
            )}
            {showPercentage && (
              <span className="text-muted-foreground">
                {Math.round(percentage)}%
              </span>
            )}
          </div>
        )}

        {/* Progress Bar */}
        <div
          className={cn(
            "relative w-full overflow-hidden rounded-full",
            backgroundClasses[variant],
            sizeClasses[size]
          )}
          role="progressbar"
          aria-valuenow={value}
          aria-valuemin={0}
          aria-valuemax={max}
          aria-label={label || "Progress indicator"}
        >
          <div
            className={cn(
              "h-full rounded-full transition-all duration-300 ease-out",
              variantClasses[variant],
              animated && "transition-transform"
            )}
            style={{
              width: `${percentage}%`,
              transform: animated ? `translateX(${percentage < 100 ? '0' : '0'})` : undefined,
            }}
          />

          {/* Animated shimmer effect for active progress */}
          {animated && percentage > 0 && percentage < 100 && (
            <div
              className={cn(
                "absolute inset-0 rounded-full",
                "bg-gradient-to-r from-transparent via-white/30 to-transparent",
                "animate-pulse"
              )}
              style={{
                width: `${percentage}%`,
              }}
            />
          )}
        </div>

        {/* Additional info for detailed progress */}
        {value !== undefined && max !== undefined && (
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>{value} of {max}</span>
            {percentage === 100 && (
              <span className="text-green-600 font-medium">Complete</span>
            )}
          </div>
        )}
      </div>
    )
  }
)

ProgressIndicator.displayName = "ProgressIndicator"

// Circular Progress Variant
interface CircularProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value: number // 0-100
  size?: number // diameter in pixels
  strokeWidth?: number
  variant?: "default" | "success" | "warning" | "error"
  showPercentage?: boolean
}

const CircularProgress = React.forwardRef<HTMLDivElement, CircularProgressProps>(
  ({
    className,
    value,
    size = 40,
    strokeWidth = 4,
    variant = "default",
    showPercentage = true,
    ...props
  }, ref) => {
    const percentage = Math.min(Math.max(value, 0), 100)
    const radius = (size - strokeWidth) / 2
    const circumference = radius * 2 * Math.PI
    const strokeDasharray = circumference
    const strokeDashoffset = circumference - (percentage / 100) * circumference

    const variantColors = {
      default: "stroke-primary",
      success: "stroke-green-500",
      warning: "stroke-amber-500",
      error: "stroke-red-500",
    }

    return (
      <div
        ref={ref}
        className={cn("relative inline-flex items-center justify-center", className)}
        {...props}
      >
        <svg
          width={size}
          height={size}
          className="transform -rotate-90"
          aria-hidden="true"
        >
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="currentColor"
            strokeWidth={strokeWidth}
            fill="none"
            className="text-muted/20"
          />
          {/* Progress circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            strokeWidth={strokeWidth}
            fill="none"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className={cn("transition-all duration-300 ease-out", variantColors[variant])}
          />
        </svg>
        {showPercentage && (
          <span className="absolute text-xs font-medium">
            {Math.round(percentage)}%
          </span>
        )}
      </div>
    )
  }
)

CircularProgress.displayName = "CircularProgress"

export { ProgressIndicator, CircularProgress }
export type { ProgressIndicatorProps, CircularProgressProps }
