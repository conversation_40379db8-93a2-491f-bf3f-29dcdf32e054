'use client'

import * as React from "react"
import { type LucideIcon, type LucideProps } from "lucide-react"
import { cn } from "@/lib/utils"

// Common icon imports - add more as needed
import {
  Search,
  LayoutDashboard,
  FileText,
  Upload,
  BarChart3,
  Settings,
  LogOut,
  User,
  Menu,
  X,
  ChevronRight,
  ChevronDown,
  Plus,
  Minus,
  Check,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  Clock,
  Download,
  Eye,
  Edit,
  Trash2,
  Copy,
  ExternalLink,
  Loader2,
  RefreshCw,
  Filter,
  SortAsc,
  SortDesc,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Globe,
  Star,
  Heart,
  Bookmark,
  Share,
  MoreHorizontal,
  MoreVertical,
} from "lucide-react"

// Icon registry for easy access
export const iconRegistry = {
  // Navigation
  search: Search,
  dashboard: LayoutDashboard,
  keywords: FileText,
  upload: Upload,
  reports: BarChart3,
  settings: Settings,
  logout: LogOut,
  user: User,
  menu: Menu,
  close: X,
  
  // Arrows & Chevrons
  chevronRight: ChevronRight,
  chevronDown: ChevronDown,
  
  // Actions
  plus: Plus,
  minus: Minus,
  check: Check,
  edit: Edit,
  delete: Trash2,
  copy: Copy,
  download: Download,
  view: Eye,
  externalLink: ExternalLink,
  
  // Status & Feedback
  success: CheckCircle,
  error: XCircle,
  warning: AlertCircle,
  info: Info,
  pending: Clock,
  
  // Loading & Refresh
  loading: Loader2,
  refresh: RefreshCw,
  
  // Data & Filters
  filter: Filter,
  sortAsc: SortAsc,
  sortDesc: SortDesc,
  
  // Common
  calendar: Calendar,
  mail: Mail,
  phone: Phone,
  location: MapPin,
  globe: Globe,
  star: Star,
  heart: Heart,
  bookmark: Bookmark,
  share: Share,
  moreHorizontal: MoreHorizontal,
  moreVertical: MoreVertical,
} as const

export type IconName = keyof typeof iconRegistry

interface IconProps extends Omit<LucideProps, 'ref'> {
  name: IconName
  className?: string
}

interface DirectIconProps extends Omit<LucideProps, 'ref'> {
  icon: LucideIcon
  className?: string
}

// Main Icon component using the registry
export const Icon = React.forwardRef<SVGSVGElement, IconProps>(
  ({ name, className, size = 16, ...props }, ref) => {
    const IconComponent = iconRegistry[name]
    
    if (!IconComponent) {
      console.warn(`Icon "${name}" not found in registry`)
      return null
    }

    return (
      <IconComponent
        ref={ref}
        size={size}
        className={cn("shrink-0", className)}
        {...props}
      />
    )
  }
)
Icon.displayName = "Icon"

// Direct icon component for when you want to use Lucide icons directly
export const DirectIcon = React.forwardRef<SVGSVGElement, DirectIconProps>(
  ({ icon: IconComponent, className, size = 16, ...props }, ref) => {
    return (
      <IconComponent
        ref={ref}
        size={size}
        className={cn("shrink-0", className)}
        {...props}
      />
    )
  }
)
DirectIcon.displayName = "DirectIcon"

// Predefined size variants
export const iconSizes = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 20,
  xl: 24,
  '2xl': 32,
} as const

export type IconSize = keyof typeof iconSizes

// Helper component with predefined sizes
interface SizedIconProps extends Omit<IconProps, 'size'> {
  size?: IconSize | number
}

export const SizedIcon = React.forwardRef<SVGSVGElement, SizedIconProps>(
  ({ size = 'md', ...props }, ref) => {
    const iconSize = typeof size === 'string' ? iconSizes[size] : size
    
    return (
      <Icon
        ref={ref}
        size={iconSize}
        {...props}
      />
    )
  }
)
SizedIcon.displayName = "SizedIcon"

// Export commonly used icons for direct import
export {
  Search,
  LayoutDashboard,
  FileText,
  Upload,
  BarChart3,
  Settings,
  LogOut,
  User,
  Menu,
  X,
  ChevronRight,
  ChevronDown,
  Plus,
  Minus,
  Check,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  Clock,
  Download,
  Eye,
  Edit,
  Trash2,
  Copy,
  ExternalLink,
  Loader2,
  RefreshCw,
  Filter,
  SortAsc,
  SortDesc,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Globe,
  Star,
  Heart,
  Bookmark,
  Share,
  MoreHorizontal,
  MoreVertical,
}
