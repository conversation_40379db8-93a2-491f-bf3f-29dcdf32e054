import { Metadata } from 'next';
import { KeywordsList } from '@/features/keywords';

export const metadata: Metadata = {
  title: 'Keywords | Dashboard',
  description: 'View and search all your keywords across all batches',
};

export default function KeywordsPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Keywords</h1>
        <p className="text-muted-foreground">
          Search and view all your keywords across all batches
        </p>
      </div>

      {/* Keywords List Component */}
      <KeywordsList />
    </div>
  );
}