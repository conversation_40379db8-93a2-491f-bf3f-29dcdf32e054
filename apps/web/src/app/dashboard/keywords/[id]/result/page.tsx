'use client';

import { Suspense } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { ArrowLeft, Loader2, AlertCircle } from 'lucide-react';
import { SearchResultDetail, useSearchResultByKeyword } from '@/features/search-results';

function KeywordSearchResultContent({ keywordId }: { keywordId: string }) {
  const router = useRouter();
  const { data: searchResult, isLoading, error } = useSearchResultByKeyword(keywordId);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading search result details...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Error Loading Search Result</h3>
            <p className="text-gray-600 mb-4">
              {error instanceof Error ? error.message : 'Failed to load search result details'}
            </p>
            <Button onClick={() => router.back()}>
              Go Back
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!searchResult) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Search Result Found</h3>
            <p className="text-gray-600 mb-4">
              This keyword hasn&apos;t been processed yet or no search result is available.
            </p>
            <Button onClick={() => router.back()}>
              Go Back
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return <SearchResultDetail searchResult={searchResult} />;
}

export default function KeywordSearchResultPage() {
  const params = useParams();
  const router = useRouter();
  const keywordId = params.id as string;

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Keyword Search Result</h1>
          <p className="text-gray-600">
            Detailed analysis of search result and extracted links for this keyword
          </p>
        </div>
      </div>

      {/* Content */}
      <Suspense
        fallback={
          <Card>
            <CardContent className="flex items-center justify-center py-12">
              <div className="flex items-center gap-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span>Loading...</span>
              </div>
            </CardContent>
          </Card>
        }
      >
        <KeywordSearchResultContent keywordId={keywordId} />
      </Suspense>
    </div>
  );
}