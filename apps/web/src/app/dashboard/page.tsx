import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { AppShell, PageHeader, PageContent, Sidebar } from '@/components/layout'
import { DashboardOverview } from '@/features/dashboard'

export default async function DashboardPage() {
  const supabase = await createClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/auth/login')
  }

  return (
    <AppShell
      sidebar={<Sidebar />}
      header={
        <div className="flex-1">
          <h2 className="text-lg font-semibold">Dashboard</h2>
        </div>
      }
    >
      <PageHeader
        title="Dashboard"
        description="Overview of your keyword scraping activities"
      />
      <PageContent>
        <DashboardOverview />
      </PageContent>
    </AppShell>
  )
}