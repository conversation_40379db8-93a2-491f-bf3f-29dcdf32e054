import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { AppShell, PageHeader, PageContent, Sidebar } from '@/components/layout'
import { BatchDetail } from '@/features/dashboard'

interface BatchDetailPageProps {
  params: Promise<{
    id: string
  }>
}

export default async function BatchDetailPage({ params }: BatchDetailPageProps) {
  const { id } = await params;
  const supabase = await createClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/auth/login')
  }

  return (
    <AppShell
      sidebar={<Sidebar />}
      header={
        <div className="flex-1">
          <h2 className="text-lg font-semibold">Batch Details</h2>
        </div>
      }
    >
      <PageHeader
        title="Batch Details"
        description="View detailed information about this keyword batch"
      />
      <PageContent>
        <BatchDetail batchId={id} />
      </PageContent>
    </AppShell>
  )
}