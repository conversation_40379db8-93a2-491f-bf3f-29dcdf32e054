import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { AppShell, PageHeader, PageContent, Sidebar } from '@/components/layout'
import { BatchList } from '@/features/dashboard'

export default async function BatchesPage() {
  const supabase = await createClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/auth/login')
  }

  return (
    <AppShell
      sidebar={<Sidebar />}
      header={
        <div className="flex-1">
          <h2 className="text-lg font-semibold">All Batches</h2>
        </div>
      }
    >
      <PageHeader
        title="Keyword Batches"
        description="Manage all your keyword scraping batches"
      />
      <PageContent>
        <BatchList />
      </PageContent>
    </AppShell>
  )
}