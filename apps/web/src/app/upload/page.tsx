import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { AppShell, PageHeader, PageContent, Sidebar } from '@/components/layout'
import { KeywordUploadFlow } from '@/features/keywords'

export default async function UploadPage() {
  const supabase = await createClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/auth/login')
  }

  return (
    <AppShell
      sidebar={<Sidebar />}
      header={
        <div className="flex-1">
          <h2 className="text-lg font-semibold">Upload Keywords</h2>
        </div>
      }
    >
      <PageHeader
        title="Upload Keywords"
        description="Upload a CSV file with keywords to start scraping search engine results"
      />
      <PageContent>
        <div className="max-w-4xl mx-auto">
          <KeywordUploadFlow />
        </div>
      </PageContent>
    </AppShell>
  )
}