@import "tailwindcss";

:root {
  --radius: 0.625rem;

  /* Enhanced color system for keyword scraper */
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);

  /* Primary - Indigo for actions and focus */
  --primary: oklch(0.68 0.15 264);
  --primary-foreground: oklch(0.98 0 0);

  /* Secondary - Slate for UI elements */
  --secondary: oklch(0.98 0 0);
  --secondary-foreground: oklch(0.145 0 0);

  /* Muted - For subtle backgrounds */
  --muted: oklch(0.96 0 0);
  --muted-foreground: oklch(0.46 0 0);

  /* Accent - For highlights */
  --accent: oklch(0.96 0 0);
  --accent-foreground: oklch(0.145 0 0);

  /* Status colors */
  --destructive: oklch(0.60 0.20 27);
  --success: oklch(0.55 0.15 142);
  --warning: oklch(0.70 0.15 85);

  /* Borders and inputs */
  --border: oklch(0.91 0 0);
  --input: oklch(0.91 0 0);
  --ring: oklch(0.68 0.15 264);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

/* Base styles */
* {
  border-color: var(--border);
  outline-color: rgb(var(--ring) / 0.5);
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Typography scale */
h1 {
  font-size: 1.875rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

h2 {
  font-size: 1.5rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

h3 {
  font-size: 1.25rem;
  font-weight: 500;
  letter-spacing: -0.025em;
}

h4 {
  font-size: 1.125rem;
  font-weight: 500;
}

p {
  font-size: 1rem;
  line-height: 1.625;
}

small {
  font-size: 0.875rem;
  color: var(--muted-foreground);
}

/* Custom component styles */
.app-container {
  margin-left: auto;
  margin-right: auto;
  max-width: 80rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .app-container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .app-container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.page-header {
  border-bottom: 1px solid var(--border);
  background-color: rgb(var(--background) / 0.95);
  backdrop-filter: blur(8px);
}

@supports (backdrop-filter: blur(8px)) {
  .page-header {
    background-color: rgb(var(--background) / 0.6);
  }
}

.status-processing {
  background-color: rgb(251 191 36 / 0.1);
  color: rgb(146 64 14);
  border-color: rgb(251 191 36 / 0.2);
}

.status-completed {
  background-color: rgb(34 197 94 / 0.1);
  color: rgb(21 128 61);
  border-color: rgb(34 197 94 / 0.2);
}

.status-error {
  background-color: rgb(239 68 68 / 0.1);
  color: rgb(185 28 28);
  border-color: rgb(239 68 68 / 0.2);
}

/* Custom utilities */
.transition-standard {
  transition-property: all;
  transition-duration: 150ms;
  transition-timing-function: ease-in-out;
}

.transition-fast {
  transition-property: all;
  transition-duration: 100ms;
  transition-timing-function: ease-in-out;
}

.transition-slow {
  transition-property: all;
  transition-duration: 300ms;
  transition-timing-function: ease-in-out;
}
