import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type { User } from '@supabase/supabase-js'

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isInitialized: boolean
}

export interface AuthActions {
  setUser: (user: User | null) => void
  setInitialized: (initialized: boolean) => void
  logout: () => void
}

export type AuthStore = AuthState & AuthActions

export const useAuthStore = create<AuthStore>()(
  devtools(
    (set, get) => ({
      // State
      user: null,
      isAuthenticated: false,
      isInitialized: false,

      // Actions
      setUser: (user: User | null) => {
        set(
          {
            user,
            isAuthenticated: !!user,
          },
          false,
          'auth/setUser'
        )
      },

      setInitialized: (initialized: boolean) => {
        set(
          {
            isInitialized: initialized,
          },
          false,
          'auth/setInitialized'
        )
      },

      logout: () => {
        set(
          {
            user: null,
            isAuthenticated: false,
          },
          false,
          'auth/logout'
        )
      },
    }),
    {
      name: 'auth-store',
    }
  )
)