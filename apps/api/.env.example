# =============================================================================
# API Application Environment Variables
# =============================================================================
# Copy this file to .env and fill in your actual values
# Never commit .env to version control

# =============================================================================
# Application Environment
# =============================================================================
NODE_ENV=development

# =============================================================================
# Server Configuration
# =============================================================================
# Port for the API server to run on
PORT=3001

# =============================================================================
# Database Configuration
# =============================================================================
# PostgreSQL connection string
# Format: postgresql://username:password@host:port/database
DATABASE_URL=postgresql://username:password@localhost:5432/search_keywords_scraper

# =============================================================================
# Redis Configuration
# =============================================================================
# Redis connection string for job queue and caching
# Format: redis://username:password@host:port
REDIS_URL=redis://localhost:6379

# =============================================================================
# Authentication Configuration
# =============================================================================
# Secret key for signing JWT tokens (minimum 32 characters)
# Generate with: openssl rand -base64 32
JWT_SECRET=your-super-secure-jwt-secret-key-here-minimum-32-characters

# =============================================================================
# Supabase Configuration
# =============================================================================
# Get these values from your Supabase project settings
# Project Settings > API > Project URL
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co

# Project Settings > API > Project API keys > service_role secret
# ⚠️  NEVER expose this key on the client side
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your-service-role-key

SUPABASE_JWT_SECRET=your-supabase-jwt-secret

# =============================================================================
# Logging Configuration
# =============================================================================
# Log level: error, warn, info, debug
LOG_LEVEL=info
