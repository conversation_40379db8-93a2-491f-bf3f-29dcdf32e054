{"name": "@search-keywords-scraper/api", "version": "0.0.1", "description": "NestJS API for Search Keywords Scraper", "author": "", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "dev": "nest start --watch --entryFile apps/api/src/main", "start": "node dist/apps/api/src/main", "start:debug": "nest start --debug --watch", "start:prod": "node dist/apps/api/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/bull": "^11.0.3", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@search-keywords-scraper/database": "workspace:*", "@search-keywords-scraper/queue": "workspace:*", "@supabase/supabase-js": "^2.55.0", "bull": "^4.16.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dotenv": "^16.6.1", "drizzle-orm": "^0.36.4", "ioredis": "^5.4.1", "jose": "^6.0.12", "jwks-rsa": "^3.2.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "postgres": "^3.4.4", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@search-keywords-scraper/test-utils": "workspace:*", "@types/express": "^5.0.0", "@types/jest": "^30.0.0", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^30.0.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^@search-keywords-scraper/test-utils$": "<rootDir>/../../../packages/test-utils/dist", "^@search-keywords-scraper/database$": "<rootDir>/../../../packages/database/src", "^@search-keywords-scraper/queue$": "<rootDir>/../../../packages/queue/src"}}}