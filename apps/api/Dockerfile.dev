FROM node:18-alpine

# Install pnpm globally
RUN npm install -g pnpm@8

# Set working directory
WORKDIR /app

# Copy package.json files for better caching
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY apps/api/package.json apps/api/
COPY packages/*/package.json packages/*/

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Build shared packages
RUN pnpm build --filter=!@search-keywords-scraper/web --filter=!@search-keywords-scraper/api --filter=!@search-keywords-scraper/worker

# Change to API directory
WORKDIR /app/apps/api

# Expose port
EXPOSE 3001

# Start development server
CMD ["pnpm", "dev"]