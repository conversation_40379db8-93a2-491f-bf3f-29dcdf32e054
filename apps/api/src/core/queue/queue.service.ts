import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { KeywordScrapingJob, BatchJob, QUEUE_NAMES, JOB_PRIORITIES } from '@search-keywords-scraper/queue';

@Injectable()
export class QueueService {
  constructor(
    @InjectQueue(QUEUE_NAMES.SCRAPER_KEYWORD)
    private keywordQueue: Queue<KeywordScrapingJob>,
    
    @InjectQueue(QUEUE_NAMES.SCRAPER_BATCH)
    private batchQueue: Queue<BatchJob>,
  ) {}

  async enqueueKeywordJob(jobData: KeywordScrapingJob, priority: number = JOB_PRIORITIES.NORMAL) {
    return this.keywordQueue.add('scrape-keyword', jobData, {
      priority,
      jobId: `keyword-${jobData.keywordId}-${Date.now()}`,
    });
  }

  async enqueueBatchJob(jobData: BatchJob, priority: number = JOB_PRIORITIES.NORMAL) {
    return this.batchQueue.add('process-batch', jobData, {
      priority,
      jobId: `batch-${jobData.batchId}-${jobData.action}-${Date.now()}`,
    });
  }

  async getQueueStatus() {
    const [keywordWaiting, keywordActive, keywordCompleted, keywordFailed] = await Promise.all([
      this.keywordQueue.getWaiting(),
      this.keywordQueue.getActive(),
      this.keywordQueue.getCompleted(),
      this.keywordQueue.getFailed(),
    ]);

    return {
      keyword: {
        waiting: keywordWaiting.length,
        active: keywordActive.length,
        completed: keywordCompleted.length,
        failed: keywordFailed.length,
      },
    };
  }
}