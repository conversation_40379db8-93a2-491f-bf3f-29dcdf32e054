import { Injectable } from '@nestjs/common';
import { eq, and } from 'drizzle-orm';
import {
  searchResults,
  keywords,
  keywordBatches,
} from '@search-keywords-scraper/database';
import { db } from '../../../database/database.config';
import type { SearchResultDetail } from '../dto/search-results.dto';

@Injectable()
export class SearchResultsService {
  async getSearchResultDetail(
    userId: string,
    searchResultId: string,
  ): Promise<SearchResultDetail | null> {
    const [result] = await db
      .select({
        id: searchResults.id,
        keywordId: searchResults.keywordId,
        totalAds: searchResults.totalAds,
        totalLinks: searchResults.totalLinks,
        metadata: searchResults.metadata,
        adsLinks: searchResults.adsLinks,
        organicLinks: searchResults.organicLinks,
        otherLinks: searchResults.otherLinks,
        scrapedAt: searchResults.scrapedAt,
        // Include keyword and batch info for context
        keyword: keywords.keyword,
        keywordStatus: keywords.status,
        searchEngine: keywords.searchEngine,
        batchId: keywords.batchId,
        batchName: keywordBatches.name,
      })
      .from(searchResults)
      .innerJoin(keywords, eq(searchResults.keywordId, keywords.id))
      .innerJoin(keywordBatches, eq(keywords.batchId, keywordBatches.id))
      .where(
        and(
          eq(searchResults.id, searchResultId),
          eq(keywordBatches.userId, userId), // Ensure user owns this data
        ),
      )
      .limit(1);

    if (!result) {
      return null;
    }

    return {
      id: result.id,
      keywordId: result.keywordId,
      keyword: result.keyword,
      searchEngine: result.searchEngine,
      batchId: result.batchId,
      batchName: result.batchName,
      totalAds: result.totalAds,
      totalLinks: result.totalLinks,
      adsLinks: result.adsLinks as any[] || [],
      organicLinks: result.organicLinks as any[] || [],
      otherLinks: result.otherLinks as any[] || [],
      metadata: result.metadata,
      htmlContent: '', // Empty by default for performance
      scrapedAt: result.scrapedAt.toISOString(),
    };
  }

  async getSearchResultByKeyword(
    userId: string,
    keywordId: string,
  ): Promise<SearchResultDetail | null> {
    const [result] = await db
      .select({
        id: searchResults.id,
        keywordId: searchResults.keywordId,
        totalAds: searchResults.totalAds,
        totalLinks: searchResults.totalLinks,
        metadata: searchResults.metadata,
        adsLinks: searchResults.adsLinks,
        organicLinks: searchResults.organicLinks,
        otherLinks: searchResults.otherLinks,
        scrapedAt: searchResults.scrapedAt,
        // Include keyword and batch info for context
        keyword: keywords.keyword,
        keywordStatus: keywords.status,
        searchEngine: keywords.searchEngine,
        batchId: keywords.batchId,
        batchName: keywordBatches.name,
      })
      .from(searchResults)
      .innerJoin(keywords, eq(searchResults.keywordId, keywords.id))
      .innerJoin(keywordBatches, eq(keywords.batchId, keywordBatches.id))
      .where(
        and(
          eq(keywords.id, keywordId),
          eq(keywordBatches.userId, userId), // Ensure user owns this data
        ),
      )
      .limit(1);

    if (!result) {
      return null;
    }

    return {
      id: result.id,
      keywordId: result.keywordId,
      keyword: result.keyword,
      searchEngine: result.searchEngine,
      batchId: result.batchId,
      batchName: result.batchName,
      totalAds: result.totalAds,
      totalLinks: result.totalLinks,
      adsLinks: result.adsLinks as any[] || [],
      organicLinks: result.organicLinks as any[] || [],
      otherLinks: result.otherLinks as any[] || [],
      metadata: result.metadata,
      htmlContent: '', // Empty by default for performance
      scrapedAt: result.scrapedAt.toISOString(),
    };
  }

  async getSearchResultHtml(
    userId: string,
    searchResultId: string,
  ): Promise<string | null> {
    const [result] = await db
      .select({
        htmlContent: searchResults.htmlContent,
      })
      .from(searchResults)
      .innerJoin(keywords, eq(searchResults.keywordId, keywords.id))
      .innerJoin(keywordBatches, eq(keywords.batchId, keywordBatches.id))
      .where(
        and(
          eq(searchResults.id, searchResultId),
          eq(keywordBatches.userId, userId), // Ensure user owns this data
        ),
      )
      .limit(1);

    return result?.htmlContent || null;
  }
}