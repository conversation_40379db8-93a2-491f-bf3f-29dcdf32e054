import {
  <PERSON>,
  Get,
  Param,
  ParseU<PERSON><PERSON><PERSON><PERSON>,
  UseGuards,
  NotFoundException,
} from '@nestjs/common';
import { SearchResultsService } from '../services/search-results.service';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';
import { CurrentUser } from '../../../core/decorators';

@Controller({ path: 'search-results', version: '1' })
@UseGuards(JwtAuthGuard)
export class SearchResultsController {
  constructor(private searchResultsService: SearchResultsService) {}

  @Get(':id')
  async getSearchResult(
    @CurrentUser() user: any,
    @Param('id', ParseUUIDPipe) searchResultId: string,
  ) {
    const searchResult = await this.searchResultsService.getSearchResultDetail(
      user.id,
      searchResultId,
    );

    if (!searchResult) {
      throw new NotFoundException('Search result not found or access denied');
    }

    return searchResult;
  }

  @Get('keyword/:keywordId')
  async getSearchResultByKeyword(
    @CurrentUser() user: any,
    @Param('keywordId', ParseUUIDPipe) keywordId: string,
  ) {
    const searchResult = await this.searchResultsService.getSearchResultByKeyword(
      user.id,
      keywordId,
    );

    if (!searchResult) {
      throw new NotFoundException('Search result not found for this keyword');
    }

    return searchResult;
  }

  @Get(':id/html')
  async getSearchResultHtml(
    @CurrentUser() user: any,
    @Param('id', ParseUUIDPipe) searchResultId: string,
  ) {
    const htmlContent = await this.searchResultsService.getSearchResultHtml(
      user.id,
      searchResultId,
    );

    if (htmlContent === null) {
      throw new NotFoundException('Search result HTML not found or access denied');
    }

    return { htmlContent };
  }
}