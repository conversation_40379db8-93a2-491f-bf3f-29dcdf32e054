export interface LinkDetail {
  url: string;
  title: string;
  position: number;
  type: 'ad' | 'organic' | 'navigation' | 'footer' | 'other';
  domain: string;
  anchorText?: string;
}

export interface SearchResultDetail {
  id: string;
  keywordId: string;
  keyword: string;
  searchEngine: 'google' | 'bing';
  batchId: string;
  batchName: string;
  totalAds: number;
  totalLinks: number;
  adsLinks: LinkDetail[];
  organicLinks: LinkDetail[];
  otherLinks: LinkDetail[];
  metadata: any;
  htmlContent: string | null;
  scrapedAt: string;
}