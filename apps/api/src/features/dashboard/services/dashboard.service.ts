import { Injectable } from '@nestjs/common';
import { eq, and, count } from 'drizzle-orm';
import { db } from '../../../database/database.config';
import { keywordBatches, keywords, searchResults } from '@search-keywords-scraper/database';

@Injectable()
export class DashboardService {
  async getUserDashboardStats(userId: string) {
    // Get total batches
    const totalBatches = await db
      .select({ count: count() })
      .from(keywordBatches)
      .where(eq(keywordBatches.userId, userId));

    // Get total keywords across all batches
    const totalKeywords = await db
      .select({ count: count() })
      .from(keywords)
      .innerJoin(keywordBatches, eq(keywords.batchId, keywordBatches.id))
      .where(eq(keywordBatches.userId, userId));

    // Get completed keywords
    const completedKeywords = await db
      .select({ count: count() })
      .from(keywords)
      .innerJoin(keywordBatches, eq(keywords.batchId, keywordBatches.id))
      .where(
        and(
          eq(keywordBatches.userId, userId),
          eq(keywords.status, 'completed')
        )
      );

    // Get failed keywords
    const failedKeywords = await db
      .select({ count: count() })
      .from(keywords)
      .innerJoin(keywordBatches, eq(keywords.batchId, keywordBatches.id))
      .where(
        and(
          eq(keywordBatches.userId, userId),
          eq(keywords.status, 'failed')
        )
      );

    // Get processing keywords
    const processingKeywords = await db
      .select({ count: count() })
      .from(keywords)
      .innerJoin(keywordBatches, eq(keywords.batchId, keywordBatches.id))
      .where(
        and(
          eq(keywordBatches.userId, userId),
          eq(keywords.status, 'processing')
        )
      );

    // Get pending keywords
    const pendingKeywords = await db
      .select({ count: count() })
      .from(keywords)
      .innerJoin(keywordBatches, eq(keywords.batchId, keywordBatches.id))
      .where(
        and(
          eq(keywordBatches.userId, userId),
          eq(keywords.status, 'pending')
        )
      );

    // Get total search results
    const totalSearchResults = await db
      .select({ count: count() })
      .from(searchResults)
      .innerJoin(keywords, eq(searchResults.keywordId, keywords.id))
      .innerJoin(keywordBatches, eq(keywords.batchId, keywordBatches.id))
      .where(eq(keywordBatches.userId, userId));

    return {
      totalBatches: totalBatches[0]?.count || 0,
      totalKeywords: totalKeywords[0]?.count || 0,
      completedKeywords: completedKeywords[0]?.count || 0,
      failedKeywords: failedKeywords[0]?.count || 0,
      processingKeywords: processingKeywords[0]?.count || 0,
      pendingKeywords: pendingKeywords[0]?.count || 0,
      totalSearchResults: totalSearchResults[0]?.count || 0,
    };
  }

  async getUserRecentBatches(userId: string, limit: number = 5) {
    return await db
      .select({
        id: keywordBatches.id,
        name: keywordBatches.name,
        status: keywordBatches.status,
        totalKeywords: keywordBatches.totalKeywords,
        completedKeywords: keywordBatches.completedKeywords,
        failedKeywords: keywordBatches.failedKeywords,
        createdAt: keywordBatches.createdAt,
        updatedAt: keywordBatches.updatedAt,
      })
      .from(keywordBatches)
      .where(eq(keywordBatches.userId, userId))
      .orderBy(keywordBatches.createdAt)
      .limit(limit);
  }
}