import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { UsersService } from '../../users/services';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private supabaseUrl: string;

  constructor(private usersService: UsersService) {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;

    if (!supabaseUrl) {
      throw new Error(
        'Missing Supabase configuration: NEXT_PUBLIC_SUPABASE_URL is required',
      );
    }

    super({
      // Use the actual JWT secret or fallback to dummy
      secretOrKey: process.env.SUPABASE_JWT_SECRET || 'dummy-secret',
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      // Try to use actual JWT verification if we have the secret
      ignoreExpiration: !process.env.SUPABASE_JWT_SECRET, // Only ignore expiration if using dummy secret
      passReqToCallback: true,
    });

    this.supabaseUrl = supabaseUrl;
  }

  async validate(req: any, payload: any) {
    try {
      console.log('JWT validation started');
      console.log('Payload received:', payload);

      // If we have JWT secret, payload is already validated by passport-jwt
      if (process.env.SUPABASE_JWT_SECRET) {
        console.log(
          'Using JWT secret verification - payload already validated',
        );

        // Ensure user exists in database (create if not exists)
        const user = await this.usersService.findOrCreate(payload.sub, payload.email);
        console.log('User ensured in database:', user.id);

        return {
          id: payload.sub,
          email: payload.email,
          role: payload.role || 'authenticated',
          aud: payload.aud,
          exp: payload.exp,
          iat: payload.iat,
        };
      }

      // Fallback to Supabase server verification if no JWT secret
      console.log(
        'No JWT secret - falling back to Supabase server verification',
      );

      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        console.error('Invalid authorization header format');
        throw new UnauthorizedException('No valid authorization header');
      }

      const token = authHeader.substring(7);
      console.log('Token extracted, length:', token.length);

      const response = await fetch(`${this.supabaseUrl}/auth/v1/user`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          apikey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        },
      });

      console.log('Supabase response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          'Supabase auth verification failed:',
          response.status,
          response.statusText,
          errorText,
        );
        throw new UnauthorizedException(
          'Token verification failed with Supabase',
        );
      }

      const user = await response.json();
      console.log('User data received:', { id: user.id, email: user.email });

      if (!user || !user.id) {
        console.error('Invalid user data from Supabase:', user);
        throw new UnauthorizedException('Invalid user data from Supabase');
      }

      console.log('JWT validation successful for user:', user.id);

      // Ensure user exists in database (create if not exists)
      const dbUser = await this.usersService.findOrCreate(user.id, user.email);
      console.log('User ensured in database:', dbUser.id);

      return {
        id: user.id,
        email: user.email,
        role: user.role || 'authenticated',
        aud: user.aud,
        exp: user.exp,
        iat: user.iat,
      };
    } catch (error) {
      console.error('JWT verification failed:', error);
      throw new UnauthorizedException('Token validation failed');
    }
  }
}
