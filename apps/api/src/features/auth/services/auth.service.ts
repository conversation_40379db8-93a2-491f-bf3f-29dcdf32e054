import { Injectable } from '@nestjs/common';
import { createClient } from '@supabase/supabase-js';

@Injectable()
export class AuthService {
  private supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
  );

  constructor() {}

  async validateUser(email: string, password: string): Promise<any> {
    const { data, error } = await this.supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error || !data.user) {
      return null;
    }

    return data.user;
  }

  login(user: any) {
    // Note: With Supabase auth, the frontend handles token management
    // This method is kept for compatibility but may not be needed
    return {
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
    };
  }

  async register(email: string, password: string) {
    const { data, error } = await this.supabase.auth.signUp({
      email,
      password,
    });

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }

  async getUserById(userId: string) {
    const { data: user, error } =
      await this.supabase.auth.admin.getUserById(userId);

    if (error || !user) {
      return null;
    }

    return user.user;
  }
}
