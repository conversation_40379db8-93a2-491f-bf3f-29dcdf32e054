import { Injectable, Logger } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { db } from '../../../database/database.config';
import { users, type User, type NewUser } from '../../../../../../packages/database/src/schema';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  /**
   * Find user by ID
   */
  async findById(id: string): Promise<User | null> {
    try {
      const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
      return result[0] || null;
    } catch (error) {
      this.logger.error(`Failed to find user by ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    try {
      const result = await db.select().from(users).where(eq(users.email, email)).limit(1);
      return result[0] || null;
    } catch (error) {
      this.logger.error(`Failed to find user by email ${email}:`, error);
      throw error;
    }
  }

  /**
   * Create a new user
   */
  async create(userData: NewUser): Promise<User> {
    try {
      this.logger.log(`Creating user with ID: ${userData.id}, email: ${userData.email}`);
      
      const result = await db.insert(users).values(userData).returning();
      
      this.logger.log(`Successfully created user: ${result[0].id}`);
      return result[0];
    } catch (error) {
      this.logger.error(`Failed to create user:`, error);
      throw error;
    }
  }

  /**
   * Find user by ID or create if not exists
   * This is the main method for the auth flow
   */
  async findOrCreate(id: string, email: string): Promise<User> {
    try {
      // First try to find existing user
      let user = await this.findById(id);
      
      if (user) {
        this.logger.log(`Found existing user: ${id}`);
        return user;
      }

      // User doesn't exist, create new one
      this.logger.log(`User ${id} not found, creating new user`);
      
      const newUser: NewUser = {
        id,
        email,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      user = await this.create(newUser);
      return user;
    } catch (error) {
      this.logger.error(`Failed to find or create user ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update user information
   */
  async update(id: string, updateData: Partial<Omit<User, 'id' | 'createdAt'>>): Promise<User> {
    try {
      const result = await db
        .update(users)
        .set({ ...updateData, updatedAt: new Date() })
        .where(eq(users.id, id))
        .returning();

      if (!result[0]) {
        throw new Error(`User with ID ${id} not found for update`);
      }

      this.logger.log(`Successfully updated user: ${id}`);
      return result[0];
    } catch (error) {
      this.logger.error(`Failed to update user ${id}:`, error);
      throw error;
    }
  }
}