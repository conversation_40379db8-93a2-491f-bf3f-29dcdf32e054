import {
  Controller,
  Get,
  Post,
  Body,
  UseGuards,
  Param,
  ParseUUIDPipe,
  Query,
} from '@nestjs/common';
import { KeywordsService } from '../services/keywords.service';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';
import { CurrentUser } from '../../../core/decorators';
import { CreateKeywordBatchDto, CreateKeywordsDto } from '../dto/keywords.dto';
import { BatchListDto } from '../dto/pagination.dto';
import { KeywordsListQueryDto } from '../dto/keywords-list.dto';

@Controller({ path: 'keywords', version: '1' })
@UseGuards(JwtAuthGuard)
export class KeywordsController {
  constructor(private keywordsService: KeywordsService) {}

  // Get all user's keywords with fuzzy search
  @Get()
  async getAllKeywords(
    @CurrentUser() user: any,
    @Query() query: KeywordsListQueryDto
  ) {
    return this.keywordsService.getAllUserKeywords(user.id, query);
  }

  // Main endpoint to process keywords array and create batch
  @Post()
  async processKeywords(
    @CurrentUser() user: any,
    @Body() createKeywordsDto: CreateKeywordsDto,
  ) {
    return this.keywordsService.processKeywords(user.id, createKeywordsDto);
  }

  // Get all user's batches
  @Get('batches')
  async getUserBatches(
    @CurrentUser() user: any,
    @Query() query: BatchListDto
  ) {
    return this.keywordsService.getUserKeywordBatches(user.id, query);
  }

  // Get specific batch with keywords
  @Get('batches/:id')
  async getBatch(
    @CurrentUser() user: any,
    @Param('id', ParseUUIDPipe) batchId: string,
  ) {
    return this.keywordsService.getBatchWithKeywords(user.id, batchId);
  }

  // Legacy endpoint for batch creation (keeping for compatibility)
  @Post('batches')
  async createBatch(
    @CurrentUser() user: any,
    @Body() createBatchDto: CreateKeywordBatchDto,
  ) {
    return this.keywordsService.createKeywordBatch(user.id, createBatchDto);
  }

  @Get('test')
  async testAuth(@CurrentUser() user: any) {
    return {
      message: 'API v1 authentication working!',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
      timestamp: new Date().toISOString(),
    };
  }
}

// Temporary test endpoint without auth guard for queue testing
@Controller({ path: 'queue-test', version: '1' })
export class QueueTestController {

  @Post('test-job')
  async testJobCreation() {
    return {
      message: 'Queue system is ready for testing. Create a user first to test job creation.',
      instructions: 'Use POST /auth/register to create a user, then use the returned userId for testing',
      queueStatus: 'Redis connected, API running, Worker ready',
      timestamp: new Date().toISOString(),
    };
  }
}