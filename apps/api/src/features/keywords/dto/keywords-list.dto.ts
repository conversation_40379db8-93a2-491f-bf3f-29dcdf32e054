import { IsOptional, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';

export class KeywordsListQueryDto {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @IsEnum(['pending', 'processing', 'completed', 'failed'])
  status?: 'pending' | 'processing' | 'completed' | 'failed';

  @IsOptional()
  @IsUUID()
  batchId?: string;

  @IsOptional()
  @IsEnum(['google', 'bing'])
  searchEngine?: 'google' | 'bing';
}

export interface KeywordWithResultsDto {
  id: string;
  keyword: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  retryCount: number;
  searchEngine: 'google' | 'bing';
  createdAt: string;
  updatedAt: string;
  batch: {
    id: string;
    name: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
  };
  searchResult: {
    id: string;
    totalAds: number;
    totalLinks: number;
    scrapedAt: string;
  } | null;
}

export interface KeywordsListResponseDto {
  keywords: KeywordWithResultsDto[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: {
    search: string | null;
    status: string | null;
    batchId: string | null;
    searchEngine: string | null;
  };
}