import { IsString, <PERSON><PERSON><PERSON>y, <PERSON>rrayMinSize, ArrayMaxSize, IsO<PERSON>al, IsEnum } from 'class-validator';

// Main DTO for processing keywords array
export class CreateKeywordsDto {
  @IsString()
  name: string;

  @IsArray()
  @ArrayMinSize(1, { message: 'At least 1 keyword is required' })
  @ArrayMaxSize(100, { message: 'Maximum 100 keywords allowed per batch' })
  @IsString({ each: true })
  keywords: string[];

  @IsOptional()
  @IsEnum(['google', 'bing'], { message: 'Search engine must be google or bing' })
  searchEngine?: 'google' | 'bing' = 'google';
}

// Legacy DTO for batch creation
export class CreateKeywordBatchDto {
  @IsString()
  name: string;

  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(100)
  @IsString({ each: true })
  keywords: string[];
}

// Response interfaces
export interface KeywordBatch {
  id: string;
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  totalKeywords: number;
  completedKeywords: number;
  failedKeywords: number;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface Keyword {
  id: string;
  batchId: string;
  keyword: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  retryCount: number;
  searchEngine: 'google' | 'bing';
  createdAt: string;
  updatedAt: string;
}

export interface BatchWithKeywords extends KeywordBatch {
  keywords: Keyword[];
}

export interface ProcessKeywordsResponse {
  batch: KeywordBatch;
  totalKeywords: number;
  message: string;
}