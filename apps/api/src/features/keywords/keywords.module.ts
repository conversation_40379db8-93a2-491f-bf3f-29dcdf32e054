import { Module } from '@nestjs/common';
import { KeywordsController, QueueTestController } from './controllers/keywords.controller';
import { KeywordsService } from './services/keywords.service';
import { QueueModule } from '../../core/queue';

@Module({
  imports: [QueueModule],
  controllers: [KeywordsController, QueueTestController],
  providers: [KeywordsService],
  exports: [KeywordsService],
})
export class KeywordsModule {}