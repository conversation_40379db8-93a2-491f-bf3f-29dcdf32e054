import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { KeywordsService } from '../keywords.service';
import { QueueService } from '../../../../core/queue/queue.service';
import { 
  UserFactory, 
  KeywordFactory, 
  BatchFactory,
  setupTest,
  createMockDb
} from '@search-keywords-scraper/test-utils';

// Mock the database module
jest.mock('../../../../database/database.config', () => ({
  db: {
    select: jest.fn(),
    insert: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    transaction: jest.fn(),
  }
}));

describe('KeywordsService', () => {
  let service: KeywordsService;
  let mockQueueService: jest.Mocked<QueueService>;
  let mockDb: any;
  // let mockQueueProvider: MockQueueProvider;

  beforeEach(async () => {
    setupTest();
    // mockQueueProvider = getMockQueueProvider();

    // Create mock database
    mockDb = createMockDb();

    // Replace the imported db with mock
    (require('../../../../database/database.config') as any).db = mockDb;

    // Mock QueueService
    mockQueueService = {
      enqueueKeywordJob: jest.fn().mockResolvedValue(undefined),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KeywordsService,
        {
          provide: QueueService,
          useValue: mockQueueService,
        },
      ],
    }).compile();

    service = module.get<KeywordsService>(KeywordsService);
  });

  describe('processKeywords', () => {
    const testUser = UserFactory.create();
    const validKeywords = KeywordFactory.createValidKeywords(5);

    beforeEach(() => {
      // Mock user exists check
      mockDb.returning.mockResolvedValueOnce([testUser]);
    });

    it('should successfully process valid keywords and create batch', async () => {
      const batchData = {
        name: 'Test Batch',
        keywords: validKeywords,
        searchEngine: 'google' as const,
      };

      const mockBatch = BatchFactory.create({
        name: batchData.name,
        totalKeywords: validKeywords.length,
        userId: testUser.id,
      });

      const mockKeywords = validKeywords.map((keyword: string) => 
        KeywordFactory.create({
          batchId: mockBatch.id,
          keyword,
          searchEngine: 'google',
        })
      );

      // Mock database operations
      mockDb.transaction.mockImplementation(async (callback: any) => {
        // Mock batch creation
        mockDb.returning.mockResolvedValueOnce([mockBatch]);
        // Mock keywords creation
        mockDb.returning.mockResolvedValueOnce(mockKeywords);

        return callback({
          insert: mockDb.insert,
          values: mockDb.values,
          returning: mockDb.returning,
        });
      });

      const result = await service.processKeywords(testUser.id, batchData);

      expect(result).toEqual({
        batch: expect.objectContaining({
          id: mockBatch.id,
          name: mockBatch.name,
          status: mockBatch.status,
          totalKeywords: mockBatch.totalKeywords,
          userId: mockBatch.userId,
        }),
        totalKeywords: validKeywords.length,
        message: `Successfully created batch "${batchData.name}" with ${validKeywords.length} jobs queued for processing`,
      });

      expect(mockQueueService.enqueueKeywordJob).toHaveBeenCalledTimes(validKeywords.length);
    });

    it('should reject keywords array with more than 100 items', async () => {
      const tooManyKeywords = Array.from({ length: 101 }, (_, i) => `keyword${i}`);
      
      const batchData = {
        name: 'Too Many Keywords',
        keywords: tooManyKeywords,
        searchEngine: 'google' as const,
      };

      // Note: This should fail before checking user, but we'll mock user anyway
      await expect(service.processKeywords(testUser.id, batchData))
        .rejects.toThrow('Keywords must be between 1 and 500 characters');
    });

    it('should reject keywords with invalid length', async () => {
      const invalidKeywords = [
        '', // Empty
        'a'.repeat(501), // Too long
        'valid keyword', // This one is valid
      ];

      const batchData = {
        name: 'Invalid Keywords',
        keywords: invalidKeywords,
        searchEngine: 'google' as const,
      };

      await expect(service.processKeywords(testUser.id, batchData))
        .rejects.toThrow('Keywords must be between 1 and 500 characters');
    });

    it('should remove duplicates and process unique keywords only', async () => {
      const keywordsWithDuplicates = ['keyword1', 'keyword2', 'keyword1', 'keyword3', 'keyword2'];
      const expectedUniqueKeywords = ['keyword1', 'keyword2', 'keyword3'];

      const batchData = {
        name: 'Duplicate Keywords',
        keywords: keywordsWithDuplicates,
        searchEngine: 'google' as const,
      };

      const mockBatch = BatchFactory.create({
        name: batchData.name,
        totalKeywords: expectedUniqueKeywords.length,
        userId: testUser.id,
      });

      const mockKeywords = expectedUniqueKeywords.map(keyword => 
        KeywordFactory.create({
          batchId: mockBatch.id,
          keyword,
          searchEngine: 'google',
        })
      );

      mockDb.transaction.mockImplementation(async (callback: any) => {
        mockDb.returning.mockResolvedValueOnce([mockBatch]);
        mockDb.returning.mockResolvedValueOnce(mockKeywords);

        return callback({
          insert: mockDb.insert,
          values: mockDb.values,
          returning: mockDb.returning,
        });
      });

      const result = await service.processKeywords(testUser.id, batchData);

      expect(result.totalKeywords).toBe(expectedUniqueKeywords.length);
      expect(mockQueueService.enqueueKeywordJob).toHaveBeenCalledTimes(expectedUniqueKeywords.length);
    });

    it('should reject empty keywords array after filtering', async () => {
      const emptyKeywords = ['', '   ', '', '  '];

      const batchData = {
        name: 'Empty Keywords',
        keywords: emptyKeywords,
        searchEngine: 'google' as const,
      };

      await expect(service.processKeywords(testUser.id, batchData))
        .rejects.toThrow('At least one valid keyword is required');
    });

    it('should throw NotFoundException for non-existent user', async () => {
      mockDb.returning.mockResolvedValueOnce([]); // No user found

      const batchData = {
        name: 'Test Batch',
        keywords: validKeywords,
        searchEngine: 'google' as const,
      };

      await expect(service.processKeywords('non-existent-user', batchData))
        .rejects.toThrow(NotFoundException);
    });

    it('should handle database transaction errors', async () => {
      const batchData = {
        name: 'Test Batch',
        keywords: validKeywords,
        searchEngine: 'google' as const,
      };

      mockDb.transaction.mockRejectedValue(new Error('Database error'));

      await expect(service.processKeywords(testUser.id, batchData))
        .rejects.toThrow(BadRequestException);
    });
  });

  describe('getAllUserKeywords', () => {
    const testUser = UserFactory.create();

    it('should return paginated keywords with fuzzy search', async () => {
      const mockKeywords = Array.from({ length: 5 }, () => ({
        id: KeywordFactory.create().id,
        keyword: 'test keyword',
        status: 'completed',
        retryCount: 0,
        searchEngine: 'google',
        createdAt: new Date(),
        updatedAt: new Date(),
        batchId: BatchFactory.create().id,
        batchName: 'Test Batch',
        batchStatus: 'completed',
        totalAds: 5,
        totalLinks: 20,
        scrapedAt: new Date(),
        searchResultId: 'result-id',
      }));

      // Mock count query
      mockDb.returning.mockResolvedValueOnce([{ total: mockKeywords.length }]);
      // Mock keywords query
      mockDb.returning.mockResolvedValueOnce(mockKeywords);

      const query = {
        search: 'test',
        page: 1,
        limit: 10,
      };

      const result = await service.getAllUserKeywords(testUser.id, query);

      expect(result).toEqual({
        keywords: expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(String),
            keyword: 'test keyword',
            status: 'completed',
            batch: expect.objectContaining({
              name: 'Test Batch',
            }),
            searchResult: expect.objectContaining({
              totalAds: 5,
              totalLinks: 20,
            }),
          }),
        ]),
        pagination: {
          page: 1,
          limit: 10,
          total: mockKeywords.length,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
        filters: {
          search: 'test',
          status: null,
          batchId: null,
          searchEngine: null,
        },
      });
    });

    it('should apply status filter correctly', async () => {
      const query = {
        status: 'completed' as const,
        page: 1,
        limit: 10,
      };

      mockDb.returning.mockResolvedValueOnce([{ total: 0 }]);
      mockDb.returning.mockResolvedValueOnce([]);

      await service.getAllUserKeywords(testUser.id, query);

      // Verify that the where condition includes status filter
      expect(mockDb.where).toHaveBeenCalled();
    });

    it('should handle pagination correctly', async () => {
      const query = {
        page: 2,
        limit: 5,
      };

      mockDb.returning.mockResolvedValueOnce([{ total: 15 }]);
      mockDb.returning.mockResolvedValueOnce([]);

      const result = await service.getAllUserKeywords(testUser.id, query);

      expect(result.pagination).toEqual({
        page: 2,
        limit: 5,
        total: 15,
        totalPages: 3,
        hasNext: true,
        hasPrev: true,
      });

      expect(mockDb.offset).toHaveBeenCalledWith(5); // (page - 1) * limit
      expect(mockDb.limit).toHaveBeenCalledWith(5);
    });

    it('should limit results to maximum 100 per page', async () => {
      const query = {
        page: 1,
        limit: 150, // Exceeds maximum
      };

      mockDb.returning.mockResolvedValueOnce([{ total: 0 }]);
      mockDb.returning.mockResolvedValueOnce([]);

      await service.getAllUserKeywords(testUser.id, query);

      expect(mockDb.limit).toHaveBeenCalledWith(100); // Should be capped at 100
    });
  });

  describe('getBatchWithKeywords', () => {
    const testUser = UserFactory.create();
    const testBatch = BatchFactory.create({ userId: testUser.id });

    it('should return batch with its keywords', async () => {
      const mockBatchKeywords = KeywordFactory.createMany(3, { batchId: testBatch.id });

      // Mock batch query
      mockDb.returning.mockResolvedValueOnce([testBatch]);
      // Mock keywords query  
      mockDb.returning.mockResolvedValueOnce(mockBatchKeywords);

      const result = await service.getBatchWithKeywords(testUser.id, testBatch.id);

      expect(result).toEqual(expect.objectContaining({
        id: testBatch.id,
        name: testBatch.name,
        keywords: expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(String),
            keyword: expect.any(String),
          }),
        ]),
      }));
    });

    it('should throw NotFoundException for non-existent batch', async () => {
      mockDb.returning.mockResolvedValueOnce([]); // No batch found

      await expect(service.getBatchWithKeywords(testUser.id, 'non-existent-batch'))
        .rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException for batch owned by different user', async () => {
      const otherUserBatch = BatchFactory.create({ userId: 'other-user-id' });
      mockDb.returning.mockResolvedValueOnce([]);

      await expect(service.getBatchWithKeywords(testUser.id, otherUserBatch.id))
        .rejects.toThrow(NotFoundException);
    });
  });
});