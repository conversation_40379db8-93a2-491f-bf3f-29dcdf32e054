import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { KeywordsService } from '../keywords.service';
import { QueueService } from '../../../../core/queue/queue.service';

// Mock the entire database module before imports
jest.mock('../../../../database/database.config', () => ({
  db: {
    select: jest.fn(),
    insert: jest.fn(),
    update: jest.fn(),
    from: jest.fn(),
    where: jest.fn(),
    limit: jest.fn(),
    transaction: jest.fn(),
  }
}));
jest.mock('@search-keywords-scraper/database', () => ({
  keywordBatches: {},
  keywords: {},
  users: {},
  searchResults: {},
}));

const mockDb = {
  select: jest.fn(),
  insert: jest.fn(),
  update: jest.fn(),
  from: jest.fn(),
  where: jest.fn(),
  limit: jest.fn(),
  transaction: jest.fn(),
};

// Replace db import
const { db } = require('../../../../database/database.config');
Object.assign(db, mockDb);

describe('KeywordsService - Basic Tests', () => {
  let service: KeywordsService;
  let mockQueueService: jest.Mocked<QueueService>;

  beforeEach(async () => {
    jest.clearAllMocks();

    // Mock QueueService
    mockQueueService = {
      enqueueKeywordJob: jest.fn().mockResolvedValue(undefined),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KeywordsService,
        {
          provide: QueueService,
          useValue: mockQueueService,
        },
      ],
    }).compile();

    service = module.get<KeywordsService>(KeywordsService);

    // Setup default mock behavior
    mockDb.select.mockReturnThis();
    mockDb.from.mockReturnThis();
    mockDb.where.mockReturnThis();
    mockDb.limit.mockReturnThis();
  });

  describe('processKeywords', () => {
    it('should reject empty keywords array', async () => {
      const testUser = { id: 'user-id', email: '<EMAIL>' };
      // Mock user exists
      mockDb.limit.mockResolvedValueOnce([testUser]);

      const batchData = {
        name: 'Empty Keywords',
        keywords: ['', '   ', ''], // These fail the length validation (length === 0)
        searchEngine: 'google' as const,
      };

      // The service validates length first, so empty strings fail with the length message
      await expect(service.processKeywords('user-id', batchData))
        .rejects.toThrow('Keywords must be between 1 and 500 characters');
    });

    it('should reject keywords with invalid length', async () => {
      const testUser = { id: 'user-id', email: '<EMAIL>' };
      // Mock user exists
      mockDb.limit.mockResolvedValueOnce([testUser]);

      const batchData = {
        name: 'Invalid Keywords',
        keywords: ['', 'a'.repeat(501)],
        searchEngine: 'google' as const,
      };

      await expect(service.processKeywords('user-id', batchData))
        .rejects.toThrow('Keywords must be between 1 and 500 characters');
    });

    it('should successfully process valid keywords when user exists', async () => {
      const testUser = { id: 'user-id', email: '<EMAIL>' };
      const validKeywords = ['keyword1', 'keyword2', 'keyword3'];
      
      // Mock user exists
      mockDb.limit.mockResolvedValueOnce([testUser]);

      const mockBatch = {
        id: 'batch-id',
        name: 'Test Batch',
        status: 'pending',
        totalKeywords: 3,
        completedKeywords: 0,
        failedKeywords: 0,
        userId: 'user-id',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockKeywords = validKeywords.map(keyword => ({
        id: `${keyword}-id`,
        batchId: 'batch-id',
        keyword,
        status: 'pending',
        retryCount: 0,
        searchEngine: 'google',
      }));

      // Mock transaction
      mockDb.transaction.mockImplementation(async (callback) => {
        const mockTx = {
          insert: jest.fn().mockReturnThis(),
          values: jest.fn().mockReturnThis(),
          returning: jest.fn()
            .mockResolvedValueOnce([mockBatch])   // batch creation
            .mockResolvedValueOnce(mockKeywords), // keywords creation
        };
        
        return callback(mockTx);
      });

      const batchData = {
        name: 'Test Batch',
        keywords: validKeywords,
        searchEngine: 'google' as const,
      };

      const result = await service.processKeywords('user-id', batchData);

      expect(result.totalKeywords).toBe(3);
      expect(result.batch.name).toBe('Test Batch');
      expect(mockQueueService.enqueueKeywordJob).toHaveBeenCalledTimes(3);
    });

    it('should throw NotFoundException when user does not exist', async () => {
      // Mock user not found
      mockDb.limit.mockResolvedValueOnce([]);

      const batchData = {
        name: 'Test Batch',
        keywords: ['keyword1'],
        searchEngine: 'google' as const,
      };

      await expect(service.processKeywords('non-existent-user', batchData))
        .rejects.toThrow(NotFoundException);
    });
  });

  describe('input validation', () => {
    it('should remove duplicate keywords', () => {
      // This tests the logic in processKeywords
      const keywords = ['keyword1', 'keyword2', 'keyword1', 'keyword3'];
      const uniqueKeywords = [...new Set(keywords.filter(k => k.trim().length > 0))];
      
      expect(uniqueKeywords).toEqual(['keyword1', 'keyword2', 'keyword3']);
      expect(uniqueKeywords).toHaveLength(3);
    });

    it('should validate keyword length constraints', () => {
      const validKeyword = 'a'.repeat(500); // Max valid length
      const invalidKeyword = 'a'.repeat(501); // Too long
      const emptyKeyword = '';

      expect(validKeyword.length).toBe(500);
      expect(validKeyword.length <= 500 && validKeyword.length > 0).toBe(true);
      
      expect(invalidKeyword.length).toBe(501);
      expect(invalidKeyword.length > 500).toBe(true);
      
      expect(emptyKeyword.length).toBe(0);
    });

    it('should filter out empty and whitespace-only keywords', () => {
      const keywords = ['keyword1', '', '   ', 'keyword2', '\t\n', 'keyword3'];
      const filtered = keywords.filter(k => k.trim().length > 0);
      
      expect(filtered).toEqual(['keyword1', 'keyword2', 'keyword3']);
    });
  });
});