import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { KeywordsService } from '../keywords.service';
import { QueueService } from '../../../../core/queue/queue.service';

// Mock data factories (inline for simplicity)
const createTestUser = () => ({
  id: '123e4567-e89b-12d3-a456-426614174000',
  email: '<EMAIL>',
  createdAt: new Date(),
  updatedAt: new Date(),
});

const createTestKeywords = (count: number = 5): string[] => 
  Array.from({ length: count }, (_, i) => `keyword${i + 1}`);

const createTestBatch = (overrides = {}) => ({
  id: '123e4567-e89b-12d3-a456-426614174001',
  userId: '123e4567-e89b-12d3-a456-426614174000',
  name: 'Test Batch',
  status: 'pending',
  totalKeywords: 5,
  completedKeywords: 0,
  failedKeywords: 0,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

// Mock the database module
jest.mock('../../../../database/database.config', () => ({
  db: {
    select: jest.fn(),
    insert: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    transaction: jest.fn(),
  }
}));

describe('KeywordsService', () => {
  let service: KeywordsService;
  let mockQueueService: jest.Mocked<QueueService>;
  let mockDb: any;

  beforeEach(async () => {
    // Reset all mocks
    jest.clearAllMocks();

    // Create mock database with chainable methods
    mockDb = {
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
      returning: jest.fn(),
      set: jest.fn().mockReturnThis(),
      transaction: jest.fn(),
      groupBy: jest.fn().mockReturnThis(),
    };

    // Replace the imported db with mock
    (require('../../../../database/database.config') as any).db = mockDb;

    // Mock QueueService
    mockQueueService = {
      enqueueKeywordJob: jest.fn().mockResolvedValue(undefined),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KeywordsService,
        {
          provide: QueueService,
          useValue: mockQueueService,
        },
      ],
    }).compile();

    service = module.get<KeywordsService>(KeywordsService);
  });

  describe('processKeywords', () => {
    const testUser = createTestUser();
    const validKeywords = createTestKeywords(5);

    beforeEach(() => {
      // Reset mocks
      jest.clearAllMocks();
      
      // Mock database chain methods to return mockDb
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.leftJoin.mockReturnValue(mockDb);
      mockDb.orderBy.mockReturnValue(mockDb);
      mockDb.limit.mockReturnValue(mockDb);
      mockDb.offset.mockReturnValue(mockDb);
      mockDb.values.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.groupBy.mockReturnValue(mockDb);
    });

    it('should successfully process valid keywords and create batch', async () => {
      // Mock user check - the final chain should resolve to the user array
      const userQuery = Promise.resolve([testUser]);
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockReturnValue(userQuery);
      
      // Clear after setting up user query
      jest.clearAllMocks();
      
      // Re-setup chain methods
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockReturnValue(userQuery);
      const batchData = {
        name: 'Test Batch',
        keywords: validKeywords,
        searchEngine: 'google' as const,
      };

      const mockBatch = createTestBatch({
        name: batchData.name,
        totalKeywords: validKeywords.length,
        userId: testUser.id,
      });

      const mockKeywords = validKeywords.map((keyword: string) => ({
        id: `keyword-${keyword}`,
        batchId: mockBatch.id,
        keyword,
        status: 'pending',
        retryCount: 0,
        searchEngine: 'google',
        createdAt: new Date(),
        updatedAt: new Date(),
      }));

      // Mock database operations
      mockDb.transaction.mockImplementation(async (callback: any) => {
        // Mock batch creation
        mockDb.returning.mockResolvedValueOnce([mockBatch]);
        // Mock keywords creation
        mockDb.returning.mockResolvedValueOnce(mockKeywords);

        return callback({
          insert: mockDb.insert,
          values: mockDb.values,
          returning: mockDb.returning,
        });
      });

      const result = await service.processKeywords(testUser.id, batchData);

      expect(result).toEqual({
        batch: expect.objectContaining({
          id: mockBatch.id,
          name: mockBatch.name,
          status: mockBatch.status,
          totalKeywords: mockBatch.totalKeywords,
          userId: mockBatch.userId,
        }),
        totalKeywords: validKeywords.length,
        message: `Successfully created batch "${batchData.name}" with ${validKeywords.length} jobs queued for processing`,
      });

      expect(mockQueueService.enqueueKeywordJob).toHaveBeenCalledTimes(validKeywords.length);
    });

    it('should reject keywords with invalid length', async () => {
      const invalidKeywords = [
        '', // Empty
        'a'.repeat(501), // Too long
        'valid keyword', // This one is valid
      ];

      const batchData = {
        name: 'Invalid Keywords',
        keywords: invalidKeywords,
        searchEngine: 'google' as const,
      };

      await expect(service.processKeywords(testUser.id, batchData))
        .rejects.toThrow('Keywords must be between 1 and 500 characters');
    });

    it('should remove duplicates and process unique keywords only', async () => {
      const keywordsWithDuplicates = ['keyword1', 'keyword2', 'keyword1', 'keyword3', 'keyword2'];
      const expectedUniqueKeywords = ['keyword1', 'keyword2', 'keyword3'];

      const batchData = {
        name: 'Duplicate Keywords',
        keywords: keywordsWithDuplicates,
        searchEngine: 'google' as const,
      };

      const mockBatch = createTestBatch({
        name: batchData.name,
        totalKeywords: expectedUniqueKeywords.length,
        userId: testUser.id,
      });

      const mockKeywords = expectedUniqueKeywords.map(keyword => ({
        id: `keyword-${keyword}`,
        batchId: mockBatch.id,
        keyword,
        status: 'pending',
        retryCount: 0,
        searchEngine: 'google',
        createdAt: new Date(),
        updatedAt: new Date(),
      }));

      mockDb.transaction.mockImplementation(async (callback: any) => {
        mockDb.returning.mockResolvedValueOnce([mockBatch]);
        mockDb.returning.mockResolvedValueOnce(mockKeywords);

        return callback({
          insert: mockDb.insert,
          values: mockDb.values,
          returning: mockDb.returning,
        });
      });

      const result = await service.processKeywords(testUser.id, batchData);

      expect(result.totalKeywords).toBe(expectedUniqueKeywords.length);
      expect(mockQueueService.enqueueKeywordJob).toHaveBeenCalledTimes(expectedUniqueKeywords.length);
    });

    it('should reject empty keywords array after filtering', async () => {
      const emptyKeywords = ['', '   ', '', '  '];

      const batchData = {
        name: 'Empty Keywords',
        keywords: emptyKeywords,
        searchEngine: 'google' as const,
      };

      await expect(service.processKeywords(testUser.id, batchData))
        .rejects.toThrow('At least one valid keyword is required');
    });

    it('should throw NotFoundException for non-existent user', async () => {
      mockDb.returning.mockResolvedValueOnce([]); // No user found

      const batchData = {
        name: 'Test Batch',
        keywords: validKeywords,
        searchEngine: 'google' as const,
      };

      await expect(service.processKeywords('non-existent-user', batchData))
        .rejects.toThrow(NotFoundException);
    });

    it('should handle database transaction errors', async () => {
      const batchData = {
        name: 'Test Batch',
        keywords: validKeywords,
        searchEngine: 'google' as const,
      };

      mockDb.transaction.mockRejectedValue(new Error('Database error'));

      await expect(service.processKeywords(testUser.id, batchData))
        .rejects.toThrow(BadRequestException);
    });
  });

  describe('getAllUserKeywords', () => {
    const testUser = createTestUser();

    it('should return paginated keywords with fuzzy search', async () => {
      const mockKeywords = Array.from({ length: 5 }, (_, i) => ({
        id: `keyword-${i}`,
        keyword: 'test keyword',
        status: 'completed',
        retryCount: 0,
        searchEngine: 'google',
        createdAt: new Date(),
        updatedAt: new Date(),
        batchId: 'batch-id',
        batchName: 'Test Batch',
        batchStatus: 'completed',
        totalAds: 5,
        totalLinks: 20,
        scrapedAt: new Date(),
        searchResultId: 'result-id',
      }));

      // Mock count query
      mockDb.returning.mockResolvedValueOnce([{ total: mockKeywords.length }]);
      // Mock keywords query
      mockDb.returning.mockResolvedValueOnce(mockKeywords);

      const query = {
        search: 'test',
        page: 1,
        limit: 10,
      };

      const result = await service.getAllUserKeywords(testUser.id, query);

      expect(result).toEqual({
        keywords: expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(String),
            keyword: 'test keyword',
            status: 'completed',
            batch: expect.objectContaining({
              name: 'Test Batch',
            }),
            searchResult: expect.objectContaining({
              totalAds: 5,
              totalLinks: 20,
            }),
          }),
        ]),
        pagination: {
          page: 1,
          limit: 10,
          total: mockKeywords.length,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
        filters: {
          search: 'test',
          status: null,
          batchId: null,
          searchEngine: null,
        },
      });
    });

    it('should limit results to maximum 100 per page', async () => {
      const query = {
        page: 1,
        limit: 150, // Exceeds maximum
      };

      mockDb.returning.mockResolvedValueOnce([{ total: 0 }]);
      mockDb.returning.mockResolvedValueOnce([]);

      await service.getAllUserKeywords(testUser.id, query);

      expect(mockDb.limit).toHaveBeenCalledWith(100); // Should be capped at 100
    });
  });

  describe('getBatchWithKeywords', () => {
    const testUser = createTestUser();
    const testBatch = createTestBatch({ userId: testUser.id });

    it('should return batch with its keywords', async () => {
      const mockBatchKeywords = Array.from({ length: 3 }, (_, i) => ({
        id: `keyword-${i}`,
        batchId: testBatch.id,
        keyword: `keyword${i + 1}`,
        status: 'pending',
        retryCount: 0,
        searchEngine: 'google',
        createdAt: new Date(),
        updatedAt: new Date(),
      }));

      // Mock batch query
      mockDb.returning.mockResolvedValueOnce([testBatch]);
      // Mock keywords query  
      mockDb.returning.mockResolvedValueOnce(mockBatchKeywords);

      const result = await service.getBatchWithKeywords(testUser.id, testBatch.id);

      expect(result).toEqual(expect.objectContaining({
        id: testBatch.id,
        name: testBatch.name,
        keywords: expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(String),
            keyword: expect.any(String),
          }),
        ]),
      }));
    });

    it('should throw NotFoundException for non-existent batch', async () => {
      mockDb.returning.mockResolvedValueOnce([]); // No batch found

      await expect(service.getBatchWithKeywords(testUser.id, 'non-existent-batch'))
        .rejects.toThrow(NotFoundException);
    });
  });
});