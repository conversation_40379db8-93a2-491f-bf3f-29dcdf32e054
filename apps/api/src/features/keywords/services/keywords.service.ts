import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { eq, and, desc, count, ilike, or, type SQL } from 'drizzle-orm';
import {
  keywordBatches,
  keywords,
  users,
  searchResults,
} from '@search-keywords-scraper/database';
import { db } from '../../../database/database.config';
import type {
  CreateKeywordsDto,
  CreateKeywordBatchDto,
  ProcessKeywordsResponse,
  BatchWithKeywords,
} from '../dto/keywords.dto';
import type { BatchListDto } from '../dto/pagination.dto';
import { QueueService } from '../../../core/queue/queue.service';
import { KeywordScrapingJob, JOB_PRIORITIES } from '@search-keywords-scraper/queue';

@Injectable()
export class KeywordsService {
  constructor(private queueService: QueueService) {}
  // Main method to process keywords array and create batch with keywords
  async processKeywords(
    userId: string,
    data: CreateKeywordsDto,
  ): Promise<ProcessKeywordsResponse> {
    // Validate user exists
    const user = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);
    if (!user.length) {
      throw new NotFoundException('User not found');
    }

    // Validate keywords
    if (
      data.keywords.some(
        (keyword) => keyword.length > 500 || keyword.length === 0,
      )
    ) {
      throw new BadRequestException(
        'Keywords must be between 1 and 500 characters',
      );
    }

    // Remove duplicates and empty keywords
    const uniqueKeywords = [
      ...new Set(data.keywords.filter((k) => k.trim().length > 0)),
    ];

    if (uniqueKeywords.length === 0) {
      throw new BadRequestException('At least one valid keyword is required');
    }

    try {
      return await db.transaction(async (tx: any) => {
        // Create the batch
        const [batch] = await tx
          .insert(keywordBatches)
          .values({
            userId: userId,
            name: data.name,
            status: 'pending',
            totalKeywords: uniqueKeywords.length,
            completedKeywords: 0,
            failedKeywords: 0,
          })
          .returning();

        // Create individual keywords
        const keywordInserts = uniqueKeywords.map((keyword) => ({
          batchId: batch.id,
          keyword: keyword.trim(),
          status: 'pending' as const,
          retryCount: 0,
          searchEngine: data.searchEngine || ('google' as const),
        }));

        const insertedKeywords = await tx.insert(keywords).values(keywordInserts).returning();

        // Enqueue scraping jobs
        const jobPromises = insertedKeywords.map((keywordRecord: any) => {
          const jobData: KeywordScrapingJob = {
            keywordId: keywordRecord.id,
            batchId: batch.id,
            userId: userId,
            keyword: keywordRecord.keyword,
            searchEngine: keywordRecord.searchEngine,
            retryAttempt: 0,
          };

          return this.queueService.enqueueKeywordJob(jobData, JOB_PRIORITIES.NORMAL);
        });

        await Promise.all(jobPromises);

        return {
          batch: {
            id: batch.id,
            name: batch.name,
            status: batch.status,
            totalKeywords: batch.totalKeywords,
            completedKeywords: batch.completedKeywords,
            failedKeywords: batch.failedKeywords,
            userId: batch.userId,
            createdAt: batch.createdAt.toISOString(),
            updatedAt: batch.updatedAt.toISOString(),
          },
          totalKeywords: uniqueKeywords.length,
          message: `Successfully created batch "${data.name}" with ${uniqueKeywords.length} jobs queued for processing`,
        };
      });
    } catch (error) {
      throw new BadRequestException(
        `Failed to create keyword batch: ${error.message}`,
      );
    }
  }

  // Get all user's batches with pagination
  async getUserKeywordBatches(userId: string, query: BatchListDto = {}) {
    const page = query.page || 1;
    const limit = query.limit || 10;
    const offset = (page - 1) * limit;

    // Build where conditions
    let whereConditions = eq(keywordBatches.userId, userId);
    if (query.status) {
      const statusCondition = and(whereConditions, eq(keywordBatches.status, query.status));
      if (statusCondition) {
        whereConditions = statusCondition;
      }
    }

    // Get total count
    const [{ total }] = await db
      .select({ total: count() })
      .from(keywordBatches)
      .where(whereConditions);

    // Get paginated batches
    const userBatches = await db
      .select()
      .from(keywordBatches)
      .where(whereConditions)
      .orderBy(desc(keywordBatches.createdAt))
      .limit(limit)
      .offset(offset);

    return {
      batches: userBatches.map((batch: any) => ({
        id: batch.id,
        name: batch.name,
        status: batch.status,
        totalKeywords: batch.totalKeywords,
        completedKeywords: batch.completedKeywords,
        failedKeywords: batch.failedKeywords,
        userId: batch.userId,
        createdAt: batch.createdAt.toISOString(),
        updatedAt: batch.updatedAt.toISOString(),
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    };
  }

  // Get specific batch with all its keywords
  async getBatchWithKeywords(
    userId: string,
    batchId: string,
  ): Promise<BatchWithKeywords> {
    // First get the batch and verify ownership
    const [batch] = await db
      .select()
      .from(keywordBatches)
      .where(
        and(eq(keywordBatches.id, batchId), eq(keywordBatches.userId, userId)),
      )
      .limit(1);

    if (!batch) {
      throw new NotFoundException('Batch not found or access denied');
    }

    // Get all keywords for this batch
    const batchKeywords = await db
      .select()
      .from(keywords)
      .where(eq(keywords.batchId, batchId))
      .orderBy(keywords.createdAt);

    return {
      id: batch.id,
      name: batch.name,
      status: batch.status,
      totalKeywords: batch.totalKeywords,
      completedKeywords: batch.completedKeywords,
      failedKeywords: batch.failedKeywords,
      userId: batch.userId,
      createdAt: batch.createdAt.toISOString(),
      updatedAt: batch.updatedAt.toISOString(),
      keywords: batchKeywords.map((keyword: any) => ({
        id: keyword.id,
        batchId: keyword.batchId,
        keyword: keyword.keyword,
        status: keyword.status,
        retryCount: keyword.retryCount,
        searchEngine: keyword.searchEngine,
        createdAt: keyword.createdAt.toISOString(),
        updatedAt: keyword.updatedAt.toISOString(),
      })),
    };
  }

  // Get all user's keywords with fuzzy search and pagination
  async getAllUserKeywords(userId: string, query: any = {}) {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 20, 100); // Max 100 per page
    const offset = (page - 1) * limit;
    const search = query.search?.trim();
    const status = query.status;
    const batchId = query.batchId;
    const searchEngine = query.searchEngine;

    // Build where conditions
    let whereConditions: SQL | undefined = eq(keywordBatches.userId, userId);

    // Add search condition (fuzzy search across keyword text and batch names)
    if (search) {
      const searchPattern = `%${search}%`;
      const searchCondition = or(
        ilike(keywords.keyword, searchPattern),
        ilike(keywordBatches.name, searchPattern)
      );
      whereConditions = whereConditions ? and(whereConditions, searchCondition) : searchCondition;
    }

    // Add status filter
    if (status) {
      const statusCondition = eq(keywords.status, status);
      whereConditions = whereConditions ? and(whereConditions, statusCondition) : statusCondition;
    }

    // Add batch filter
    if (batchId) {
      const batchCondition = eq(keywords.batchId, batchId);
      whereConditions = whereConditions ? and(whereConditions, batchCondition) : batchCondition;
    }

    // Add search engine filter
    if (searchEngine) {
      const engineCondition = eq(keywords.searchEngine, searchEngine);
      whereConditions = whereConditions ? and(whereConditions, engineCondition) : engineCondition;
    }

    // Get total count
    const [{ total }] = await db
      .select({ total: count() })
      .from(keywords)
      .leftJoin(keywordBatches, eq(keywords.batchId, keywordBatches.id))
      .where(whereConditions);

    // Get paginated keywords with batch and search results data
    const keywordsWithDetails = await db
      .select({
        // Keyword fields
        id: keywords.id,
        keyword: keywords.keyword,
        status: keywords.status,
        retryCount: keywords.retryCount,
        searchEngine: keywords.searchEngine,
        createdAt: keywords.createdAt,
        updatedAt: keywords.updatedAt,
        // Batch fields
        batchId: keywordBatches.id,
        batchName: keywordBatches.name,
        batchStatus: keywordBatches.status,
        // Search results summary
        totalAds: searchResults.totalAds,
        totalLinks: searchResults.totalLinks,
        scrapedAt: searchResults.scrapedAt,
        searchResultId: searchResults.id,
      })
      .from(keywords)
      .leftJoin(keywordBatches, eq(keywords.batchId, keywordBatches.id))
      .leftJoin(searchResults, eq(keywords.id, searchResults.keywordId))
      .where(whereConditions)
      .orderBy(desc(keywords.createdAt))
      .limit(limit)
      .offset(offset);

    // Format the response
    const formattedKeywords = keywordsWithDetails.map((row) => ({
      id: row.id,
      keyword: row.keyword,
      status: row.status,
      retryCount: row.retryCount,
      searchEngine: row.searchEngine,
      createdAt: row.createdAt.toISOString(),
      updatedAt: row.updatedAt.toISOString(),
      batch: {
        id: row.batchId,
        name: row.batchName,
        status: row.batchStatus,
      },
      searchResult: row.searchResultId ? {
        id: row.searchResultId,
        totalAds: row.totalAds || 0,
        totalLinks: row.totalLinks || 0,
        scrapedAt: row.scrapedAt?.toISOString(),
      } : null,
    }));

    return {
      keywords: formattedKeywords,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      filters: {
        search: search || null,
        status: status || null,
        batchId: batchId || null,
        searchEngine: searchEngine || null,
      },
    };
  }

  // Legacy method for batch creation (keeping for compatibility)
  async createKeywordBatch(userId: string, data: CreateKeywordBatchDto) {
    return this.processKeywords(userId, {
      name: data.name,
      keywords: data.keywords,
      searchEngine: 'google',
    });
  }
}
