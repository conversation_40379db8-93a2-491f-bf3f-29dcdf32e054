import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule, KeywordsModule, DashboardModule, SearchResultsModule } from './features';
import { QueueModule } from './core/queue';

@Module({
  imports: [QueueModule, AuthModule, KeywordsModule, DashboardModule, SearchResultsModule],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
