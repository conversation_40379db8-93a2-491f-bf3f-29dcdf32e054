# Production Dockerfile for Worker
FROM node:18-alpine AS base

# Install pnpm
RUN npm install -g pnpm@8

# Install system dependencies for Playwright
RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    curl

# Set Playwright environment variables
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Dependencies stage
FROM base AS deps
WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY apps/worker/package.json apps/worker/
COPY packages/*/package.json packages/*/

# Install dependencies
RUN pnpm install --frozen-lockfile --prod=false

# Builder stage
FROM base AS builder
WORKDIR /app

# Copy dependencies
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/apps/worker/node_modules ./apps/worker/node_modules

# Copy source code
COPY . .

# Build shared packages first
RUN pnpm build --filter=!@search-keywords-scraper/web --filter=!@search-keywords-scraper/api --filter=!@search-keywords-scraper/worker

# Build the worker
RUN pnpm build --filter=@search-keywords-scraper/worker

# Production stage
FROM base AS runner
WORKDIR /app

# Set environment
ENV NODE_ENV=production

# Create non-root user
RUN addgroup --system --gid 1001 worker
RUN adduser --system --uid 1001 worker

# Copy built application and node_modules
COPY --from=builder /app/apps/worker/dist ./dist
COPY --from=builder /app/packages ./packages
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/apps/worker/node_modules ./node_modules

# Copy package.json for runtime
COPY apps/worker/package.json ./package.json

# Install Playwright browsers
RUN npx playwright install chromium --with-deps

# Change ownership
RUN chown -R worker:worker /app
USER worker

# Health check - check if worker can connect to Redis
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD node -e "console.log('Worker health check - process running')" || exit 1

# Start the application
CMD ["node", "dist/index.js"]