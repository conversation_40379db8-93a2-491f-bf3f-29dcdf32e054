FROM node:18-alpine

# Install pnpm globally
RUN npm install -g pnpm@8

# Install system dependencies for Playwright
RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont

# Set Playwright environment variables
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Set working directory
WORKDIR /app

# Copy package.json files for better caching
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY apps/worker/package.json apps/worker/
COPY packages/*/package.json packages/*/

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Build shared packages
RUN pnpm build --filter=!@search-keywords-scraper/web --filter=!@search-keywords-scraper/api --filter=!@search-keywords-scraper/worker

# Change to worker directory
WORKDIR /app/apps/worker

# Install Playwright browsers
RUN npx playwright install chromium --with-deps

# Start development server
CMD ["pnpm", "dev"]