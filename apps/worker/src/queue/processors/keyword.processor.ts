import { Job } from 'bull';
import { <PERSON>S<PERSON>raper, BingScraper } from '@search-keywords-scraper/scraper';
import { KeywordScrapingJob } from '@search-keywords-scraper/queue';
import { db, keywords, searchResults, keywordBatches } from '@search-keywords-scraper/database';
import { eq, and, count, sql } from 'drizzle-orm';

export class KeywordProcessor {
  private googleScraper: GoogleScraper;
  private bingScraper: BingScraper;
  private isInitialized: boolean = false;

  constructor(
    private scraperConfig: any,
    private logger: any
  ) {
    this.googleScraper = new GoogleScraper(scraperConfig);
    this.bingScraper = new BingScraper(scraperConfig);
  }

  async initialize(): Promise<void> {
    if (!this.isInitialized) {
      await Promise.all([
        this.googleScraper.initialize(),
        this.bingScraper.initialize()
      ]);
      this.isInitialized = true;
      this.logger.info('KeywordProcessor scrapers (Google & Bing) initialized');
    }
  }

  async shutdown(): Promise<void> {
    if (this.isInitialized) {
      await Promise.all([
        this.googleScraper.close(),
        this.bingScraper.close()
      ]);
      this.isInitialized = false;
      this.logger.info('KeywordProcessor scrapers shut down');
    }
  }

  private async updateBatchStatus(batchId: string) {
    // Get batch statistics
    const [batchStats] = await db
      .select({
        totalKeywords: keywordBatches.totalKeywords,
        completedCount: sql<number>`cast(count(case when ${keywords.status} = 'completed' then 1 end) as int)`,
        failedCount: sql<number>`cast(count(case when ${keywords.status} = 'failed' then 1 end) as int)`,
        pendingCount: sql<number>`cast(count(case when ${keywords.status} in ('pending', 'processing') then 1 end) as int)`,
      })
      .from(keywordBatches)
      .leftJoin(keywords, eq(keywords.batchId, keywordBatches.id))
      .where(eq(keywordBatches.id, batchId))
      .groupBy(keywordBatches.id, keywordBatches.totalKeywords);

    if (!batchStats) return;

    const { totalKeywords, completedCount, failedCount, pendingCount } = batchStats;
    
    // Determine batch status
    let batchStatus: 'pending' | 'processing' | 'completed' | 'failed' = 'pending';
    
    if (completedCount === totalKeywords) {
      batchStatus = 'completed';
    } else if (failedCount === totalKeywords) {
      batchStatus = 'failed';
    } else if (completedCount + failedCount === totalKeywords) {
      batchStatus = 'completed'; // Some completed, some failed, but all done
    } else if (completedCount > 0 || failedCount > 0) {
      batchStatus = 'processing';
    }

    // Update batch status and counts
    await db
      .update(keywordBatches)
      .set({
        status: batchStatus,
        completedKeywords: completedCount,
        failedKeywords: failedCount,
        updatedAt: new Date(),
      })
      .where(eq(keywordBatches.id, batchId));

    this.logger.info(`Updated batch ${batchId} status: ${batchStatus} (${completedCount}/${totalKeywords} completed, ${failedCount} failed)`);
  }

  async process(job: Job<KeywordScrapingJob>) {
    const { keywordId, batchId, keyword, searchEngine, userId } = job.data;

    try {
      this.logger.info(`Processing keyword: ${keyword}`, { jobId: job.id, keywordId });

      // Update job progress
      await job.progress(10);

      // Ensure scraper is initialized (should already be done at startup)
      if (!this.isInitialized) {
        await this.initialize();
      }

      await job.progress(25);

      // Select the appropriate scraper based on search engine
      const scraper = searchEngine === 'bing' ? this.bingScraper : this.googleScraper;
      
      // Perform the scraping
      const scrapingResult = await scraper.search({
        query: keyword,
        page: 1,
        resultsPerPage: 10,
      });

      await job.progress(75);

      // Store results in database
      const [searchResult] = await db
        .insert(searchResults)
        .values({
          keywordId: keywordId,
          totalAds: scrapingResult.adsCount,
          totalLinks: scrapingResult.totalLinksCount,
          htmlContent: scrapingResult.htmlCache,
          metadata: {
            keyword: keyword,
            searchEngine: searchEngine,
            results: scrapingResult.results,
          },
          // Enhanced link arrays for detailed analysis
          adsLinks: scrapingResult.adsLinks || [],
          organicLinks: scrapingResult.organicLinks || [],
          otherLinks: scrapingResult.otherLinks || [],
          scrapedAt: new Date(),
        })
        .returning();

      // Update keyword status
      await db
        .update(keywords)
        .set({
          status: 'completed',
          updatedAt: new Date(),
        })
        .where(eq(keywords.id, keywordId));

      // Update batch status after keyword completion
      await this.updateBatchStatus(batchId);

      await job.progress(100);

      this.logger.info(`Keyword processing completed: ${keyword}`, {
        jobId: job.id,
        keywordId,
        adsCount: scrapingResult.adsCount,
        linksCount: scrapingResult.totalLinksCount,
      });

      return {
        keywordId,
        keyword,
        adsCount: scrapingResult.adsCount,
        totalLinksCount: scrapingResult.totalLinksCount,
        searchResultId: searchResult?.id,
        status: 'completed',
      };

    } catch (error: any) {
      const errorInfo = this.analyzeError(error);
      
      this.logger.error(`Keyword processing failed: ${keyword}`, {
        jobId: job.id,
        keywordId,
        error: error?.message || String(error),
        errorType: errorInfo.type,
        isRetryable: errorInfo.isRetryable,
        stack: error?.stack,
        attemptNumber: job.attemptsMade + 1,
        maxAttempts: job.opts.attempts || 3
      });

      // Determine if we should retry based on error type
      const shouldRetry = errorInfo.isRetryable && (job.attemptsMade < (job.opts.attempts || 3) - 1);
      const finalStatus = shouldRetry ? 'pending' : 'failed';

      // Store partial results if we managed to get some data before failure
      if (errorInfo.partialResults) {
        await this.storePartialResults(keywordId, errorInfo.partialResults);
      }

      // Update keyword with failure info
      await db
        .update(keywords)
        .set({
          status: finalStatus,
          retryCount: job.attemptsMade,
          updatedAt: new Date(),
        })
        .where(eq(keywords.id, keywordId));

      // Update batch status if keyword finally failed (not just retrying)
      if (finalStatus === 'failed') {
        await this.updateBatchStatus(batchId);
      }

      // For certain error types, implement smart retry delays
      if (shouldRetry && errorInfo.suggestedDelay) {
        // This would need to be implemented at queue level for custom delays
        this.logger.info(`Will retry with suggested delay: ${errorInfo.suggestedDelay}ms`);
      }

      throw error; // Re-throw to trigger Bull's retry mechanism
    }
  }

  /**
   * Analyze error to determine type and retry strategy
   */
  private analyzeError(error: any): {
    type: string;
    isRetryable: boolean;
    suggestedDelay?: number;
    partialResults?: any;
  } {
    const errorMessage = error?.message?.toLowerCase() || '';
    
    // Network/Connection errors - usually retryable
    if (errorMessage.includes('timeout') || 
        errorMessage.includes('econnrefused') || 
        errorMessage.includes('enotfound') ||
        errorMessage.includes('network error')) {
      return {
        type: 'network',
        isRetryable: true,
        suggestedDelay: 30000 // 30 seconds
      };
    }

    // Rate limiting - retryable with longer delay
    if (errorMessage.includes('rate limit') || 
        errorMessage.includes('429') ||
        errorMessage.includes('too many requests')) {
      return {
        type: 'rate_limit',
        isRetryable: true,
        suggestedDelay: 300000 // 5 minutes
      };
    }

    // Captcha/Blocking - retryable but might need different approach
    if (errorMessage.includes('captcha') || 
        errorMessage.includes('blocked') ||
        errorMessage.includes('access denied') ||
        errorMessage.includes('403')) {
      return {
        type: 'blocked',
        isRetryable: true,
        suggestedDelay: 600000 // 10 minutes
      };
    }

    // Browser/Scraper errors - might be retryable
    if (errorMessage.includes('browser') || 
        errorMessage.includes('page closed') ||
        errorMessage.includes('context closed')) {
      return {
        type: 'browser',
        isRetryable: true,
        suggestedDelay: 60000 // 1 minute
      };
    }

    // Parsing errors - usually not retryable (page structure changed)
    if (errorMessage.includes('parsing') || 
        errorMessage.includes('element not found') ||
        errorMessage.includes('selector')) {
      return {
        type: 'parsing',
        isRetryable: false
      };
    }

    // Invalid keyword - not retryable
    if (errorMessage.includes('invalid keyword') || 
        errorMessage.includes('malformed query')) {
      return {
        type: 'invalid_keyword',
        isRetryable: false
      };
    }

    // Database errors - usually retryable
    if (errorMessage.includes('database') || 
        errorMessage.includes('connection') ||
        errorMessage.includes('sql')) {
      return {
        type: 'database',
        isRetryable: true,
        suggestedDelay: 10000 // 10 seconds
      };
    }

    // Unknown errors - be conservative and retry
    return {
      type: 'unknown',
      isRetryable: true,
      suggestedDelay: 120000 // 2 minutes
    };
  }

  /**
   * Store partial results from failed scraping attempts
   */
  private async storePartialResults(keywordId: string, partialData: any): Promise<void> {
    try {
      // Only store if we have some meaningful data
      if (!partialData || (!partialData.adsCount && !partialData.totalLinksCount && !partialData.htmlContent)) {
        return;
      }

      await db
        .insert(searchResults)
        .values({
          keywordId: keywordId,
          totalAds: partialData.adsCount || 0,
          totalLinks: partialData.totalLinksCount || 0,
          htmlContent: partialData.htmlContent || null,
          metadata: {
            isPartial: true,
            errorType: 'partial_scraping_failure',
            timestamp: new Date().toISOString(),
            ...partialData.metadata
          },
          adsLinks: partialData.adsLinks || [],
          organicLinks: partialData.organicLinks || [],
          otherLinks: partialData.otherLinks || [],
          scrapedAt: new Date(),
        })
        .onConflictDoNothing(); // Don't overwrite if already exists

      this.logger.info(`Stored partial results for keyword ${keywordId}`, {
        adsCount: partialData.adsCount || 0,
        linksCount: partialData.totalLinksCount || 0,
        hasHtml: !!partialData.htmlContent
      });

    } catch (error) {
      this.logger.warn(`Failed to store partial results for keyword ${keywordId}:`, error);
    }
  }
}