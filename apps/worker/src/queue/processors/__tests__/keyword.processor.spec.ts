import { Job } from 'bull';
import { KeywordProcessor } from '../keyword.processor';
import { KeywordScrapingJob } from '@search-keywords-scraper/queue';
import { GoogleScraper, BingScraper } from '@search-keywords-scraper/scraper';

// Mock dependencies
jest.mock('@search-keywords-scraper/scraper');
jest.mock('@search-keywords-scraper/database', () => ({
  db: {
    select: jest.fn(),
    insert: jest.fn(),
    update: jest.fn(),
  },
  keywords: {},
  searchResults: {},
  keywordBatches: {},
}));
jest.mock('drizzle-orm', () => ({
  eq: jest.fn(),
  and: jest.fn(),
  count: jest.fn(),
  sql: jest.fn(),
}));

describe('KeywordProcessor', () => {
  let processor: KeywordProcessor;
  let mockGoogleScraper: jest.Mocked<GoogleScraper>;
  let mockBingScraper: jest.Mocked<BingScraper>;
  let mockLogger: any;
  let mockDb: any;
  let mockJob: jest.Mocked<Job<KeywordScrapingJob>>;

  beforeEach(() => {
    // Mock logger
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    };

    // Mock database
    mockDb = {
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      groupBy: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
      returning: jest.fn(),
      onConflictDoNothing: jest.fn().mockReturnThis(),
    };

    // Mock scrapers
    mockGoogleScraper = {
      initialize: jest.fn().mockResolvedValue(undefined),
      close: jest.fn().mockResolvedValue(undefined),
      search: jest.fn(),
    } as any;

    mockBingScraper = {
      initialize: jest.fn().mockResolvedValue(undefined),
      close: jest.fn().mockResolvedValue(undefined),
      search: jest.fn(),
    } as any;

    // Mock the scraper constructors
    (GoogleScraper as jest.MockedClass<typeof GoogleScraper>).mockImplementation(() => mockGoogleScraper);
    (BingScraper as jest.MockedClass<typeof BingScraper>).mockImplementation(() => mockBingScraper);

    // Replace db import
    const dbModule = require('@search-keywords-scraper/database');
    Object.assign(dbModule.db, mockDb);

    const scraperConfig = {
      browser: { headless: true },
      scraping: { maxRetries: 3, retryDelay: 1000, requestsPerHour: 100 }
    };

    processor = new KeywordProcessor(scraperConfig, mockLogger);

    // Mock job
    mockJob = {
      id: 'job-123',
      data: {
        keywordId: 'keyword-456',
        batchId: 'batch-789',
        keyword: 'test keyword',
        searchEngine: 'google' as const,
        userId: 'user-123',
        retryAttempt: 0,
      },
      progress: jest.fn().mockResolvedValue(undefined),
      attemptsMade: 0,
      opts: { attempts: 3 },
    } as any;

    jest.clearAllMocks();
  });

  describe('initialization and shutdown', () => {
    it('should initialize scrapers correctly', async () => {
      await processor.initialize();

      expect(mockGoogleScraper.initialize).toHaveBeenCalledTimes(1);
      expect(mockBingScraper.initialize).toHaveBeenCalledTimes(1);
      expect(mockLogger.info).toHaveBeenCalledWith('KeywordProcessor scrapers (Google & Bing) initialized');
    });

    it('should not re-initialize if already initialized', async () => {
      await processor.initialize();
      await processor.initialize();

      expect(mockGoogleScraper.initialize).toHaveBeenCalledTimes(1);
      expect(mockBingScraper.initialize).toHaveBeenCalledTimes(1);
    });

    it('should shutdown scrapers correctly', async () => {
      await processor.initialize();
      await processor.shutdown();

      expect(mockGoogleScraper.close).toHaveBeenCalledTimes(1);
      expect(mockBingScraper.close).toHaveBeenCalledTimes(1);
      expect(mockLogger.info).toHaveBeenCalledWith('KeywordProcessor scrapers shut down');
    });

    it('should handle shutdown when not initialized', async () => {
      await processor.shutdown();

      expect(mockGoogleScraper.close).not.toHaveBeenCalled();
      expect(mockBingScraper.close).not.toHaveBeenCalled();
    });
  });

  describe('successful processing', () => {
    const mockSearchResult = {
      query: 'test keyword',
      timestamp: new Date(),
      page: 1,
      hasNextPage: false,
      adsCount: 5,
      totalLinksCount: 25,
      htmlCache: '<html>Cached content</html>',
      results: [
        { title: 'Result 1', url: 'https://example1.com', snippet: 'snippet 1', position: 1, type: 'organic' as const },
        { title: 'Ad 1', url: 'https://ad1.com', snippet: 'ad snippet 1', position: 2, type: 'ad' as const }
      ],
      adsLinks: [{ url: 'https://ad1.com', title: 'Ad 1', position: 1, type: 'ad' as const, domain: 'ad1.com' }],
      organicLinks: [{ url: 'https://example1.com', title: 'Result 1', position: 1, type: 'organic' as const, domain: 'example1.com' }],
      otherLinks: []
    };

    beforeEach(() => {
      // Mock successful scraping
      mockGoogleScraper.search.mockResolvedValue(mockSearchResult);
      mockBingScraper.search.mockResolvedValue(mockSearchResult);

      // Mock database operations
      mockDb.returning
        .mockResolvedValueOnce([{ id: 'search-result-123' }]) // search result insert
        .mockResolvedValueOnce([{ // batch stats for updateBatchStatus
          totalKeywords: 10,
          completedCount: 5,
          failedCount: 1,
          pendingCount: 4
        }]);
    });

    it('should process Google search successfully', async () => {
      const result = await processor.process(mockJob);

      expect(mockGoogleScraper.search).toHaveBeenCalledWith({
        query: 'test keyword',
        page: 1,
        resultsPerPage: 10,
      });

      expect(result).toEqual({
        keywordId: 'keyword-456',
        keyword: 'test keyword',
        adsCount: 5,
        totalLinksCount: 25,
        searchResultId: 'search-result-123',
        status: 'completed',
      });

      expect(mockJob.progress).toHaveBeenCalledWith(10);
      expect(mockJob.progress).toHaveBeenCalledWith(25);
      expect(mockJob.progress).toHaveBeenCalledWith(75);
      expect(mockJob.progress).toHaveBeenCalledWith(100);
    });

    it('should process Bing search successfully', async () => {
      mockJob.data.searchEngine = 'bing';

      const result = await processor.process(mockJob);

      expect(mockBingScraper.search).toHaveBeenCalledWith({
        query: 'test keyword',
        page: 1,
        resultsPerPage: 10,
      });
      expect(mockGoogleScraper.search).not.toHaveBeenCalled();

      expect(result.status).toBe('completed');
    });

    it('should store search results in database correctly', async () => {
      await processor.process(mockJob);

      expect(mockDb.insert).toHaveBeenCalledTimes(1);
      expect(mockDb.values).toHaveBeenCalledWith({
        keywordId: 'keyword-456',
        totalAds: 5,
        totalLinks: 25,
        htmlContent: '<html>Cached content</html>',
        metadata: {
          keyword: 'test keyword',
          searchEngine: 'google',
          results: mockSearchResult.results,
        },
        adsLinks: mockSearchResult.adsLinks,
        organicLinks: mockSearchResult.organicLinks,
        otherLinks: [],
        scrapedAt: expect.any(Date),
      });
    });

    it('should update keyword status to completed', async () => {
      await processor.process(mockJob);

      expect(mockDb.update).toHaveBeenCalled();
      expect(mockDb.set).toHaveBeenCalledWith({
        status: 'completed',
        updatedAt: expect.any(Date),
      });
    });

    it('should initialize scrapers if not already initialized', async () => {
      // Create processor without initializing
      const uninitializedProcessor = new KeywordProcessor({}, mockLogger);
      
      await uninitializedProcessor.process(mockJob);

      expect(mockGoogleScraper.initialize).toHaveBeenCalled();
      expect(mockBingScraper.initialize).toHaveBeenCalled();
    });
  });

  describe('error handling and analysis', () => {
    beforeEach(() => {
      mockGoogleScraper.search.mockRejectedValue(new Error('Test error'));
    });

    it('should analyze network errors correctly', async () => {
      const networkError = new Error('Connection timeout occurred');
      mockGoogleScraper.search.mockRejectedValue(networkError);

      await expect(processor.process(mockJob)).rejects.toThrow('Connection timeout occurred');

      expect(mockDb.set).toHaveBeenCalledWith({
        status: 'pending', // Should retry network errors
        retryCount: 0,
        updatedAt: expect.any(Date),
      });
    });

    it('should analyze rate limit errors correctly', async () => {
      const rateLimitError = new Error('Rate limit exceeded - 429 Too Many Requests');
      mockGoogleScraper.search.mockRejectedValue(rateLimitError);

      await expect(processor.process(mockJob)).rejects.toThrow('Rate limit exceeded');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Keyword processing failed: test keyword',
        expect.objectContaining({
          errorType: 'rate_limit',
          isRetryable: true,
        })
      );
    });

    it('should analyze blocking errors correctly', async () => {
      const blockingError = new Error('Access denied - captcha detected');
      mockGoogleScraper.search.mockRejectedValue(blockingError);

      await expect(processor.process(mockJob)).rejects.toThrow('Access denied');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Keyword processing failed: test keyword',
        expect.objectContaining({
          errorType: 'blocked',
          isRetryable: true,
        })
      );
    });

    it('should analyze parsing errors as non-retryable', async () => {
      const parsingError = new Error('Element not found - parsing failed');
      mockGoogleScraper.search.mockRejectedValue(parsingError);

      await expect(processor.process(mockJob)).rejects.toThrow('Element not found');

      expect(mockDb.set).toHaveBeenCalledWith({
        status: 'failed', // Should not retry parsing errors
        retryCount: 0,
        updatedAt: expect.any(Date),
      });
    });

    it('should handle invalid keyword errors as non-retryable', async () => {
      const invalidKeywordError = new Error('Invalid keyword format provided');
      mockGoogleScraper.search.mockRejectedValue(invalidKeywordError);

      await expect(processor.process(mockJob)).rejects.toThrow('Invalid keyword');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Keyword processing failed: test keyword',
        expect.objectContaining({
          errorType: 'invalid_keyword',
          isRetryable: false,
        })
      );
    });

    it('should handle database errors as retryable', async () => {
      const dbError = new Error('Database connection lost');
      mockGoogleScraper.search.mockRejectedValue(dbError);

      await expect(processor.process(mockJob)).rejects.toThrow('Database connection');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Keyword processing failed: test keyword',
        expect.objectContaining({
          errorType: 'database',
          isRetryable: true,
        })
      );
    });

    it('should handle final failure after max attempts', async () => {
      // Simulate final attempt
      mockJob.attemptsMade = 2;
      mockJob.opts.attempts = 3;

      const finalError = new Error('Persistent network error');
      mockGoogleScraper.search.mockRejectedValue(finalError);

      await expect(processor.process(mockJob)).rejects.toThrow('Persistent network error');

      expect(mockDb.set).toHaveBeenCalledWith({
        status: 'failed', // Should fail permanently after max attempts
        retryCount: 2,
        updatedAt: expect.any(Date),
      });
    });

    it('should handle unknown errors conservatively', async () => {
      const unknownError = new Error('Mysterious error occurred');
      mockGoogleScraper.search.mockRejectedValue(unknownError);

      await expect(processor.process(mockJob)).rejects.toThrow('Mysterious error');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Keyword processing failed: test keyword',
        expect.objectContaining({
          errorType: 'unknown',
          isRetryable: true, // Conservative approach
        })
      );
    });
  });

  describe('batch status updates', () => {
    beforeEach(() => {
      // Mock successful scraping
      mockGoogleScraper.search.mockResolvedValue({
        query: 'test keyword',
        timestamp: new Date(),
        page: 1,
        hasNextPage: false,
        adsCount: 3,
        totalLinksCount: 15,
        htmlCache: '<html>content</html>',
        results: [],
        adsLinks: [],
        organicLinks: [],
        otherLinks: []
      });

      mockDb.returning.mockResolvedValueOnce([{ id: 'search-result-123' }]);
    });

    it('should update batch status to processing when partially complete', async () => {
      // Mock batch with some completed keywords
      mockDb.returning.mockResolvedValueOnce([{
        totalKeywords: 10,
        completedCount: 3,
        failedCount: 1,
        pendingCount: 6
      }]);

      await processor.process(mockJob);

      expect(mockDb.set).toHaveBeenCalledWith({
        status: 'processing',
        completedKeywords: 3,
        failedKeywords: 1,
        updatedAt: expect.any(Date),
      });
    });

    it('should update batch status to completed when all keywords done', async () => {
      // Mock batch with all keywords completed
      mockDb.returning.mockResolvedValueOnce([{
        totalKeywords: 10,
        completedCount: 10,
        failedCount: 0,
        pendingCount: 0
      }]);

      await processor.process(mockJob);

      expect(mockDb.set).toHaveBeenCalledWith({
        status: 'completed',
        completedKeywords: 10,
        failedKeywords: 0,
        updatedAt: expect.any(Date),
      });
    });

    it('should update batch status to failed when all keywords failed', async () => {
      // Simulate failure
      mockGoogleScraper.search.mockRejectedValue(new Error('Invalid keyword format'));
      mockJob.attemptsMade = 2; // Final attempt

      // Mock batch with all keywords failed
      mockDb.returning.mockResolvedValueOnce([{
        totalKeywords: 5,
        completedCount: 0,
        failedCount: 5,
        pendingCount: 0
      }]);

      await expect(processor.process(mockJob)).rejects.toThrow();

      expect(mockDb.set).toHaveBeenCalledWith({
        status: 'failed',
        completedKeywords: 0,
        failedKeywords: 5,
        updatedAt: expect.any(Date),
      });
    });

    it('should update batch status to completed when mix of completed and failed', async () => {
      // Mock batch with mixed results but all done
      mockDb.returning.mockResolvedValueOnce([{
        totalKeywords: 10,
        completedCount: 7,
        failedCount: 3,
        pendingCount: 0
      }]);

      await processor.process(mockJob);

      expect(mockDb.set).toHaveBeenCalledWith({
        status: 'completed',
        completedKeywords: 7,
        failedKeywords: 3,
        updatedAt: expect.any(Date),
      });
    });
  });

  describe('partial results storage', () => {
    it('should store partial results when available', async () => {
      // Mock error that includes partial results
      const errorWithPartialResults = new Error('Timeout during scraping');
      (errorWithPartialResults as any).partialResults = {
        adsCount: 2,
        totalLinksCount: 8,
        htmlContent: '<html>partial</html>',
        adsLinks: [{ url: 'https://ad.com', title: 'Ad', type: 'ad' }],
        metadata: { partial: true }
      };

      // Mock the analyzeError method to return partial results
      const originalAnalyzeError = (processor as any).analyzeError;
      (processor as any).analyzeError = jest.fn().mockReturnValue({
        type: 'network',
        isRetryable: true,
        partialResults: {
          adsCount: 2,
          totalLinksCount: 8,
          htmlContent: '<html>partial</html>',
          adsLinks: [{ url: 'https://ad.com', title: 'Ad', type: 'ad' }],
          metadata: { partial: true }
        }
      });

      mockGoogleScraper.search.mockRejectedValue(errorWithPartialResults);

      await expect(processor.process(mockJob)).rejects.toThrow();

      expect(mockDb.values).toHaveBeenCalledWith(
        expect.objectContaining({
          keywordId: 'keyword-456',
          totalAds: 2,
          totalLinks: 8,
          htmlContent: '<html>partial</html>',
          metadata: expect.objectContaining({
            isPartial: true,
            errorType: 'partial_scraping_failure'
          })
        })
      );

      // Restore original method
      (processor as any).analyzeError = originalAnalyzeError;
    });

    it('should not store empty partial results', async () => {
      const originalStorePartialResults = (processor as any).storePartialResults;
      const storePartialResultsSpy = jest.fn();
      (processor as any).storePartialResults = storePartialResultsSpy;

      const originalAnalyzeError = (processor as any).analyzeError;
      (processor as any).analyzeError = jest.fn().mockReturnValue({
        type: 'network',
        isRetryable: true,
        partialResults: null // No partial results
      });

      mockGoogleScraper.search.mockRejectedValue(new Error('Complete failure'));

      await expect(processor.process(mockJob)).rejects.toThrow();

      expect(storePartialResultsSpy).toHaveBeenCalledWith('keyword-456', null);

      // Restore methods
      (processor as any).storePartialResults = originalStorePartialResults;
      (processor as any).analyzeError = originalAnalyzeError;
    });
  });

  describe('error analysis edge cases', () => {
    const testErrorAnalysis = (errorMessage: string, expectedType: string, expectedRetryable: boolean) => {
      const analyzeError = (processor as any).analyzeError.bind(processor);
      const result = analyzeError(new Error(errorMessage));
      expect(result.type).toBe(expectedType);
      expect(result.isRetryable).toBe(expectedRetryable);
      return result;
    };

    it('should correctly categorize various error types', () => {
      testErrorAnalysis('Connection timeout', 'network', true);
      testErrorAnalysis('ECONNREFUSED connection refused', 'network', true);
      testErrorAnalysis('Rate limit exceeded', 'rate_limit', true);
      testErrorAnalysis('HTTP 429 Too Many Requests', 'rate_limit', true);
      testErrorAnalysis('CAPTCHA challenge detected', 'blocked', true);
      testErrorAnalysis('Access denied - 403 Forbidden', 'blocked', true);
      testErrorAnalysis('Browser context closed unexpectedly', 'browser', true);
      testErrorAnalysis('Page was closed', 'browser', true);
      testErrorAnalysis('Element not found with selector', 'parsing', false);
      testErrorAnalysis('Invalid keyword format provided', 'invalid_keyword', false);
      testErrorAnalysis('Database connection error', 'database', true);
      testErrorAnalysis('SQL syntax error occurred', 'database', true);
    });

    it('should provide appropriate delay suggestions', () => {
      const networkResult = testErrorAnalysis('Timeout error', 'network', true);
      expect(networkResult.suggestedDelay).toBe(30000);

      const rateLimitResult = testErrorAnalysis('Rate limit hit', 'rate_limit', true);
      expect(rateLimitResult.suggestedDelay).toBe(300000);

      const blockedResult = testErrorAnalysis('Captcha detected', 'blocked', true);
      expect(blockedResult.suggestedDelay).toBe(600000);

      const browserResult = testErrorAnalysis('Browser crashed', 'browser', true);
      expect(browserResult.suggestedDelay).toBe(60000);

      const dbResult = testErrorAnalysis('Database timeout', 'database', true);
      expect(dbResult.suggestedDelay).toBe(10000);
    });

    it('should handle null or undefined errors gracefully', () => {
      const analyzeError = (processor as any).analyzeError.bind(processor);
      
      const nullResult = analyzeError(null);
      expect(nullResult.type).toBe('unknown');
      expect(nullResult.isRetryable).toBe(true);

      const undefinedResult = analyzeError(undefined);
      expect(undefinedResult.type).toBe('unknown');
      expect(undefinedResult.isRetryable).toBe(true);

      const noMessageResult = analyzeError({});
      expect(noMessageResult.type).toBe('unknown');
      expect(noMessageResult.isRetryable).toBe(true);
    });
  });
});