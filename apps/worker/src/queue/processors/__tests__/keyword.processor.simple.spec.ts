import { Job } from 'bull';
import { KeywordProcessor } from '../keyword.processor';
import { KeywordScrapingJob } from '@search-keywords-scraper/queue';

// Mock all external dependencies
jest.mock('@search-keywords-scraper/scraper', () => ({
  GoogleScraper: jest.fn().mockImplementation(() => ({
    initialize: jest.fn().mockResolvedValue(undefined),
    close: jest.fn().mockResolvedValue(undefined),
    search: jest.fn().mockResolvedValue({
      query: 'test',
      timestamp: new Date(),
      page: 1,
      hasNextPage: false,
      adsCount: 0,
      totalLinksCount: 0,
      htmlCache: '',
      results: [],
      adsLinks: [],
      organicLinks: [],
      otherLinks: []
    })
  })),
  BingScraper: jest.fn().mockImplementation(() => ({
    initialize: jest.fn().mockResolvedValue(undefined),
    close: jest.fn().mockResolvedValue(undefined),
    search: jest.fn().mockResolvedValue({
      query: 'test',
      timestamp: new Date(),
      page: 1,
      hasNextPage: false,
      adsCount: 0,
      totalLinksCount: 0,
      htmlCache: '',
      results: [],
      adsLinks: [],
      organicLinks: [],
      otherLinks: []
    })
  }))
}));

jest.mock('@search-keywords-scraper/database', () => ({
  db: {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    leftJoin: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    groupBy: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    values: jest.fn().mockReturnThis(),
    returning: jest.fn().mockResolvedValue([{ id: 'test-id' }]),
    onConflictDoNothing: jest.fn().mockReturnThis(),
  },
  keywords: { id: 'keywords-table' },
  searchResults: { id: 'search-results-table' },
  keywordBatches: { id: 'batches-table' },
}));

jest.mock('drizzle-orm', () => ({
  eq: jest.fn(() => 'eq-condition'),
  and: jest.fn(() => 'and-condition'),
  count: jest.fn(() => 'count-function'),
  sql: jest.fn(() => 'sql-template'),
}));

describe('KeywordProcessor - Simple Tests', () => {
  let processor: KeywordProcessor;
  let mockLogger: any;
  let mockJob: jest.Mocked<Job<KeywordScrapingJob>>;

  beforeEach(() => {
    // Mock logger
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    };

    const scraperConfig = {
      browser: { headless: true },
      scraping: { maxRetries: 3, retryDelay: 1000, requestsPerHour: 100 }
    };

    processor = new KeywordProcessor(scraperConfig, mockLogger);

    // Mock job
    mockJob = {
      id: 'job-123',
      data: {
        keywordId: 'keyword-456',
        batchId: 'batch-789',
        keyword: 'test keyword',
        searchEngine: 'google' as const,
        userId: 'user-123',
        retryAttempt: 0,
      },
      progress: jest.fn().mockResolvedValue(undefined),
      attemptsMade: 0,
      opts: { attempts: 3 },
    } as any;

    jest.clearAllMocks();
  });

  describe('Basic functionality', () => {
    it('should be instantiated correctly', () => {
      expect(processor).toBeInstanceOf(KeywordProcessor);
    });

    it('should have access to error analysis for testing', () => {
      const analyzeError = (processor as any).analyzeError.bind(processor);
      expect(typeof analyzeError).toBe('function');
    });
  });

  describe('Error analysis', () => {
    const testErrorAnalysis = (errorMessage: string, expectedType: string, expectedRetryable: boolean) => {
      const analyzeError = (processor as any).analyzeError.bind(processor);
      const result = analyzeError(new Error(errorMessage));
      expect(result.type).toBe(expectedType);
      expect(result.isRetryable).toBe(expectedRetryable);
      return result;
    };

    it('should correctly categorize network errors', () => {
      const result = testErrorAnalysis('Connection timeout', 'network', true);
      expect(result.suggestedDelay).toBe(30000);
    });

    it('should correctly categorize rate limit errors', () => {
      const result = testErrorAnalysis('Rate limit exceeded', 'rate_limit', true);
      expect(result.suggestedDelay).toBe(300000);
    });

    it('should correctly categorize blocking errors', () => {
      const result = testErrorAnalysis('CAPTCHA challenge detected', 'blocked', true);
      expect(result.suggestedDelay).toBe(600000);
    });

    it('should correctly categorize browser errors', () => {
      const result = testErrorAnalysis('Browser context closed', 'browser', true);
      expect(result.suggestedDelay).toBe(60000);
    });

    it('should correctly categorize parsing errors as non-retryable', () => {
      testErrorAnalysis('Element not found with selector', 'parsing', false);
    });

    it('should correctly categorize invalid keyword errors as non-retryable', () => {
      testErrorAnalysis('Invalid keyword format provided', 'invalid_keyword', false);
    });

    it('should correctly categorize database errors', () => {
      const result = testErrorAnalysis('Database connection error', 'database', true);
      expect(result.suggestedDelay).toBe(10000);
    });

    it('should handle various network error patterns', () => {
      testErrorAnalysis('ECONNREFUSED connection refused', 'network', true);
      testErrorAnalysis('ENOTFOUND hostname not found', 'network', true);
      testErrorAnalysis('Network error occurred', 'network', true);
    });

    it('should handle various rate limit patterns', () => {
      testErrorAnalysis('HTTP 429 Too Many Requests', 'rate_limit', true);
      testErrorAnalysis('Too many requests per minute', 'rate_limit', true);
    });

    it('should handle various blocking patterns', () => {
      testErrorAnalysis('Access denied - 403 Forbidden', 'blocked', true);
      testErrorAnalysis('Request blocked by security', 'blocked', true);
    });

    it('should handle browser-specific errors', () => {
      testErrorAnalysis('browser context closed', 'browser', true);
      testErrorAnalysis('page closed unexpectedly', 'browser', true);
    });

    it('should handle database-specific errors', () => {
      testErrorAnalysis('SQL syntax error occurred', 'database', true);
      testErrorAnalysis('Connection to database failed', 'database', true);
    });

    it('should handle unknown errors conservatively', () => {
      const result = testErrorAnalysis('Mysterious error occurred', 'unknown', true);
      expect(result.suggestedDelay).toBe(120000);
    });

    it('should handle null or undefined errors gracefully', () => {
      const analyzeError = (processor as any).analyzeError.bind(processor);
      
      const nullResult = analyzeError(null);
      expect(nullResult.type).toBe('unknown');
      expect(nullResult.isRetryable).toBe(true);

      const undefinedResult = analyzeError(undefined);
      expect(undefinedResult.type).toBe('unknown');
      expect(undefinedResult.isRetryable).toBe(true);

      const noMessageResult = analyzeError({});
      expect(noMessageResult.type).toBe('unknown');
      expect(noMessageResult.isRetryable).toBe(true);
    });
  });

  describe('Initialization and shutdown', () => {
    it('should initialize and shutdown properly', async () => {
      await processor.initialize();
      expect(mockLogger.info).toHaveBeenCalledWith('KeywordProcessor scrapers (Google & Bing) initialized');
      
      await processor.shutdown();
      expect(mockLogger.info).toHaveBeenCalledWith('KeywordProcessor scrapers shut down');
    });

    it('should handle multiple initialization calls', async () => {
      await processor.initialize();
      await processor.initialize(); // Should not re-initialize
      
      // Should only log once
      expect(mockLogger.info).toHaveBeenCalledTimes(1);
    });

    it('should handle shutdown when not initialized', async () => {
      await processor.shutdown();
      // Should not crash or log errors
    });
  });

  describe('Processing workflow', () => {
    it('should update job progress during processing', async () => {
      try {
        await processor.process(mockJob);
      } catch (error) {
        // Expected to potentially fail due to mocking, but progress should still be called
      }

      expect(mockJob.progress).toHaveBeenCalledWith(10);
      expect(mockJob.progress).toHaveBeenCalledWith(25);
    });

    it('should use Google scraper for google search engine', () => {
      expect(mockJob.data.searchEngine).toBe('google');
      // This verifies the job data is set up for Google
    });

    it('should use Bing scraper for bing search engine', () => {
      mockJob.data.searchEngine = 'bing';
      expect(mockJob.data.searchEngine).toBe('bing');
      // This verifies we can modify the search engine
    });

    it('should contain proper job data structure', () => {
      expect(mockJob.data).toMatchObject({
        keywordId: expect.any(String),
        batchId: expect.any(String),
        keyword: expect.any(String),
        searchEngine: expect.any(String),
        userId: expect.any(String),
        retryAttempt: expect.any(Number),
      });
    });
  });

  describe('Partial results storage', () => {
    it('should handle partial results storage', async () => {
      const storePartialResults = (processor as any).storePartialResults.bind(processor);
      
      const partialData = {
        adsCount: 2,
        totalLinksCount: 8,
        htmlContent: '<html>partial</html>',
        adsLinks: [{ url: 'https://ad.com', title: 'Ad', type: 'ad' }],
        metadata: { partial: true }
      };

      // Should not throw errors
      await expect(storePartialResults('keyword-123', partialData)).resolves.not.toThrow();
    });

    it('should skip storage for empty partial results', async () => {
      const storePartialResults = (processor as any).storePartialResults.bind(processor);
      
      // Should handle empty data gracefully
      await expect(storePartialResults('keyword-123', null)).resolves.not.toThrow();
      await expect(storePartialResults('keyword-123', {})).resolves.not.toThrow();
    });
  });

  describe('Batch status logic', () => {
    const testBatchStatusDetermination = (totalKeywords: number, completedCount: number, failedCount: number, expectedStatus: string) => {
      // This tests the logic that would be used in updateBatchStatus
      let batchStatus: 'pending' | 'processing' | 'completed' | 'failed' = 'pending';
      
      if (completedCount === totalKeywords) {
        batchStatus = 'completed';
      } else if (failedCount === totalKeywords) {
        batchStatus = 'failed';
      } else if (completedCount + failedCount === totalKeywords) {
        batchStatus = 'completed';
      } else if (completedCount > 0 || failedCount > 0) {
        batchStatus = 'processing';
      }

      expect(batchStatus).toBe(expectedStatus);
    };

    it('should determine correct batch status for various scenarios', () => {
      testBatchStatusDetermination(10, 10, 0, 'completed');  // All completed
      testBatchStatusDetermination(10, 0, 10, 'failed');     // All failed
      testBatchStatusDetermination(10, 7, 3, 'completed');   // Mixed but all done
      testBatchStatusDetermination(10, 5, 0, 'processing');  // Partially completed
      testBatchStatusDetermination(10, 3, 2, 'processing');  // Mixed in progress
      testBatchStatusDetermination(10, 0, 0, 'pending');     // Not started
    });
  });

  describe('Retry logic', () => {
    it('should determine retry status based on attempts', () => {
      const maxAttempts = 3;
      
      // Should retry on first attempt
      const shouldRetry1 = true && (0 < maxAttempts - 1);
      expect(shouldRetry1).toBe(true);
      
      // Should retry on second attempt
      const shouldRetry2 = true && (1 < maxAttempts - 1);
      expect(shouldRetry2).toBe(true);
      
      // Should not retry on final attempt
      const shouldRetry3 = true && (2 < maxAttempts - 1);
      expect(shouldRetry3).toBe(false);
    });

    it('should not retry non-retryable errors', () => {
      const shouldRetry = false && (0 < 3 - 1);
      expect(shouldRetry).toBe(false);
    });
  });
});