import Bull from 'bull';
import { createLogger } from 'winston';
import { KeywordScrapingJob, QUEUE_NAMES } from '@search-keywords-scraper/queue';
import { GoogleScraper } from '@search-keywords-scraper/scraper';
import { KeywordProcessor } from './processors/keyword.processor';

export class QueueConsumer {
  private keywordQueue!: Bull.Queue<KeywordScrapingJob>;
  private keywordProcessor: KeywordProcessor;
  private logger: any;

  constructor(
    private scraperConfig: any,
    private workerConfig: any
  ) {
    this.logger = createLogger({/* winston config */});
    this.keywordProcessor = new KeywordProcessor(scraperConfig, this.logger);
    this.setupQueues();
  }

  private setupQueues() {
    const redisConfig = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
    };

    this.keywordQueue = new Bull(QUEUE_NAMES.SCRAPER_KEYWORD, {
      redis: redisConfig,
      defaultJobOptions: {
        attempts: 3,
        backoff: { type: 'exponential', delay: 60000 },
      },
    });

    // Set up job processors
    this.keywordQueue.process(
      'scrape-keyword',
      this.workerConfig.concurrency,
      this.processKeywordJob.bind(this)
    );

    // Set up event handlers
    this.setupEventHandlers();
  }

  private async processKeywordJob(job: Bull.Job<KeywordScrapingJob>) {
    return this.keywordProcessor.process(job);
  }

  private setupEventHandlers() {
    this.keywordQueue.on('completed', (job, result) => {
      this.logger.info(`Job completed: ${job.id}`, { result });
    });

    this.keywordQueue.on('failed', (job, error) => {
      this.logger.error(`Job failed: ${job.id}`, { error: error.message });
    });

    this.keywordQueue.on('stalled', (job) => {
      this.logger.warn(`Job stalled: ${job.id}`);
    });
  }

  async start() {
    this.logger.info('Queue consumer starting...', {
      queues: [QUEUE_NAMES.SCRAPER_KEYWORD],
      concurrency: this.workerConfig.concurrency,
    });

    // Wait for Redis connection
    await this.keywordQueue.isReady();
    
    // Initialize the keyword processor
    await this.keywordProcessor.initialize();
    
    this.logger.info('Connected to Redis and ready to process jobs');
  }

  async stop() {
    this.logger.info('Shutting down queue consumer...');
    
    // Shutdown processor first
    await this.keywordProcessor.shutdown();
    
    // Then close the queue
    await this.keywordQueue.close();
    
    this.logger.info('Queue consumer stopped');
  }
}