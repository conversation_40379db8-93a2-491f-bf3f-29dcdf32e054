import { createLogger, format, transports } from 'winston';
import { scraperConfig, workerConfig } from './config';
import { QueueConsumer } from './queue/queue-consumer';

const logger = createLogger({
  level: workerConfig.logLevel,
  format: format.combine(
    format.timestamp(),
    format.errors({ stack: true }),
    format.json()
  ),
  defaultMeta: { service: 'worker' },
  transports: [
    new transports.File({ filename: 'logs/worker-error.log', level: 'error' }),
    new transports.File({ filename: 'logs/worker-combined.log' }),
    new transports.Console({
      format: format.combine(
        format.colorize(),
        format.simple()
      )
    })
  ],
});

let queueConsumer: QueueConsumer;

async function main() {
  logger.info('Worker starting...', {
    mode: workerConfig.testMode ? 'TEST' : 'PRODUCTION',
    config: {
      headless: scraperConfig.browser.headless,
      requestsPerHour: scraperConfig.scraping.requestsPerHour,
      concurrency: workerConfig.concurrency
    }
  });

  try {
    queueConsumer = new QueueConsumer(scraperConfig, workerConfig);
    await queueConsumer.start();

    logger.info('Worker started successfully and waiting for jobs...');

    // Keep the process running
    process.on('SIGTERM', gracefulShutdown);
    process.on('SIGINT', gracefulShutdown);

  } catch (error) {
    logger.error('Failed to start worker:', error);
    process.exit(1);
  }
}

async function gracefulShutdown() {
  logger.info('Received shutdown signal, shutting down gracefully...');
  
  if (queueConsumer) {
    await queueConsumer.stop();
  }
  
  logger.info('Worker shutdown complete');
  process.exit(0);
}

main().catch((error) => {
  logger.error('Fatal error:', error);
  process.exit(1);
});