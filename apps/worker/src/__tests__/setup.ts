// Test setup file to configure global mocks and DOM types

// Define minimal DOM types needed for tests  
declare global {
  interface HTMLCanvasElement {
    toDataURL(...args: any[]): string;
  }
  
  interface WebGLRenderingContext {
    getParameter(parameter: any): any;
  }
  
  var HTMLCanvasElement: {
    prototype: HTMLCanvasElement;
  };
  
  var WebGLRenderingContext: {
    prototype: WebGLRenderingContext;
  };
  
  var navigator: {
    webdriver?: boolean;
    userAgent?: string;
  };
  
  var window: {
    scrollBy(x: number, y: number): void;
  };
}

// Mock global objects to prevent DOM-related errors in node environment
global.HTMLCanvasElement = {
  prototype: {
    toDataURL: jest.fn()
  } as any
};

global.WebGLRenderingContext = {
  prototype: {
    getParameter: jest.fn()
  } as any
};

global.navigator = {
  webdriver: false,
  userAgent: 'test-agent'
};

global.window = {
  scrollBy: jest.fn()
};

export {};