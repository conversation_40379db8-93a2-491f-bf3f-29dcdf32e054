{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "CommonJS", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "outDir": "./dist", "rootDir": "./src", "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "paths": {"@search-keywords-scraper/types": ["../../packages/types/src"], "@search-keywords-scraper/database": ["../../packages/database/src"], "@search-keywords-scraper/config": ["../../packages/config/src"], "@search-keywords-scraper/utils": ["../../packages/utils/src"]}}, "references": [{"path": "../../packages/types"}, {"path": "../../packages/database"}, {"path": "../../packages/config"}, {"path": "../../packages/utils"}], "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.test.ts"]}