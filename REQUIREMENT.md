# Web - Technical Challenge

# Overview

- Create a web application that will extract large amounts of data from the Google or Bing search results page.
    
    💡 You need to find a way to work around the limitations of mass-searching keywords, as Google/Bing prevents it. Be creative.
    
    You can decide to work with only 1 service, either Google or Bing search engine, for this assignment.
    
    ⚠️ Using a third-party API to acquire search data is not valid. One of the goals of the code challenge is to make applicants work on data scraping.

- Store the search result and display it to users as soon as possible after each keyword is processed.
- Users must be authenticated to use the application.

# Application Requirements

1. Authenticated users can upload a CSV file of keywords. This uploaded file can be in any size from 1 to 100 keywords.
2. The uploaded file contains keywords. Each of these keywords will be used to search on [https://www.google.com](https://www.google.com/) (or [https://www.bing.com](https://www.bing.com)) and will start running the search process as soon as they are uploaded.
3. For each search result/keyword result page, store the following information on the first results page:
    - The total number of Google/Bing Ads advertisers on the page.
    - The total number of links (all of them) on the page.
    - HTML code of the page/cache of the page.
4. Allow users to view the list of their uploaded keywords. For each keyword, users can also view the search result information stored in the database.
---

All features must have a Web user interface. 
If you feel like going the extra mile, you can also add an API to the application (optional). Refer to the below sections for a detailed description.

## Web UI

The following screens must be implemented:

- Sign in.
- Sign up.
- Upload a keyword file.
- View the list of keywords.
- View the search result information for each keyword.
- Search across all reports.

## API (optional)

The following endpoints must be implemented:

- Sign in.
- Get the list of keywords.
- Upload a keyword file.
- Get the search result information for each keyword.

Users would need to sign up via the Web UI (in-browser) to use the API.

## Technical Requirements

- Use Git during the development process. **Make regular commits and merge code using pull requests.**
    
    Push to a **private repository** on GitHub, and invite the reviewers with View access when done.
    
- Use a Web Framework of your choice.
- Use PostgreSQL.
- For the interface, front-end frameworks such as Bootstrap, Tailwind, or Foundation can be used. 
Use SASS as the CSS preprocessor.
    
    Extra points are provided for the neatness and user-friendliness of the front end.
    
- Write tests using your framework of choice.
- Optional: deploy the application to a cloud provider, e.g., Heroku, AWS, Google Cloud, or Digital Ocean.