# Application Environment
NODE_ENV=development

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/search_keywords_scraper

# Redis Configuration (for job queue)
REDIS_URL=redis://localhost:6379

# Authentication Secrets
JWT_SECRET=your-super-secure-jwt-secret-key-here-minimum-32-characters

# Supabase Configuration (if using Supabase)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Upstash Redis Configuration (if using Upstash)
UPSTASH_REDIS_REST_URL=https://your-redis.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-upstash-redis-token

# API Configuration
API_PORT=3001
WEB_PORT=3000

# Worker Configuration
WORKER_CONCURRENCY=5
SCRAPING_TIMEOUT=30000
MAX_RETRY_ATTEMPTS=3

# Proxy Configuration (optional)
PROXY_LIST_URL=https://your-proxy-provider.com/api/proxies
PROXY_USERNAME=your-proxy-username
PROXY_PASSWORD=your-proxy-password

# Monitoring & Logging
LOG_LEVEL=info
SENTRY_DSN=https://<EMAIL>/project-id

# Development Only
DEBUG=true
ENABLE_LOGGING=true