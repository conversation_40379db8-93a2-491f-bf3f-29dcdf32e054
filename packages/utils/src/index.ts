import { SearchEngine } from '@search-keywords-scraper/types';

// Utility functions for string operations
export const stringUtils = {
  slugify: (text: string): string => {
    return text
      .toString()
      .toLowerCase()
      .trim()
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]+/g, '')
      .replace(/\-\-+/g, '-')
      .replace(/^-+/, '')
      .replace(/-+$/, '');
  },

  truncate: (text: string, length: number): string => {
    if (text.length <= length) return text;
    return text.substring(0, length) + '...';
  },

  sanitizeKeyword: (keyword: string): string => {
    return keyword.trim().replace(/\s+/g, ' ');
  },
};

// Date utility functions
export const dateUtils = {
  formatDateForDisplay: (date: Date): string => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  },

  isWithinLast24Hours: (date: Date): boolean => {
    const now = new Date();
    const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    return date > twentyFourHoursAgo;
  },
};

// Array utility functions
export const arrayUtils = {
  deduplicate: <T>(array: T[]): T[] => {
    return [...new Set(array)];
  },

  chunk: <T>(array: T[], size: number): T[][] => {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  },

  shuffle: <T>(array: T[]): T[] => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      const temp = shuffled[i];
      shuffled[i] = shuffled[j]!;
      shuffled[j] = temp!;
    }
    return shuffled;
  },
};

// Search engine utility functions
export const searchEngineUtils = {
  getSearchUrl: (searchEngine: SearchEngine, query: string): string => {
    const encodedQuery = encodeURIComponent(query);
    switch (searchEngine) {
      case SearchEngine.GOOGLE:
        return `https://www.google.com/search?q=${encodedQuery}`;
      case SearchEngine.BING:
        return `https://www.bing.com/search?q=${encodedQuery}`;
      default:
        throw new Error(`Unsupported search engine: ${searchEngine}`);
    }
  },

  extractDomain: (url: string): string => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch {
      return '';
    }
  },
};

// Validation utility functions
export const validationUtils = {
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  isValidKeyword: (keyword: string): boolean => {
    const trimmed = keyword.trim();
    return trimmed.length >= 1 && trimmed.length <= 500;
  },

  isValidBatchSize: (keywords: string[]): boolean => {
    return keywords.length >= 1 && keywords.length <= 100;
  },
};

// Delay utility for rate limiting
export const delayUtils = {
  randomDelay: (min: number, max: number): Promise<void> => {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    return new Promise(resolve => setTimeout(resolve, delay));
  },

  exponentialBackoff: (attempt: number, baseDelay = 1000): number => {
    return Math.min(baseDelay * Math.pow(2, attempt), 30000); // Max 30 seconds
  },
};