export interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  position: number;
  type: 'organic' | 'ad' | 'featured_snippet' | 'people_also_ask';
  metadata?: {
    domain: string;
    breadcrumbs?: string[];
    sitelinks?: string[];
  };
}

export interface LinkDetail {
  url: string;
  title: string;
  position: number;
  type: 'ad' | 'organic' | 'navigation' | 'footer' | 'other';
  domain: string;
  anchorText?: string;
}

export interface SearchResponse {
  query: string;
  totalResults?: number;
  results: SearchResult[];
  relatedQueries?: string[];
  timestamp: Date;
  page: number;
  hasNextPage: boolean;
  // Core metrics required by the project
  adsCount: number;
  totalLinksCount: number;
  htmlCache: string;
  // Enhanced link arrays for detailed analysis
  adsLinks: LinkDetail[];
  organicLinks: LinkDetail[];
  otherLinks: LinkDetail[];
}

export interface SearchOptions {
  query: string;
  page?: number;
  resultsPerPage?: number;
  language?: string;
  country?: string;
  safeSearch?: 'on' | 'off' | 'moderate';
}

export interface SearchParams {
  q: string;
  start?: number;
  num?: number;
  hl?: string;
  gl?: string;
  safe?: string;
}

export interface ParserSelectors {
  resultContainer: string[];
  title: string[];
  url: string[];
  snippet: string[];
  adIndicator: string[];
  nextPageButton: string[];
}

// Browser configuration types
export interface BrowserConfig {
  headless: boolean;
  slowMo?: number;
  launchOptions?: Record<string, unknown>;
}

export interface BrowserContext {
  id: string;
  userAgent: string;
  viewport: {
    width: number;
    height: number;
  };
  proxy?: ProxyInfo | LegacyProxyConfig;
  createdAt: Date;
  lastUsed: Date;
}

// Proxy configuration types
export interface ProxyInfo {
  id: string;
  host: string;
  port: number;
  protocol: 'http' | 'https' | 'socks4' | 'socks5';
  credentials?: {
    username: string;
    password: string;
  };
  provider: string;
  type: string;
  location?: string;
  health: ProxyHealthStatus;
  usage: ProxyUsageStats;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface LegacyProxyConfig {
  host: string;
  port: number;
  username?: string;
  password?: string;
}

export interface ProxyHealthStatus {
  isHealthy: boolean;
  lastChecked: Date;
  responseTime?: number;
  error?: string;
  consecutiveFailures: number;
  lastSuccessful?: Date;
  ipAddress?: string;
  location?: {
    country?: string;
  };
}

export interface ProxyUsageStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  avgResponseTime: number;
  lastUsed: Date;
  bytesTransferred: number;
}

export interface ProxyTestResult {
  success: boolean;
  responseTime?: number;
  error?: string;
  ipAddress?: string;
  location?: string;
}

export interface ProxyProvider {
  name: string;
  initialize(): Promise<void>;
  getProxies(): Promise<ProxyConfig[]>;
  testProxy(proxy: ProxyInfo): Promise<ProxyTestResult>;
}

export interface ProxyConfig {
  id: string;
  host: string;
  port: number;
  protocol: 'http' | 'https' | 'socks4' | 'socks5';
  credentials?: {
    username: string;
    password: string;
  };
  provider: string;
  type: string;
  location?: string;
}

export interface ProxyManagerConfig {
  providers: Array<{
    name: string;
    type: string;
    config: Record<string, unknown>;
  }>;
  rotationStrategy: 'round-robin' | 'random' | 'least-used' | 'healthiest';
  healthCheckInterval: number; // minutes
  failureThreshold: number;
  maxConcurrentHealthChecks: number;
}

export interface ProxySelectionCriteria {
  provider?: string;
  type?: string;
  location?: string;
  maxResponseTime?: number;
  excludeUnhealthy?: boolean;
}

// Scraper configuration
export interface ScraperConfig {
  browser: BrowserConfig;
  scraping: {
    requestsPerHour: number;
    maxRetries: number;
    retryDelay: number;
    delayBetweenRequests: [number, number]; // [min, max] in milliseconds
  };
}

// Event types for proxy manager
export type ProxyManagerEvents = {
  'proxy-rotation': { newProxy: ProxyInfo };
  'proxy-failure': { proxy: ProxyInfo; error: string };
  'proxy-recovery': { proxy: ProxyInfo };
  'proxy-health-check': { proxy: ProxyInfo; health: ProxyHealthStatus };
  'provider-error': { provider: string; error: string };
};

export type ProxyManagerEventType = keyof ProxyManagerEvents;
export type ProxyManagerEventHandler<T extends ProxyManagerEventType> = (data: ProxyManagerEvents[T]) => void;