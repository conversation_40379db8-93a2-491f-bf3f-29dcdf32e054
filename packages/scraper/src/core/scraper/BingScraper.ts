import type { <PERSON> } from 'patchright';
import { BrowserManager } from '../browser/BrowserManager';
import { BingResultParser } from './BingResultParser';
import type { 
  SearchOptions, 
  SearchResponse, 
  SearchParams, 
  LegacyProxyConfig, 
  ProxyInfo, 
  ScraperConfig 
} from '../../types';
import { retry, CircuitBreaker } from '../../utils/retry';
import { RateLimiter, randomDelay } from '../../utils/delay';
import { logger } from '../../utils/logger';

export class BingScraper {
  private readonly browserManager: BrowserManager;
  private readonly parser: BingResultParser;
  private readonly rateLimiter: RateLimiter;
  private readonly circuitBreaker: CircuitBreaker;
  private readonly config: ScraperConfig;

  constructor(config: ScraperConfig) {
    this.config = config;
    this.browserManager = new BrowserManager(config.browser);
    this.parser = new BingResultParser();
    
    // Rate limiting: convert requests per hour to requests per hour window
    this.rateLimiter = new RateLimiter(
      config.scraping.requestsPerHour,
      60 * 60 * 1000 // 1 hour in milliseconds
    );
    
    this.circuitBreaker = new CircuitBreaker(5, 60000); // 5 failures, 1 minute recovery
  }

  async initialize(): Promise<void> {
    await this.browserManager.launch();
    logger.info('BingScraper initialized successfully');
  }

  async search(options: SearchOptions, proxy?: ProxyInfo | LegacyProxyConfig): Promise<SearchResponse> {
    await this.rateLimiter.waitIfNeeded();
    
    return this.circuitBreaker.execute(async () => {
      return this.performSearch(options, proxy);
    });
  }

  private async performSearch(options: SearchOptions, proxy?: ProxyInfo | LegacyProxyConfig): Promise<SearchResponse> {
    const contextId = await this.browserManager.createContext(proxy);
    let page: Page | null = null;

    try {
      page = await this.browserManager.getPage(contextId);
      
      const searchUrl = this.buildSearchUrl(options);
      logger.info(`Bing searching for: "${options.query}" at ${searchUrl}`);

      // Navigate with retry logic
      await retry(
        async () => {
          if (!page) throw new Error('Page is null');
          await page.goto(searchUrl, { 
            waitUntil: 'networkidle',
            timeout: 30000 
          });
        },
        {
          maxAttempts: this.config.scraping.maxRetries,
          baseDelay: this.config.scraping.retryDelay,
          maxDelay: 30000,
          retryCondition: (error) => {
            // Retry on network errors but not on access denied
            return !error.message.includes('403') && !error.message.includes('blocked');
          }
        }
      );

      // Check for captcha or blocking
      const isBlocked = await this.detectBlocking(page);
      if (isBlocked) {
        logger.warn('Bing blocking detected');
        throw new Error('Bing access blocked or captcha detected');
      }

      // Wait for results to load
      await this.waitForResults(page);

      // Simulate human behavior
      await this.browserManager.simulateHumanBehavior(page);

      // Parse results
      const results = await this.parser.parse(page, options.query);
      
      // Add delay between requests
      const [minDelay, maxDelay] = this.config.scraping.delayBetweenRequests;
      await randomDelay(minDelay, maxDelay);

      logger.info(`Successfully scraped ${results.results.length} Bing results for query: "${options.query}"`);
      return results;

    } catch (error) {
      logger.error(`Failed to scrape Bing query "${options.query}":`, error);
      throw error;
    } finally {
      if (page) {
        await page.close();
      }
      await this.browserManager.closeContext(contextId);
    }
  }

  private buildSearchUrl(options: SearchOptions): string {
    const params: Record<string, string | number> = {
      q: options.query
    };

    if (options.page && options.page > 1) {
      // Bing uses 'first' parameter for pagination (0-indexed)
      params.first = (options.page - 1) * (options.resultsPerPage || 10);
    }

    if (options.resultsPerPage) {
      params.count = Math.min(options.resultsPerPage, 50); // Bing allows up to 50
    }

    if (options.language) {
      params.setlang = options.language;
    }

    if (options.country) {
      params.cc = options.country;
    }

    if (options.safeSearch) {
      params.safesearch = options.safeSearch === 'on' ? 'strict' : 'off';
    }

    const searchParams = new URLSearchParams();
    for (const [key, value] of Object.entries(params)) {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    }

    return `https://www.bing.com/search?${searchParams.toString()}`;
  }

  private async detectBlocking(page: Page): Promise<boolean> {
    try {
      // Check for common Bing blocking indicators
      const blockingSelectors = [
        '#captcha',
        '.captcha',
        '[data-captcha]',
        '.errorpage',
        '#errorPage'
      ];

      for (const selector of blockingSelectors) {
        const element = await page.$(selector);
        if (element) {
          return true;
        }
      }

      // Check for access denied messages
      const bodyText = await page.textContent('body');
      if (bodyText?.includes('automated queries') || 
          bodyText?.includes('unusual activity') ||
          bodyText?.includes('verify that you are human') ||
          bodyText?.includes('blocked')) {
        return true;
      }

      return false;
    } catch (error) {
      logger.warn('Error detecting Bing blocking:', error);
      return false;
    }
  }

  private async waitForResults(page: Page): Promise<void> {
    try {
      // Wait for either search results or error indicators
      await Promise.race([
        page.waitForSelector('#b_results .b_algo, .b_ad, .b_pag', { timeout: 10000 }),
        page.waitForSelector('#captcha, .errorpage', { timeout: 10000 })
      ]);

      // Additional wait for dynamic content
      await randomDelay(1000, 3000);
    } catch (error) {
      logger.warn('Timeout waiting for Bing results, proceeding anyway');
    }
  }

  async searchMultiplePages(
    options: SearchOptions, 
    maxPages = 3,
    proxy?: ProxyInfo | LegacyProxyConfig
  ): Promise<SearchResponse[]> {
    const results: SearchResponse[] = [];
    
    for (let page = 1; page <= maxPages; page++) {
      try {
        const pageOptions = { ...options, page };
        const result = await this.search(pageOptions, proxy);
        results.push(result);
        
        if (!result.hasNextPage) {
          logger.info(`No more Bing pages available after page ${page}`);
          break;
        }
        
        // Longer delay between pages
        const [minDelay, maxDelay] = this.config.scraping.delayBetweenRequests;
        await randomDelay(minDelay * 2, maxDelay * 2);
        
      } catch (error) {
        logger.error(`Failed to scrape Bing page ${page}:`, error);
        break;
      }
    }
    
    return results;
  }

  async close(): Promise<void> {
    await this.browserManager.close();
    logger.info('BingScraper closed');
  }

  getStats(): {
    contextCount: number;
    circuitBreakerState: string;
  } {
    return {
      contextCount: this.browserManager.getContextCount(),
      circuitBreakerState: this.circuitBreaker.getState()
    };
  }
}