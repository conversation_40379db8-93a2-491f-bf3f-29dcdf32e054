import type { Page, Locator } from 'patchright';
import type { SearchResult, SearchResponse, LinkDetail } from '../../types';
import { logger } from '../../utils/logger';

interface BingParserSelectors {
  resultContainer: string[];
  title: string[];
  url: string[];
  snippet: string[];
  adIndicator: string[];
  nextPageButton: string[];
}

export class BingResultParser {
  private readonly selectors: BingParserSelectors = {
    resultContainer: [
      '.b_algo',
      '.b_ad',
      '.b_pag li',
      '.b_ansResult',
      '.b_adLastChild'
    ],
    title: [
      'h2 a',
      '.b_adlabel + * a',
      'h3 a',
      'h2',
      'h3'
    ],
    url: [
      'h2 a',
      '.b_adurl a',
      '.b_attribution a',
      'cite',
      'a[href]'
    ],
    snippet: [
      '.b_caption p',
      '.b_snippetBigText',
      '.b_adlabel + * .b_caption',
      '.b_snippet',
      '.b_lineclamp2',
      '.b_mText'
    ],
    adIndicator: [
      '.b_ad',
      '.b_adlabel',
      '.b_adLastChild',
      '.sb_adta',
      '.b_adurl',
      '.b_adslug'
    ],
    nextPageButton: [
      '.sb_pagN',
      'a[aria-label="Next page"]',
      '.b_pag .b_widePag + a',
      '.sw_next'
    ]
  };

  async parse(page: Page, query: string): Promise<SearchResponse> {
    try {
      const results: SearchResult[] = [];
      const timestamp = new Date();

      // Get result containers
      const containers = await this.getElements(page, this.selectors.resultContainer);
      
      logger.info(`Found ${containers.length} Bing result containers`);

      let position = 1;
      for (const container of containers) {
        try {
          const result = await this.parseResultContainer(container, position);
          if (result) {
            results.push(result);
            position++;
          }
        } catch (error) {
          logger.warn(`Failed to parse Bing result container ${position}:`, error);
        }
      }

      // Check for next page
      const hasNextPage = await this.hasNextPage(page);

      // Get total results (if available)
      const totalResults = await this.getTotalResults(page);

      // Get related queries
      const relatedQueries = await this.getRelatedQueries(page);

      // Count total ads on the page (Bing-specific)
      const adsCount = await this.countAdsOnPage(page);

      // Count total links on the page
      const totalLinksCount = await this.countLinksOnPage(page);

      // Cache HTML content of the page
      const htmlCache = await this.getPageHtmlCache(page);

      // Extract detailed link arrays
      const { adsLinks, organicLinks, otherLinks } = await this.extractDetailedLinks(page, results);

      return {
        query,
        totalResults: totalResults || undefined,
        results,
        relatedQueries: relatedQueries.length > 0 ? relatedQueries : undefined,
        timestamp,
        page: this.getCurrentPage(page) || 1,
        hasNextPage,
        // Core metrics required by the project
        adsCount,
        totalLinksCount,
        htmlCache,
        // Enhanced link arrays for detailed analysis
        adsLinks,
        organicLinks,
        otherLinks
      };

    } catch (error) {
      logger.error('Failed to parse Bing search results:', error);
      throw error;
    }
  }

  private async parseResultContainer(container: Locator, position: number): Promise<SearchResult | null> {
    try {
      // Check if this is an ad
      const isAd = await this.isAdvertisement(container);

      // Extract title
      const title = await this.extractText(container, this.selectors.title);
      if (!title) {
        return null; // Skip if no title found
      }

      // Extract URL
      const url = await this.extractUrl(container, this.selectors.url);
      if (!url) {
        return null; // Skip if no URL found
      }

      // Extract snippet
      const snippet = await this.extractText(container, this.selectors.snippet) || '';

      // Determine result type
      const type = this.determineResultType(container, isAd);

      // Extract metadata
      const metadata = await this.extractMetadata(container, url);

      return {
        title,
        url,
        snippet,
        position,
        type,
        metadata: metadata || undefined
      };

    } catch (error) {
      logger.warn(`Failed to parse Bing result at position ${position}:`, error);
      return null;
    }
  }

  private async extractText(container: Locator, selectors: string[]): Promise<string | null> {
    for (const selector of selectors) {
      try {
        const element = container.locator(selector).first();
        const text = await element.textContent({ timeout: 1000 });
        if (text && text.trim()) {
          return text.trim();
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    return null;
  }

  private async extractUrl(container: Locator, selectors: string[]): Promise<string | null> {
    for (const selector of selectors) {
      try {
        const element = container.locator(selector).first();
        const href = await element.getAttribute('href', { timeout: 1000 });
        if (href) {
          return this.cleanUrl(href);
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    return null;
  }

  private cleanUrl(url: string): string {
    // Remove Bing redirect parameters
    if (url.includes('bing.com/ck/a?')) {
      try {
        const urlObj = new URL(url);
        const actualUrl = urlObj.searchParams.get('u');
        if (actualUrl) {
          // Decode the base64-encoded URL
          return decodeURIComponent(actualUrl);
        }
      } catch (error) {
        // Fall back to original URL
      }
    }
    
    if (url.startsWith('http')) {
      return url;
    }
    
    return url;
  }

  private async isAdvertisement(container: Locator): Promise<boolean> {
    for (const selector of this.selectors.adIndicator) {
      try {
        const adElement = container.locator(selector).first();
        const isVisible = await adElement.isVisible({ timeout: 500 });
        if (isVisible) {
          return true;
        }
      } catch (error) {
        // Continue checking
      }
    }

    // Check for Bing ad text indicators
    try {
      const text = await container.textContent();
      if (text?.toLowerCase().includes('ad') || 
          text?.toLowerCase().includes('sponsored') ||
          text?.toLowerCase().includes('advertisement')) {
        return true;
      }
    } catch (error) {
      // Ignore
    }

    return false;
  }

  private determineResultType(container: Locator, isAd: boolean): SearchResult['type'] {
    if (isAd) {
      return 'ad';
    }
    return 'organic';
  }

  private async extractMetadata(container: Locator, url: string): Promise<SearchResult['metadata']> {
    try {
      const domain = new URL(url).hostname;
      
      return {
        domain,
        breadcrumbs: undefined,
        sitelinks: undefined
      };
    } catch (error) {
      return {
        domain: 'unknown',
        breadcrumbs: undefined,
        sitelinks: undefined
      };
    }
  }

  private async getElements(page: Page, selectors: string[]): Promise<Locator[]> {
    for (const selector of selectors) {
      try {
        const elements = page.locator(selector);
        const count = await elements.count();
        if (count > 0) {
          const locators: Locator[] = [];
          for (let i = 0; i < count; i++) {
            locators.push(elements.nth(i));
          }
          return locators;
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    return [];
  }

  private async hasNextPage(page: Page): Promise<boolean> {
    for (const selector of this.selectors.nextPageButton) {
      try {
        const nextButton = page.locator(selector).first();
        const isVisible = await nextButton.isVisible({ timeout: 1000 });
        if (isVisible) {
          return true;
        }
      } catch (error) {
        // Continue checking
      }
    }
    return false;
  }

  private async getTotalResults(page: Page): Promise<number | undefined> {
    const resultStatsSelectors = ['.sb_count', '.b_focusTextLarge', '.b_anscount'];
    
    for (const selector of resultStatsSelectors) {
      try {
        const element = page.locator(selector).first();
        const text = await element.textContent({ timeout: 1000 });
        if (text) {
          const match = text.match(/[\d,]+/);
          if (match) {
            return Number.parseInt(match[0].replace(/,/g, ''), 10);
          }
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    
    return undefined;
  }

  private async getRelatedQueries(page: Page): Promise<string[]> {
    const relatedSelectors = ['.b_rs li a', '.b_algoBR a'];
    
    for (const selector of relatedSelectors) {
      try {
        const elements = page.locator(selector);
        const count = await elements.count();
        const queries: string[] = [];
        
        for (let i = 0; i < count; i++) {
          const text = await elements.nth(i).textContent();
          if (text && text.trim()) {
            queries.push(text.trim());
          }
        }
        
        if (queries.length > 0) {
          return queries;
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    
    return [];
  }

  private getCurrentPage(page: Page): number | undefined {
    try {
      const url = page.url();
      const urlParams = new URLSearchParams(new URL(url).search);
      const first = urlParams.get('first');
      const count = urlParams.get('count') || '10';
      
      if (first) {
        return Math.floor(Number.parseInt(first, 10) / Number.parseInt(count, 10)) + 1;
      }
      
      return 1;
    } catch (error) {
      return undefined;
    }
  }

  /**
   * Count total number of Bing Ads on the page
   */
  private async countAdsOnPage(page: Page): Promise<number> {
    try {
      const adCounts = {
        bySelector: 0,
        byText: 0,
        byPosition: 0
      };

      // Method 1: Bing-specific ad selectors
      const bingAdSelectors = [
        '.b_ad',
        '.b_adlabel',
        '.b_adLastChild',
        '.sb_adta',
        '.b_adurl',
        '.b_adslug',
        '.b_ads .b_algo',
        '.b_pole .b_ad',
        '.b_wlkr .b_ad'
      ];

      // Count unique ad containers
      const uniqueAdContainers = new Set<string>();

      for (const selector of bingAdSelectors) {
        try {
          const elements = page.locator(selector);
          const count = await elements.count();
          
          for (let i = 0; i < count; i++) {
            const element = elements.nth(i);
            const boundingBox = await element.boundingBox().catch(() => null);
            if (boundingBox) {
              const key = `${Math.round(boundingBox.x)},${Math.round(boundingBox.y)},${Math.round(boundingBox.width)},${Math.round(boundingBox.height)}`;
              uniqueAdContainers.add(key);
            }
          }
        } catch (error) {
          // Continue with next selector
        }
      }

      adCounts.bySelector = uniqueAdContainers.size;

      // Method 2: Text-based detection for Bing
      try {
        const textBasedAds = await page.evaluate(() => {
          const adTextPatterns = [
            /\bsponsored\b/i,
            /\badvertisement\b/i,
            /^ad$/i,
            /^ads$/i
          ];

          const excludePatterns = [
            /address/i,
            /added/i,
            /advanced/i
          ];

          const adElements = document.querySelectorAll('*');
          const foundAds = new Set();

          for (const element of adElements) {
            const text = element.textContent?.trim() || '';
            const hasAdText = adTextPatterns.some(pattern => pattern.test(text));
            const hasExclude = excludePatterns.some(pattern => pattern.test(text));

            if (hasAdText && !hasExclude && text.length < 50) {
              const rect = element.getBoundingClientRect();
              if (rect.width > 0 && rect.height > 0) {
                const containerKey = `${Math.round(rect.x)},${Math.round(rect.y)}`;
                foundAds.add(containerKey);
              }
            }
          }

          return foundAds.size;
        });

        adCounts.byText = textBasedAds;
      } catch (error) {
        logger.debug('Failed to count Bing ads by text content:', error);
      }

      // Method 3: Position-based detection for Bing
      try {
        const positionBasedAds = await page.evaluate(() => {
          const topAdArea = document.querySelector('#b_pole, .b_ads');
          const sideAdArea = document.querySelector('#b_context .b_ad');
          let count = 0;

          if (topAdArea) {
            const topAds = topAdArea.querySelectorAll('.b_ad, .b_algo');
            count += topAds.length;
          }

          if (sideAdArea) {
            const sideAds = sideAdArea.querySelectorAll('.b_ad, .b_algo');
            count += sideAds.length;
          }

          return count;
        });

        adCounts.byPosition = positionBasedAds;
      } catch (error) {
        logger.debug('Failed to count Bing ads by position:', error);
      }

      // Use the highest count from our detection methods
      const finalCount = Math.max(
        adCounts.bySelector,
        adCounts.byText,
        adCounts.byPosition
      );

      logger.info(`Found ${finalCount} Bing ads on the page`, {
        bySelector: adCounts.bySelector,
        byText: adCounts.byText,
        byPosition: adCounts.byPosition,
        final: finalCount
      });

      return finalCount;

    } catch (error) {
      logger.error('Failed to count Bing ads on page:', error);
      return 0;
    }
  }

  /**
   * Count total number of links on the page
   */
  private async countLinksOnPage(page: Page): Promise<number> {
    try {
      const linkCount = await page.locator('a[href]').count();
      logger.info(`Found ${linkCount} total links on Bing page`);
      return linkCount;
    } catch (error) {
      logger.error('Failed to count links on Bing page:', error);
      return 0;
    }
  }

  /**
   * Get HTML cache of the page with compression and cleaning
   */
  private async getPageHtmlCache(page: Page): Promise<string> {
    try {
      let htmlContent = await page.content();
      const originalSize = htmlContent.length;

      // Clean up HTML to reduce size while preserving important content
      htmlContent = this.cleanHtmlForStorage(htmlContent);
      
      // Compress HTML using simple techniques
      htmlContent = this.compressHtml(htmlContent);

      const finalSize = htmlContent.length;
      const compressionRatio = ((originalSize - finalSize) / originalSize * 100).toFixed(1);

      logger.info(`Cached and compressed Bing HTML content`, {
        originalSize,
        finalSize,
        compressionRatio: `${compressionRatio}%`,
        savedBytes: originalSize - finalSize
      });

      return htmlContent;
    } catch (error) {
      logger.error('Failed to get Bing page HTML cache:', error);
      return '';
    }
  }

  private cleanHtmlForStorage(html: string): string {
    try {
      // Remove scripts, styles, comments, and other non-essential content
      let cleanedHtml = html
        .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
        .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
        .replace(/<!--[\s\S]*?-->/g, '')
        .replace(/\sstyle="[^"]*"/gi, '')
        .replace(/\son\w+="[^"]*"/gi, '')
        .replace(/\s+/g, ' ')
        .replace(/>\s+</g, '><')
        .trim();

      return cleanedHtml;
    } catch (error) {
      logger.warn('Failed to clean Bing HTML, returning original:', error);
      return html;
    }
  }

  private compressHtml(html: string): string {
    try {
      let compressed = html
        .replace(/>\s+</g, '><')
        .replace(/\s*=\s*/g, '=')
        .replace(/\s{2,}/g, ' ')
        .replace(/\s+$/gm, '')
        .replace(/^\s*[\r\n]/gm, '');

      return compressed.trim();
    } catch (error) {
      logger.warn('Failed to compress Bing HTML, returning original:', error);
      return html;
    }
  }

  /**
   * Extract detailed links arrays categorized by type for Bing
   */
  private async extractDetailedLinks(page: Page, searchResults: SearchResult[]): Promise<{
    adsLinks: LinkDetail[];
    organicLinks: LinkDetail[];
    otherLinks: LinkDetail[];
  }> {
    try {
      const adsLinks: LinkDetail[] = [];
      const organicLinks: LinkDetail[] = [];
      const otherLinks: LinkDetail[] = [];

      const allLinks = page.locator('a[href]');
      const linkCount = await allLinks.count();

      logger.info(`Processing ${linkCount} Bing links for detailed categorization`);

      for (let i = 0; i < linkCount; i++) {
        try {
          const link = allLinks.nth(i);
          const href = await link.getAttribute('href');
          const text = await link.textContent() || '';
          
          if (!href || href.startsWith('#') || href.startsWith('javascript:')) {
            continue;
          }

          const cleanedUrl = this.cleanUrl(href);
          const domain = this.extractDomain(cleanedUrl);
          
          if (this.isBingInternalLink(cleanedUrl)) {
            continue;
          }

          const linkDetail: LinkDetail = {
            url: cleanedUrl,
            title: text.trim(),
            position: i + 1,
            type: 'other',
            domain,
            anchorText: text.trim()
          };

          const linkType = await this.categorizeLinkType(link, cleanedUrl, searchResults);
          linkDetail.type = linkType;

          switch (linkType) {
            case 'ad':
              adsLinks.push(linkDetail);
              break;
            case 'organic':
              organicLinks.push(linkDetail);
              break;
            default:
              otherLinks.push(linkDetail);
              break;
          }

        } catch (error) {
          logger.debug(`Failed to process Bing link ${i}:`, error);
        }
      }

      logger.info(`Categorized Bing links: ${adsLinks.length} ads, ${organicLinks.length} organic, ${otherLinks.length} other`);

      return { adsLinks, organicLinks, otherLinks };

    } catch (error) {
      logger.error('Failed to extract detailed Bing links:', error);
      return { adsLinks: [], organicLinks: [], otherLinks: [] };
    }
  }

  private async categorizeLinkType(
    link: Locator, 
    url: string, 
    searchResults: SearchResult[]
  ): Promise<LinkDetail['type']> {
    try {
      // Check if this link is from an ad container
      const isInAdContainer = await this.isLinkInAdContainer(link);
      if (isInAdContainer) {
        return 'ad';
      }

      // Check if this link matches any of our parsed search results
      const matchingResult = searchResults.find(result => result.url === url);
      if (matchingResult) {
        return matchingResult.type === 'ad' ? 'ad' : 'organic';
      }

      // Check if it's in main search results area
      const isInSearchResults = await this.isLinkInSearchResults(link);
      if (isInSearchResults) {
        return 'organic';
      }

      return 'other';

    } catch (error) {
      return 'other';
    }
  }

  private async isLinkInAdContainer(link: Locator): Promise<boolean> {
    try {
      const bingAdSelectors = ['.b_ad', '.b_adlabel', '.b_ads'];
      
      for (const selector of bingAdSelectors) {
        const adContainer = link.locator(`xpath=ancestor::*[contains(@class, "${selector.replace('.', '')}")]`);
        const count = await adContainer.count();
        if (count > 0) {
          return true;
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  private async isLinkInSearchResults(link: Locator): Promise<boolean> {
    try {
      const searchResultSelectors = ['.b_algo', '#b_results'];
      
      for (const selector of searchResultSelectors) {
        const resultContainer = link.locator(`xpath=ancestor::*[contains(@class, "${selector.replace('.', '').replace('#', '')}") or contains(@id, "${selector.replace('.', '').replace('#', '')}")]`);
        const count = await resultContainer.count();
        if (count > 0) {
          return true;
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  private extractDomain(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      return 'unknown';
    }
  }

  private isBingInternalLink(url: string): boolean {
    const bingPatterns = [
      'bing.com/maps',
      'bing.com/images',
      'bing.com/videos',
      'bing.com/news',
      'bing.com/dict',
      'bing.com/search'
    ];

    return bingPatterns.some(pattern => url.includes(pattern));
  }
}