import { SearchResultParser } from '../SearchResultParser';
import type { Page, Locator } from 'patchright';
import type { SearchResult, SearchResponse } from '../../../types';

// Mock patchright
jest.mock('patchright', () => ({
  Page: jest.fn(),
  Locator: jest.fn(),
}));

// Mock logger
jest.mock('../../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('SearchResultParser', () => {
  let parser: SearchResultParser;
  let mockPage: jest.Mocked<Page>;
  let mockLocator: jest.Mocked<Locator>;

  beforeEach(() => {
    parser = new SearchResultParser();
    
    // Create mock locator with chainable methods
    mockLocator = {
      first: jest.fn().mockReturnThis(),
      nth: jest.fn().mockReturnThis(),
      locator: jest.fn().mockReturnThis(),
      count: jest.fn().mockResolvedValue(0),
      textContent: jest.fn().mockResolvedValue(''),
      getAttribute: jest.fn().mockResolvedValue(''),
      isVisible: jest.fn().mockResolvedValue(false),
      boundingBox: jest.fn().mockResolvedValue(null),
    } as any;

    // Create mock page
    mockPage = {
      locator: jest.fn().mockReturnValue(mockLocator),
      content: jest.fn().mockResolvedValue('<html></html>'),
      url: jest.fn().mockReturnValue('https://www.google.com/search?q=test'),
      evaluate: jest.fn().mockResolvedValue(0),
    } as any;

    jest.clearAllMocks();
  });

  describe('parse', () => {
    it('should parse basic search results successfully', async () => {
      const query = 'test query';

      // Mock finding result containers
      mockLocator.count.mockResolvedValueOnce(2); // 2 result containers
      mockLocator.nth.mockImplementation((index) => {
        const containerLocator = { ...mockLocator };
        
        // Mock parseResultContainer data
        containerLocator.locator.mockReturnValue(mockLocator);
        mockLocator.textContent.mockResolvedValue(index === 0 ? 'First Result Title' : 'Second Result Title');
        mockLocator.getAttribute.mockResolvedValue(index === 0 ? 'https://example1.com' : 'https://example2.com');
        mockLocator.isVisible.mockResolvedValue(false); // Not an ad
        
        return containerLocator;
      });

      // Mock other page methods
      mockLocator.count
        .mockResolvedValueOnce(2) // result containers
        .mockResolvedValueOnce(0) // next page button
        .mockResolvedValueOnce(0) // total results
        .mockResolvedValueOnce(0) // related queries
        .mockResolvedValueOnce(10); // total links count

      mockPage.content.mockResolvedValue('<html><body>Test HTML</body></html>');
      mockPage.evaluate.mockResolvedValue(0); // ads count

      const result = await parser.parse(mockPage, query);

      expect(result).toEqual({
        query,
        totalResults: undefined,
        results: expect.any(Array),
        relatedQueries: undefined,
        timestamp: expect.any(Date),
        page: 1,
        hasNextPage: false,
        adsCount: 0,
        totalLinksCount: 10,
        htmlCache: expect.any(String),
        adsLinks: [],
        organicLinks: [],
        otherLinks: [],
      });
    });

    it('should handle parsing errors gracefully', async () => {
      const query = 'test query';
      
      // Mock error in getting elements
      mockLocator.count.mockRejectedValue(new Error('Page error'));

      await expect(parser.parse(mockPage, query)).rejects.toThrow('Page error');
    });

    it('should extract search results with ads correctly', async () => {
      const query = 'test ads query';

      // Mock finding result containers with one ad
      let callCount = 0;
      mockLocator.count.mockImplementation(() => {
        callCount++;
        if (callCount === 1) return Promise.resolve(2); // result containers
        if (callCount === 2) return Promise.resolve(0); // next page button
        if (callCount === 3) return Promise.resolve(0); // total results
        if (callCount === 4) return Promise.resolve(0); // related queries
        if (callCount === 5) return Promise.resolve(15); // total links count
        return Promise.resolve(0);
      });

      mockLocator.nth.mockImplementation((index) => {
        const containerLocator = { ...mockLocator };
        
        // Mock different results for each container
        let textCallCount = 0;
        containerLocator.locator.mockImplementation(() => {
          const elementLocator = { ...mockLocator };
          elementLocator.textContent.mockImplementation(() => {
            textCallCount++;
            if (index === 0) {
              return Promise.resolve(textCallCount === 1 ? 'Ad Result Title' : 'Ad snippet');
            } else {
              return Promise.resolve(textCallCount === 1 ? 'Organic Result Title' : 'Organic snippet');
            }
          });
          elementLocator.getAttribute.mockResolvedValue(index === 0 ? 'https://ad-example.com' : 'https://organic-example.com');
          elementLocator.isVisible.mockResolvedValue(index === 0); // First is ad, second is not
          elementLocator.count.mockResolvedValue(0); // No breadcrumbs/sitelinks
          return elementLocator;
        });
        
        return containerLocator;
      });

      // Mock ads count detection
      mockPage.evaluate.mockResolvedValue(3);

      const result = await parser.parse(mockPage, query);

      expect(result.adsCount).toBe(3);
      expect(result.totalLinksCount).toBe(15);
      expect(result.results).toHaveLength(2);
    });
  });

  describe('URL cleaning', () => {
    it('should clean Google redirect URLs', () => {
      // Access private method via bracket notation for testing
      const cleanUrl = (parser as any).cleanUrl.bind(parser);

      // Test Google redirect URL
      const googleRedirect = '/url?q=https://example.com/page&sa=U&ved=123';
      expect(cleanUrl(googleRedirect)).toBe('https://example.com/page');

      // Test normal URL
      const normalUrl = 'https://example.com/page';
      expect(cleanUrl(normalUrl)).toBe('https://example.com/page');

      // Test relative URL
      const relativeUrl = '/relative/path';
      expect(cleanUrl(relativeUrl)).toBe('/relative/path');
    });
  });

  describe('Ad detection', () => {
    it('should detect ads by selectors', async () => {
      const mockContainer = { ...mockLocator };
      mockContainer.locator.mockReturnValue(mockLocator);
      mockLocator.isVisible.mockResolvedValue(true);

      const isAd = await (parser as any).isAdvertisement(mockContainer);
      expect(isAd).toBe(true);
    });

    it('should detect ads by text content', async () => {
      const mockContainer = { ...mockLocator };
      mockContainer.locator.mockReturnValue(mockLocator);
      mockLocator.isVisible.mockResolvedValue(false); // No ad selectors
      mockContainer.textContent.mockResolvedValue('This is a sponsored result');

      const isAd = await (parser as any).isAdvertisement(mockContainer);
      expect(isAd).toBe(true);
    });

    it('should not detect organic results as ads', async () => {
      const mockContainer = { ...mockLocator };
      mockContainer.locator.mockReturnValue(mockLocator);
      mockLocator.isVisible.mockResolvedValue(false);
      mockContainer.textContent.mockResolvedValue('Organic search result');

      const isAd = await (parser as any).isAdvertisement(mockContainer);
      expect(isAd).toBe(false);
    });
  });

  describe('Text extraction', () => {
    it('should extract text from first matching selector', async () => {
      const mockContainer = { ...mockLocator };
      const selectors = ['.title1', '.title2', '.title3'];
      
      mockContainer.locator.mockImplementation((selector) => {
        const elementLocator = { ...mockLocator };
        if (selector === '.title2') {
          elementLocator.textContent.mockResolvedValue('Found Title');
        } else {
          elementLocator.textContent.mockResolvedValue(null);
        }
        return elementLocator;
      });

      const text = await (parser as any).extractText(mockContainer, selectors);
      expect(text).toBe('Found Title');
    });

    it('should return null if no text found', async () => {
      const mockContainer = { ...mockLocator };
      const selectors = ['.title1', '.title2'];
      
      mockContainer.locator.mockReturnValue(mockLocator);
      mockLocator.textContent.mockResolvedValue(null);

      const text = await (parser as any).extractText(mockContainer, selectors);
      expect(text).toBeNull();
    });

    it('should trim whitespace from extracted text', async () => {
      const mockContainer = { ...mockLocator };
      const selectors = ['.title'];
      
      mockContainer.locator.mockReturnValue(mockLocator);
      mockLocator.textContent.mockResolvedValue('  Title with spaces  ');

      const text = await (parser as any).extractText(mockContainer, selectors);
      expect(text).toBe('Title with spaces');
    });
  });

  describe('URL extraction', () => {
    it('should extract and clean URLs', async () => {
      const mockContainer = { ...mockLocator };
      const selectors = ['a'];
      
      mockContainer.locator.mockReturnValue(mockLocator);
      mockLocator.getAttribute.mockResolvedValue('/url?q=https://example.com&sa=U');

      const url = await (parser as any).extractUrl(mockContainer, selectors);
      expect(url).toBe('https://example.com');
    });

    it('should return null if no href found', async () => {
      const mockContainer = { ...mockLocator };
      const selectors = ['a'];
      
      mockContainer.locator.mockReturnValue(mockLocator);
      mockLocator.getAttribute.mockResolvedValue(null);

      const url = await (parser as any).extractUrl(mockContainer, selectors);
      expect(url).toBeNull();
    });
  });

  describe('Page navigation detection', () => {
    it('should detect next page button', async () => {
      mockLocator.isVisible.mockResolvedValue(true);

      const hasNext = await (parser as any).hasNextPage(mockPage);
      expect(hasNext).toBe(true);
    });

    it('should handle missing next page button', async () => {
      mockLocator.isVisible.mockResolvedValue(false);

      const hasNext = await (parser as any).hasNextPage(mockPage);
      expect(hasNext).toBe(false);
    });
  });

  describe('Total results parsing', () => {
    it('should parse total results from stats', async () => {
      mockLocator.textContent.mockResolvedValue('About 1,234,567 results');

      const total = await (parser as any).getTotalResults(mockPage);
      expect(total).toBe(1234567);
    });

    it('should handle missing result stats', async () => {
      mockLocator.textContent.mockResolvedValue(null);

      const total = await (parser as any).getTotalResults(mockPage);
      expect(total).toBeUndefined();
    });

    it('should parse different number formats', async () => {
      mockLocator.textContent.mockResolvedValue('Approximately 500,000 results (0.45 seconds)');

      const total = await (parser as any).getTotalResults(mockPage);
      expect(total).toBe(500000);
    });
  });

  describe('Current page detection', () => {
    it('should detect page number from URL start parameter', () => {
      mockPage.url.mockReturnValue('https://www.google.com/search?q=test&start=20');

      const pageNum = (parser as any).getCurrentPage(mockPage);
      expect(pageNum).toBe(3); // start=20 means page 3 (20/10 + 1)
    });

    it('should default to page 1 if no start parameter', () => {
      mockPage.url.mockReturnValue('https://www.google.com/search?q=test');

      const pageNum = (parser as any).getCurrentPage(mockPage);
      expect(pageNum).toBe(1);
    });

    it('should handle malformed URLs', () => {
      mockPage.url.mockReturnValue('invalid-url');

      const pageNum = (parser as any).getCurrentPage(mockPage);
      expect(pageNum).toBeUndefined();
    });
  });

  describe('HTML cleaning and compression', () => {
    it('should remove scripts and styles from HTML', () => {
      const dirtyHtml = `
        <html>
          <head>
            <script>alert('test');</script>
            <style>body { color: red; }</style>
          </head>
          <body>
            <div>Content</div>
          </body>
        </html>
      `;

      const cleaned = (parser as any).cleanHtmlForStorage(dirtyHtml);
      
      expect(cleaned).not.toContain('<script>');
      expect(cleaned).not.toContain('<style>');
      expect(cleaned).toContain('<div>Content</div>');
    });

    it('should compress HTML by removing extra whitespace', () => {
      const bloatedHtml = `<html>  <body>   <div>  Content  </div>  </body>  </html>`;

      const compressed = (parser as any).compressHtml(bloatedHtml);
      
      expect(compressed).toBe('<html><body><div> Content </div></body></html>');
    });

    it('should handle HTML cleaning errors gracefully', () => {
      const originalHtml = '<html><body>Content</body></html>';

      // Mock a cleaning error by making string methods throw
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      const result = (parser as any).cleanHtmlForStorage(originalHtml);
      
      expect(result).toEqual(originalHtml);
      consoleSpy.mockRestore();
    });
  });

  describe('Link categorization', () => {
    it('should count total links on page', async () => {
      mockLocator.count.mockResolvedValue(25);

      const count = await (parser as any).countLinksOnPage(mockPage);
      expect(count).toBe(25);
    });

    it('should handle link counting errors', async () => {
      mockLocator.count.mockRejectedValue(new Error('Count failed'));

      const count = await (parser as any).countLinksOnPage(mockPage);
      expect(count).toBe(0);
    });

    it('should extract domain from URL', () => {
      const extractDomain = (parser as any).extractDomain.bind(parser);

      expect(extractDomain('https://www.example.com/path')).toBe('www.example.com');
      expect(extractDomain('http://subdomain.example.org')).toBe('subdomain.example.org');
      expect(extractDomain('invalid-url')).toBe('unknown');
    });

    it('should identify Google internal links', () => {
      const isGoogleInternal = (parser as any).isGoogleInternalLink.bind(parser);

      expect(isGoogleInternal('https://accounts.google.com/login')).toBe(true);
      expect(isGoogleInternal('https://support.google.com/help')).toBe(true);
      expect(isGoogleInternal('https://www.example.com')).toBe(false);
    });
  });

  describe('Ads counting', () => {
    it('should count ads using multiple detection methods', async () => {
      // Mock page.evaluate calls for different detection methods
      mockPage.evaluate
        .mockResolvedValueOnce(3) // textBasedAds
        .mockResolvedValueOnce(2) // positionBasedAds  
        .mockResolvedValueOnce(4); // structureBasedAds

      // Mock selector-based detection
      mockLocator.count.mockResolvedValue(2);
      mockLocator.boundingBox.mockResolvedValue({ x: 10, y: 10, width: 200, height: 100 });

      const count = await (parser as any).countAdsOnPage(mockPage);
      
      // Should return the maximum count from all methods
      expect(count).toBe(4);
    });

    it('should handle ads counting errors', async () => {
      mockPage.evaluate.mockRejectedValue(new Error('Evaluation failed'));
      mockLocator.count.mockRejectedValue(new Error('Count failed'));

      const count = await (parser as any).countAdsOnPage(mockPage);
      expect(count).toBe(0);
    });

    it('should cap ads count at reasonable limit', async () => {
      // Mock unreasonably high counts
      mockPage.evaluate
        .mockResolvedValueOnce(50)
        .mockResolvedValueOnce(45)
        .mockResolvedValueOnce(40);
      
      mockLocator.count.mockResolvedValue(0);

      const count = await (parser as any).countAdsOnPage(mockPage);
      
      // Should be capped at 20 (reasonable limit)
      expect(count).toBeLessThanOrEqual(20);
    });
  });

  describe('Result container parsing', () => {
    it('should parse complete result container with metadata', async () => {
      const mockContainer = { ...mockLocator };
      const position = 1;

      // Mock successful extraction
      mockContainer.locator.mockReturnValue(mockLocator);
      mockLocator.isVisible.mockResolvedValue(false); // Not an ad
      mockLocator.textContent
        .mockResolvedValueOnce('Test Result Title') // title
        .mockResolvedValueOnce('This is a test snippet'); // snippet
      mockLocator.getAttribute.mockResolvedValue('https://example.com/test');

      // Mock breadcrumbs and sitelinks extraction
      mockLocator.count.mockResolvedValue(0); // No breadcrumbs/sitelinks

      const result = await (parser as any).parseResultContainer(mockContainer, position);

      expect(result).toEqual({
        title: 'Test Result Title',
        url: 'https://example.com/test',
        snippet: 'This is a test snippet',
        position: 1,
        type: 'organic',
        metadata: {
          domain: 'example.com',
          breadcrumbs: undefined,
          sitelinks: undefined,
        },
      });
    });

    it('should skip container if no title found', async () => {
      const mockContainer = { ...mockLocator };
      mockContainer.locator.mockReturnValue(mockLocator);
      mockLocator.textContent.mockResolvedValue(null); // No title

      const result = await (parser as any).parseResultContainer(mockContainer, 1);
      expect(result).toBeNull();
    });

    it('should skip container if no URL found', async () => {
      const mockContainer = { ...mockLocator };
      mockContainer.locator.mockReturnValue(mockLocator);
      mockLocator.textContent.mockResolvedValue('Test Title');
      mockLocator.getAttribute.mockResolvedValue(null); // No URL

      const result = await (parser as any).parseResultContainer(mockContainer, 1);
      expect(result).toBeNull();
    });

    it('should handle container parsing errors gracefully', async () => {
      const mockContainer = { ...mockLocator };
      mockContainer.locator.mockImplementation(() => {
        throw new Error('Locator error');
      });

      const result = await (parser as any).parseResultContainer(mockContainer, 1);
      expect(result).toBeNull();
    });
  });

  describe('Related queries extraction', () => {
    it('should extract related queries when available', async () => {
      mockLocator.count.mockResolvedValue(3);
      mockLocator.nth.mockImplementation((index) => {
        const queryLocator = { ...mockLocator };
        queryLocator.textContent.mockResolvedValue(`Related query ${index + 1}`);
        return queryLocator;
      });

      const queries = await (parser as any).getRelatedQueries(mockPage);
      expect(queries).toEqual(['Related query 1', 'Related query 2', 'Related query 3']);
    });

    it('should return empty array when no related queries found', async () => {
      mockLocator.count.mockResolvedValue(0);

      const queries = await (parser as any).getRelatedQueries(mockPage);
      expect(queries).toEqual([]);
    });
  });
});