import { SearchResultParser } from '../SearchResultParser';

// Mock logger
jest.mock('../../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('SearchResultParser - Integration Tests', () => {
  let parser: SearchResultParser;

  beforeEach(() => {
    parser = new SearchResultParser();
    jest.clearAllMocks();
  });

  describe('Full parse workflow', () => {
    it('should parse empty search results page', async () => {
      const mockPage = createMockPage({
        resultContainers: 0,
        hasNextPage: false,
        totalLinks: 5,
        adsCount: 0,
        htmlContent: '<html><body>No results</body></html>'
      });

      const result = await parser.parse(mockPage as any, 'empty query');

      expect(result).toMatchObject({
        query: 'empty query',
        results: [],
        adsCount: 0,
        totalLinksCount: 5,
        hasNextPage: false,
        page: 1,
        htmlCache: expect.any(String),
        adsLinks: [],
        organicLinks: [],
        otherLinks: []
      });
    });

    it('should parse search results with organic results only', async () => {
      const mockPage = createMockPage({
        resultContainers: 3,
        results: [
          { title: 'Result 1', url: 'https://example1.com', snippet: 'Snippet 1', isAd: false },
          { title: 'Result 2', url: 'https://example2.com', snippet: 'Snippet 2', isAd: false },
          { title: 'Result 3', url: 'https://example3.com', snippet: 'Snippet 3', isAd: false }
        ],
        hasNextPage: true,
        totalLinks: 25,
        adsCount: 0
      });

      const result = await parser.parse(mockPage as any, 'organic query');

      expect(result).toMatchObject({
        query: 'organic query',
        results: expect.arrayContaining([
          expect.objectContaining({
            title: 'Result 1',
            url: 'https://example1.com',
            type: 'organic',
            position: 1
          }),
          expect.objectContaining({
            title: 'Result 2', 
            url: 'https://example2.com',
            type: 'organic',
            position: 2
          })
        ]),
        adsCount: 0,
        hasNextPage: true
      });
      
      expect(result.results).toHaveLength(3);
    });

    it('should parse search results with ads and organic results', async () => {
      const mockPage = createMockPage({
        resultContainers: 4,
        results: [
          { title: 'Ad 1', url: 'https://ad1.com', snippet: 'Ad snippet 1', isAd: true },
          { title: 'Ad 2', url: 'https://ad2.com', snippet: 'Ad snippet 2', isAd: true },
          { title: 'Organic 1', url: 'https://organic1.com', snippet: 'Organic snippet 1', isAd: false },
          { title: 'Organic 2', url: 'https://organic2.com', snippet: 'Organic snippet 2', isAd: false }
        ],
        hasNextPage: false,
        totalLinks: 30,
        adsCount: 2
      });

      const result = await parser.parse(mockPage as any, 'mixed query');

      expect(result).toMatchObject({
        query: 'mixed query',
        results: expect.arrayContaining([
          expect.objectContaining({
            title: 'Ad 1',
            type: 'ad',
            position: 1
          }),
          expect.objectContaining({
            title: 'Organic 1',
            type: 'organic',
            position: 3
          })
        ]),
        adsCount: 2,
        totalLinksCount: 30
      });

      expect(result.results).toHaveLength(4);
      const adResults = result.results.filter(r => r.type === 'ad');
      const organicResults = result.results.filter(r => r.type === 'organic');
      expect(adResults).toHaveLength(2);
      expect(organicResults).toHaveLength(2);
    });

    it('should handle parsing errors gracefully', async () => {
      const mockPage = createMockPageWithErrors();

      await expect(parser.parse(mockPage as any, 'error query')).rejects.toThrow('Simulated page error');
    });

    it('should parse with total results count', async () => {
      const mockPage = createMockPage({
        resultContainers: 2,
        results: [
          { title: 'Result 1', url: 'https://example1.com', snippet: 'Snippet 1', isAd: false },
          { title: 'Result 2', url: 'https://example2.com', snippet: 'Snippet 2', isAd: false }
        ],
        totalResultsText: 'About 1,234,567 results (0.45 seconds)',
        relatedQueries: ['related 1', 'related 2'],
        hasNextPage: true,
        totalLinks: 20,
        adsCount: 0
      });

      const result = await parser.parse(mockPage as any, 'comprehensive query');

      expect(result).toMatchObject({
        query: 'comprehensive query',
        totalResults: 1234567,
        relatedQueries: ['related 1', 'related 2'],
        hasNextPage: true,
        page: 1
      });
    });
  });

  describe('Edge cases', () => {
    it('should handle results with missing data gracefully', async () => {
      const mockPage = createMockPage({
        resultContainers: 2,
        results: [
          { title: 'Complete Result', url: 'https://complete.com', snippet: 'Complete snippet', isAd: false },
          { title: '', url: 'https://incomplete.com', snippet: 'No title', isAd: false } // This should be filtered out
        ],
        hasNextPage: false,
        totalLinks: 10,
        adsCount: 0
      });

      const result = await parser.parse(mockPage as any, 'incomplete data query');

      // Should only include the complete result
      expect(result.results).toHaveLength(1);
      expect(result.results[0]).toMatchObject({
        title: 'Complete Result',
        url: 'https://complete.com'
      });
    });

    it('should handle results with Google redirect URLs', async () => {
      const mockPage = createMockPage({
        resultContainers: 1,
        results: [
          { 
            title: 'Redirected Result', 
            url: '/url?q=https://realurl.com/page&sa=U&ved=123', 
            snippet: 'Redirected snippet', 
            isAd: false 
          }
        ],
        hasNextPage: false,
        totalLinks: 8,
        adsCount: 0
      });

      const result = await parser.parse(mockPage as any, 'redirect query');

      expect(result.results).toHaveLength(1);
      expect(result.results[0].url).toBe('https://realurl.com/page');
    });
  });
});

// Helper function to create mock page with specified behavior
function createMockPage(options: {
  resultContainers: number;
  results?: Array<{title: string; url: string; snippet: string; isAd: boolean}>;
  hasNextPage?: boolean;
  totalLinks?: number;
  adsCount?: number;
  htmlContent?: string;
  totalResultsText?: string;
  relatedQueries?: string[];
}) {
  const {
    resultContainers,
    results = [],
    hasNextPage = false,
    totalLinks = 0,
    adsCount = 0,
    htmlContent = '<html><body>Default content</body></html>',
    totalResultsText = null,
    relatedQueries = []
  } = options;

  let locatorCallCount = 0;
  let pageEvaluateCallCount = 0;

  const createMockLocator = (result?: any, index?: number) => ({
    count: jest.fn().mockImplementation(() => {
      locatorCallCount++;
      if (locatorCallCount === 1) return Promise.resolve(resultContainers); // result containers
      if (locatorCallCount === 2) return Promise.resolve(hasNextPage ? 1 : 0); // next page button
      if (locatorCallCount === 3) return Promise.resolve(totalResultsText ? 1 : 0); // total results
      if (locatorCallCount === 4) return Promise.resolve(relatedQueries.length); // related queries
      if (locatorCallCount === 5) return Promise.resolve(totalLinks); // total links count
      return Promise.resolve(0);
    }),
    
    nth: jest.fn().mockImplementation((idx: number) => {
      const containerResult = results[idx];
      if (!containerResult) return createMockLocator();

      let textCallIndex = 0;
      return {
        locator: jest.fn().mockImplementation(() => ({
          first: jest.fn().mockReturnThis(),
          textContent: jest.fn().mockImplementation(() => {
            textCallIndex++;
            if (textCallIndex === 1) return Promise.resolve(containerResult.title);
            if (textCallIndex === 2) return Promise.resolve(containerResult.snippet);
            if (relatedQueries.length > 0 && idx < relatedQueries.length) {
              return Promise.resolve(relatedQueries[idx]);
            }
            return Promise.resolve(totalResultsText || '');
          }),
          getAttribute: jest.fn().mockResolvedValue(containerResult.url),
          isVisible: jest.fn().mockResolvedValue(containerResult.isAd),
          count: jest.fn().mockResolvedValue(0) // No breadcrumbs/sitelinks
        })),
        textContent: jest.fn().mockResolvedValue(containerResult.isAd ? 'sponsored' : 'organic content'),
        isVisible: jest.fn().mockResolvedValue(containerResult.isAd)
      };
    }),

    first: jest.fn().mockReturnThis(),
    textContent: jest.fn().mockResolvedValue(totalResultsText),
    isVisible: jest.fn().mockResolvedValue(hasNextPage),
    boundingBox: jest.fn().mockResolvedValue({ x: 10, y: 10, width: 100, height: 50 })
  });

  const mockLocator = createMockLocator();

  return {
    locator: jest.fn().mockReturnValue(mockLocator),
    content: jest.fn().mockResolvedValue(htmlContent),
    url: jest.fn().mockReturnValue('https://www.google.com/search?q=test'),
    evaluate: jest.fn().mockImplementation(() => {
      pageEvaluateCallCount++;
      if (pageEvaluateCallCount <= 3) return Promise.resolve(adsCount); // Different ad counting methods
      return Promise.resolve(0);
    })
  };
}

// Helper function to create mock page that throws errors
function createMockPageWithErrors() {
  const mockLocator = {
    count: jest.fn().mockRejectedValue(new Error('Simulated page error')),
    nth: jest.fn(),
    first: jest.fn(),
    textContent: jest.fn(),
    isVisible: jest.fn(),
    boundingBox: jest.fn()
  };

  return {
    locator: jest.fn().mockReturnValue(mockLocator),
    content: jest.fn().mockResolvedValue(''),
    url: jest.fn().mockReturnValue(''),
    evaluate: jest.fn().mockResolvedValue(0)
  };
}