import { SearchResultParser } from '../SearchResultParser';
import type { Page, Locator } from 'patchright';

// Mock patchright
jest.mock('patchright');

// Mock logger
jest.mock('../../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('SearchResultParser - Simple Tests', () => {
  let parser: SearchResultParser;

  beforeEach(() => {
    parser = new SearchResultParser();
    jest.clearAllMocks();
  });

  describe('Basic functionality', () => {
    it('should be instantiated correctly', () => {
      expect(parser).toBeInstanceOf(SearchResultParser);
    });

    it('should have access to private methods for testing', () => {
      // Test private methods via bracket notation
      expect(typeof (parser as any).cleanUrl).toBe('function');
      expect(typeof (parser as any).extractText).toBe('function');
      expect(typeof (parser as any).isAdvertisement).toBe('function');
    });
  });

  describe('URL cleaning', () => {
    it('should clean Google redirect URLs', () => {
      const cleanUrl = (parser as any).cleanUrl.bind(parser);

      // Test Google redirect URL
      const googleRedirect = '/url?q=https://example.com/page&sa=U&ved=123';
      expect(cleanUrl(googleRedirect)).toBe('https://example.com/page');

      // Test normal URL
      const normalUrl = 'https://example.com/page';
      expect(cleanUrl(normalUrl)).toBe('https://example.com/page');

      // Test relative URL
      const relativeUrl = '/relative/path';
      expect(cleanUrl(relativeUrl)).toBe('/relative/path');
    });

    it('should handle malformed redirect URLs', () => {
      const cleanUrl = (parser as any).cleanUrl.bind(parser);

      const malformedUrl = '/url?invalid=params';
      expect(cleanUrl(malformedUrl)).toBe('/url?invalid=params');
    });

    it('should handle URL parameter variations', () => {
      const cleanUrl = (parser as any).cleanUrl.bind(parser);

      const urlWithUrl = '/url?url=https://test.com&other=param';
      expect(cleanUrl(urlWithUrl)).toBe('https://test.com');

      const urlWithQ = '/url?q=https://example.org&ved=123';
      expect(cleanUrl(urlWithQ)).toBe('https://example.org');
    });
  });

  describe('HTML processing', () => {
    it('should remove scripts and styles from HTML', () => {
      const cleanHtml = (parser as any).cleanHtmlForStorage.bind(parser);
      
      const dirtyHtml = `
        <html>
          <head>
            <script>alert('test');</script>
            <style>body { color: red; }</style>
          </head>
          <body>
            <div onclick="alert('click')">Content</div>
          </body>
        </html>
      `;

      const cleaned = cleanHtml(dirtyHtml);
      
      expect(cleaned).not.toContain('<script>');
      expect(cleaned).not.toContain('<style>');
      expect(cleaned).not.toContain('onclick=');
      expect(cleaned).toContain('<div>Content</div>');
    });

    it('should compress HTML by removing extra whitespace', () => {
      const compressHtml = (parser as any).compressHtml.bind(parser);
      
      const bloatedHtml = `<html>  <body>   <div>  Content  </div>  </body>  </html>`;
      const compressed = compressHtml(bloatedHtml);
      
      expect(compressed).toBe('<html><body><div> Content </div></body></html>');
    });

    it('should handle HTML processing errors gracefully', () => {
      const cleanHtml = (parser as any).cleanHtmlForStorage.bind(parser);
      const originalHtml = '<html><body>Content</body></html>';

      const result = cleanHtml(originalHtml);
      
      // Should return processed HTML or original if error
      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(0);
    });
  });

  describe('Current page detection', () => {
    it('should detect page number from URL start parameter', () => {
      const getCurrentPage = (parser as any).getCurrentPage.bind(parser);
      
      const mockPage = {
        url: () => 'https://www.google.com/search?q=test&start=20'
      };

      const pageNum = getCurrentPage(mockPage);
      expect(pageNum).toBe(3); // start=20 means page 3 (20/10 + 1)
    });

    it('should default to page 1 if no start parameter', () => {
      const getCurrentPage = (parser as any).getCurrentPage.bind(parser);
      
      const mockPage = {
        url: () => 'https://www.google.com/search?q=test'
      };

      const pageNum = getCurrentPage(mockPage);
      expect(pageNum).toBe(1);
    });

    it('should handle malformed URLs', () => {
      const getCurrentPage = (parser as any).getCurrentPage.bind(parser);
      
      const mockPage = {
        url: () => 'invalid-url'
      };

      const pageNum = getCurrentPage(mockPage);
      expect(pageNum).toBeUndefined();
    });
  });

  describe('Domain extraction', () => {
    it('should extract domain from URL', () => {
      const extractDomain = (parser as any).extractDomain.bind(parser);

      expect(extractDomain('https://www.example.com/path')).toBe('www.example.com');
      expect(extractDomain('http://subdomain.example.org')).toBe('subdomain.example.org');
      expect(extractDomain('https://example.co.uk/path?query=1')).toBe('example.co.uk');
    });

    it('should handle invalid URLs', () => {
      const extractDomain = (parser as any).extractDomain.bind(parser);

      expect(extractDomain('invalid-url')).toBe('unknown');
      expect(extractDomain('')).toBe('unknown');
      expect(extractDomain('not-a-url')).toBe('unknown');
    });
  });

  describe('Google internal link detection', () => {
    it('should identify Google internal links', () => {
      const isGoogleInternal = (parser as any).isGoogleInternalLink.bind(parser);

      expect(isGoogleInternal('https://accounts.google.com/login')).toBe(true);
      expect(isGoogleInternal('https://support.google.com/help')).toBe(true);
      expect(isGoogleInternal('https://policies.google.com/terms')).toBe(true);
      expect(isGoogleInternal('https://google.com/preferences')).toBe(true);
    });

    it('should not identify external links as Google internal', () => {
      const isGoogleInternal = (parser as any).isGoogleInternalLink.bind(parser);

      expect(isGoogleInternal('https://www.example.com')).toBe(false);
      expect(isGoogleInternal('https://facebook.com')).toBe(false);
      expect(isGoogleInternal('https://microsoft.com/support')).toBe(false);
    });
  });

  describe('Result type determination', () => {
    it('should determine ad type correctly', () => {
      const determineResultType = (parser as any).determineResultType.bind(parser);
      
      const mockContainer = {}; // Not used in current implementation
      
      expect(determineResultType(mockContainer, true)).toBe('ad');
      expect(determineResultType(mockContainer, false)).toBe('organic');
    });
  });

  describe('Selector configurations', () => {
    it('should have proper selector configurations', () => {
      const selectors = (parser as any).selectors;

      expect(Array.isArray(selectors.resultContainer)).toBe(true);
      expect(Array.isArray(selectors.title)).toBe(true);
      expect(Array.isArray(selectors.url)).toBe(true);
      expect(Array.isArray(selectors.snippet)).toBe(true);
      expect(Array.isArray(selectors.adIndicator)).toBe(true);
      expect(Array.isArray(selectors.nextPageButton)).toBe(true);

      // Should have multiple fallback selectors
      expect(selectors.resultContainer.length).toBeGreaterThan(1);
      expect(selectors.title.length).toBeGreaterThan(1);
      expect(selectors.adIndicator.length).toBeGreaterThan(1);
    });

    it('should have reasonable selector values', () => {
      const selectors = (parser as any).selectors;

      // Check that selectors look like CSS selectors
      expect(selectors.resultContainer[0]).toMatch(/^[.#\[]/);
      expect(selectors.title[0]).toMatch(/^[.#\[a-zA-Z]/);
      expect(selectors.adIndicator[0]).toMatch(/^\[.*\]$/);
    });
  });

  describe('Error handling', () => {
    it('should handle metadata extraction errors', async () => {
      const extractMetadata = (parser as any).extractMetadata.bind(parser);
      
      const mockContainer = {
        textContent: jest.fn().mockRejectedValue(new Error('DOM error'))
      };

      const metadata = await extractMetadata(mockContainer, 'https://example.com');
      
      expect(metadata).toEqual({
        domain: 'example.com',
        breadcrumbs: undefined,
        sitelinks: undefined
      });
    });

    it('should handle URL parsing errors in metadata', async () => {
      const extractMetadata = (parser as any).extractMetadata.bind(parser);
      
      const mockContainer = {
        textContent: jest.fn().mockResolvedValue('')
      };

      const metadata = await extractMetadata(mockContainer, 'invalid-url');
      
      expect(metadata).toEqual({
        domain: 'unknown',
        breadcrumbs: undefined,
        sitelinks: undefined
      });
    });
  });
});