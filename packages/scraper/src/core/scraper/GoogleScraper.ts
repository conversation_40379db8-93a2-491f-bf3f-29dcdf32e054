import type { <PERSON> } from 'patchright';
import { <PERSON>rowserManager } from '../browser/BrowserManager';
import { SearchResultParser } from './SearchResultParser';
import type { 
  SearchOptions, 
  SearchResponse, 
  SearchParams, 
  LegacyProxyConfig, 
  ProxyInfo, 
  ScraperConfig 
} from '../../types';
import { retry, CircuitBreaker } from '../../utils/retry';
import { RateLimiter, randomDelay } from '../../utils/delay';
import { logger } from '../../utils/logger';

export class GoogleScraper {
  private readonly browserManager: BrowserManager;
  private readonly parser: SearchResultParser;
  private readonly rateLimiter: RateLimiter;
  private readonly circuitBreaker: CircuitBreaker;
  private readonly config: ScraperConfig;

  constructor(config: ScraperConfig) {
    this.config = config;
    this.browserManager = new BrowserManager(config.browser);
    this.parser = new SearchResultParser();
    
    // Rate limiting: convert requests per hour to requests per hour window
    this.rateLimiter = new RateLimiter(
      config.scraping.requestsPerHour,
      60 * 60 * 1000 // 1 hour in milliseconds
    );
    
    this.circuitBreaker = new CircuitBreaker(5, 60000); // 5 failures, 1 minute recovery
  }

  async initialize(): Promise<void> {
    await this.browserManager.launch();
    logger.info('GoogleScraper initialized successfully');
  }

  async search(options: SearchOptions, proxy?: ProxyInfo | LegacyProxyConfig): Promise<SearchResponse> {
    await this.rateLimiter.waitIfNeeded();
    
    return this.circuitBreaker.execute(async () => {
      return this.performSearch(options, proxy);
    });
  }

  private async performSearch(options: SearchOptions, proxy?: ProxyInfo | LegacyProxyConfig): Promise<SearchResponse> {
    const contextId = await this.browserManager.createContext(proxy);
    let page: Page | null = null;

    try {
      page = await this.browserManager.getPage(contextId);
      
      const searchUrl = this.buildSearchUrl(options);
      logger.info(`Searching for: "${options.query}" at ${searchUrl}`);

      // Navigate with retry logic
      await retry(
        async () => {
          if (!page) throw new Error('Page is null');
          await page.goto(searchUrl, { 
            waitUntil: 'networkidle',
            timeout: 30000 
          });
        },
        {
          maxAttempts: this.config.scraping.maxRetries,
          baseDelay: this.config.scraping.retryDelay,
          maxDelay: 30000,
          retryCondition: (error) => {
            // Retry on network errors but not on access denied
            return !error.message.includes('403') && !error.message.includes('blocked');
          }
        }
      );

      // Check for captcha
      const hasCaptcha = await this.detectCaptcha(page);
      if (hasCaptcha) {
        logger.warn('Captcha detected - this would need captcha solving integration');
        throw new Error('Captcha detected');
      }

      // Wait for results to load
      await this.waitForResults(page);

      // Simulate human behavior
      await this.browserManager.simulateHumanBehavior(page);

      // Parse results
      const results = await this.parser.parse(page, options.query);
      
      // Add delay between requests
      const [minDelay, maxDelay] = this.config.scraping.delayBetweenRequests;
      await randomDelay(minDelay, maxDelay);

      logger.info(`Successfully scraped ${results.results.length} results for query: "${options.query}"`);
      return results;

    } catch (error) {
      logger.error(`Failed to scrape query "${options.query}":`, error);
      throw error;
    } finally {
      if (page) {
        await page.close();
      }
      await this.browserManager.closeContext(contextId);
    }
  }

  private buildSearchUrl(options: SearchOptions): string {
    const params: SearchParams = {
      q: options.query
    };

    if (options.page && options.page > 1) {
      params.start = (options.page - 1) * (options.resultsPerPage || 10);
    }

    if (options.resultsPerPage) {
      params.num = Math.min(options.resultsPerPage, 20); // Google limits to ~20
    }

    if (options.language) {
      params.hl = options.language;
    }

    if (options.country) {
      params.gl = options.country;
    }

    if (options.safeSearch) {
      params.safe = options.safeSearch === 'on' ? 'active' : 'off';
    }

    const searchParams = new URLSearchParams();
    for (const [key, value] of Object.entries(params)) {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    }

    return `https://www.google.com/search?${searchParams.toString()}`;
  }

  private async detectCaptcha(page: Page): Promise<boolean> {
    try {
      // Check for common captcha indicators
      const captchaSelectors = [
        '#captcha-form',
        '.g-recaptcha',
        '[data-recaptcha]',
        '#recaptcha',
        '.captcha-container',
        'iframe[src*="recaptcha"]'
      ];

      for (const selector of captchaSelectors) {
        const element = await page.$(selector);
        if (element) {
          return true;
        }
      }

      // Check for access denied messages
      const bodyText = await page.textContent('body');
      if (bodyText?.includes('automated queries') || 
          bodyText?.includes('unusual traffic') ||
          bodyText?.includes('verify you are human')) {
        return true;
      }

      return false;
    } catch (error) {
      logger.warn('Error detecting captcha:', error);
      return false;
    }
  }

  private async waitForResults(page: Page): Promise<void> {
    try {
      // Wait for either search results or error indicators
      await Promise.race([
        page.waitForSelector('.g, [data-sokoban-container]', { timeout: 10000 }),
        page.waitForSelector('#captcha-form', { timeout: 10000 }),
        page.waitForSelector('.error-page', { timeout: 10000 })
      ]);

      // Additional wait for dynamic content
      await randomDelay(1000, 3000);
    } catch (error) {
      logger.warn('Timeout waiting for results, proceeding anyway');
    }
  }

  async searchMultiplePages(
    options: SearchOptions, 
    maxPages = 3,
    proxy?: ProxyInfo | LegacyProxyConfig
  ): Promise<SearchResponse[]> {
    const results: SearchResponse[] = [];
    
    for (let page = 1; page <= maxPages; page++) {
      try {
        const pageOptions = { ...options, page };
        const result = await this.search(pageOptions, proxy);
        results.push(result);
        
        if (!result.hasNextPage) {
          logger.info(`No more pages available after page ${page}`);
          break;
        }
        
        // Longer delay between pages
        const [minDelay, maxDelay] = this.config.scraping.delayBetweenRequests;
        await randomDelay(minDelay * 2, maxDelay * 2);
        
      } catch (error) {
        logger.error(`Failed to scrape page ${page}:`, error);
        break;
      }
    }
    
    return results;
  }

  async close(): Promise<void> {
    await this.browserManager.close();
    logger.info('GoogleScraper closed');
  }

  getStats(): {
    contextCount: number;
    circuitBreakerState: string;
  } {
    return {
      contextCount: this.browserManager.getContextCount(),
      circuitBreakerState: this.circuitBreaker.getState()
    };
  }
}