import type { Page, Locator } from 'patchright';
import type { SearchResult, SearchResponse, ParserSelectors, LinkDetail } from '../../types';
import { logger } from '../../utils/logger';

export class SearchResultParser {
  private readonly selectors: ParserSelectors = {
    resultContainer: [
      '.g',
      '[data-sokoban-container]',
      '.rc',
      '.g .tF2Cxc',
      '.kvH3mc'
    ],
    title: [
      '.LC20lb',
      'h3',
      '.DKV0Md',
      '.LC20lb.DKV0Md',
      'a h3'
    ],
    url: [
      '.yuRUbf > a',
      '.yuRUbf a',
      'a[href]',
      '.r > a',
      '.kCrYT > a'
    ],
    snippet: [
      '.VwiC3b',
      '.aCOpRe',
      '.s',
      '.st',
      '.IsZvec'
    ],
    adIndicator: [
      '[data-text-ad]',
      '.ads-ad',
      '.ad_cclk',
      '.commercial-unit-desktop-top',
      '.pla-unit'
    ],
    nextPageButton: [
      'a[aria-label="Next"]',
      '#pnnext',
      'a[id="pnnext"]',
      '.nBDE1b.G5eFlf'
    ]
  };

  async parse(page: Page, query: string): Promise<SearchResponse> {
    try {
      const results: SearchResult[] = [];
      const timestamp = new Date();

      // Get result containers
      const containers = await this.getElements(page, this.selectors.resultContainer);
      
      logger.info(`Found ${containers.length} result containers`);

      let position = 1;
      for (const container of containers) {
        try {
          const result = await this.parseResultContainer(container, position);
          if (result) {
            results.push(result);
            position++;
          }
        } catch (error) {
          logger.warn(`Failed to parse result container ${position}:`, error);
        }
      }

      // Check for next page
      const hasNextPage = await this.hasNextPage(page);

      // Get total results (if available)
      const totalResults = await this.getTotalResults(page);

      // Get related queries
      const relatedQueries = await this.getRelatedQueries(page);

      // Count total ads on the page
      const adsCount = await this.countAdsOnPage(page);

      // Count total links on the page
      const totalLinksCount = await this.countLinksOnPage(page);

      // Cache HTML content of the page
      const htmlCache = await this.getPageHtmlCache(page);

      // Extract detailed link arrays
      const { adsLinks, organicLinks, otherLinks } = await this.extractDetailedLinks(page, results);

      return {
        query,
        totalResults: totalResults || undefined,
        results,
        relatedQueries: relatedQueries.length > 0 ? relatedQueries : undefined,
        timestamp,
        page: this.getCurrentPage(page) || 1,
        hasNextPage,
        // Core metrics required by the project
        adsCount,
        totalLinksCount,
        htmlCache,
        // Enhanced link arrays for detailed analysis
        adsLinks,
        organicLinks,
        otherLinks
      };

    } catch (error) {
      logger.error('Failed to parse search results:', error);
      throw error;
    }
  }

  private async parseResultContainer(container: Locator, position: number): Promise<SearchResult | null> {
    try {
      // Check if this is an ad
      const isAd = await this.isAdvertisement(container);

      // Extract title
      const title = await this.extractText(container, this.selectors.title);
      if (!title) {
        return null; // Skip if no title found
      }

      // Extract URL
      const url = await this.extractUrl(container, this.selectors.url);
      if (!url) {
        return null; // Skip if no URL found
      }

      // Extract snippet
      const snippet = await this.extractText(container, this.selectors.snippet) || '';

      // Determine result type
      const type = this.determineResultType(container, isAd);

      // Extract metadata
      const metadata = await this.extractMetadata(container, url);

      return {
        title,
        url,
        snippet,
        position,
        type,
        metadata: metadata || undefined
      };

    } catch (error) {
      logger.warn(`Failed to parse result at position ${position}:`, error);
      return null;
    }
  }

  private async extractText(container: Locator, selectors: string[]): Promise<string | null> {
    for (const selector of selectors) {
      try {
        const element = container.locator(selector).first();
        const text = await element.textContent({ timeout: 1000 });
        if (text && text.trim()) {
          return text.trim();
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    return null;
  }

  private async extractUrl(container: Locator, selectors: string[]): Promise<string | null> {
    for (const selector of selectors) {
      try {
        const element = container.locator(selector).first();
        const href = await element.getAttribute('href', { timeout: 1000 });
        if (href) {
          // Clean up Google redirect URLs
          return this.cleanUrl(href);
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    return null;
  }

  private cleanUrl(url: string): string {
    // Remove Google redirect parameters
    if (url.startsWith('/url?')) {
      const params = new URLSearchParams(url.substring(5));
      const actualUrl = params.get('q') || params.get('url');
      if (actualUrl) {
        return actualUrl;
      }
    }
    
    if (url.startsWith('http')) {
      return url;
    }
    
    return url;
  }

  private async isAdvertisement(container: Locator): Promise<boolean> {
    for (const selector of this.selectors.adIndicator) {
      try {
        const adElement = container.locator(selector).first();
        const isVisible = await adElement.isVisible({ timeout: 500 });
        if (isVisible) {
          return true;
        }
      } catch (error) {
        // Continue checking
      }
    }

    // Check for ad text indicators
    try {
      const text = await container.textContent();
      if (text?.toLowerCase().includes('ad') || text?.toLowerCase().includes('sponsored')) {
        return true;
      }
    } catch (error) {
      // Ignore
    }

    return false;
  }

  private determineResultType(container: Locator, isAd: boolean): SearchResult['type'] {
    if (isAd) {
      return 'ad';
    }

    // Could add logic to detect featured snippets, people also ask, etc.
    return 'organic';
  }

  private async extractMetadata(container: Locator, url: string): Promise<SearchResult['metadata']> {
    try {
      const domain = new URL(url).hostname;
      
      // Extract breadcrumbs if available
      const breadcrumbs = await this.extractBreadcrumbs(container);
      
      // Extract sitelinks if available
      const sitelinks = await this.extractSitelinks(container);

      return {
        domain,
        breadcrumbs: breadcrumbs.length > 0 ? breadcrumbs : undefined,
        sitelinks: sitelinks.length > 0 ? sitelinks : undefined
      };
    } catch (error) {
      return {
        domain: 'unknown',
        breadcrumbs: undefined,
        sitelinks: undefined
      };
    }
  }

  private async extractBreadcrumbs(container: Locator): Promise<string[]> {
    const breadcrumbSelectors = ['.tjvcx', '.eIIj3e', '.AP7Wnd'];
    
    for (const selector of breadcrumbSelectors) {
      try {
        const elements = container.locator(selector);
        const count = await elements.count();
        const breadcrumbs: string[] = [];
        
        for (let i = 0; i < count; i++) {
          const text = await elements.nth(i).textContent();
          if (text && text.trim()) {
            breadcrumbs.push(text.trim());
          }
        }
        
        if (breadcrumbs.length > 0) {
          return breadcrumbs;
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    
    return [];
  }

  private async extractSitelinks(container: Locator): Promise<string[]> {
    const sitelinkSelectors = ['.sVXRqc', '.oewGkc'];
    
    for (const selector of sitelinkSelectors) {
      try {
        const elements = container.locator(selector);
        const count = await elements.count();
        const sitelinks: string[] = [];
        
        for (let i = 0; i < count; i++) {
          const text = await elements.nth(i).textContent();
          if (text && text.trim()) {
            sitelinks.push(text.trim());
          }
        }
        
        if (sitelinks.length > 0) {
          return sitelinks;
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    
    return [];
  }

  private async getElements(page: Page, selectors: string[]): Promise<Locator[]> {
    for (const selector of selectors) {
      try {
        const elements = page.locator(selector);
        const count = await elements.count();
        if (count > 0) {
          const locators: Locator[] = [];
          for (let i = 0; i < count; i++) {
            locators.push(elements.nth(i));
          }
          return locators;
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    return [];
  }

  private async hasNextPage(page: Page): Promise<boolean> {
    for (const selector of this.selectors.nextPageButton) {
      try {
        const nextButton = page.locator(selector).first();
        const isVisible = await nextButton.isVisible({ timeout: 1000 });
        if (isVisible) {
          return true;
        }
      } catch (error) {
        // Continue checking
      }
    }
    return false;
  }

  private async getTotalResults(page: Page): Promise<number | undefined> {
    const resultStatsSelectors = ['#result-stats', '.LHJvCe'];
    
    for (const selector of resultStatsSelectors) {
      try {
        const element = page.locator(selector).first();
        const text = await element.textContent({ timeout: 1000 });
        if (text) {
          const match = text.match(/[\d,]+/);
          if (match) {
            return Number.parseInt(match[0].replace(/,/g, ''), 10);
          }
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    
    return undefined;
  }

  private async getRelatedQueries(page: Page): Promise<string[]> {
    const relatedSelectors = ['.k8XOCe', '.BNeawe.s3v9rd.AP7Wnd'];
    
    for (const selector of relatedSelectors) {
      try {
        const elements = page.locator(selector);
        const count = await elements.count();
        const queries: string[] = [];
        
        for (let i = 0; i < count; i++) {
          const text = await elements.nth(i).textContent();
          if (text && text.trim()) {
            queries.push(text.trim());
          }
        }
        
        if (queries.length > 0) {
          return queries;
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    
    return [];
  }

  private getCurrentPage(page: Page): number | undefined {
    try {
      const url = page.url();
      const urlParams = new URLSearchParams(new URL(url).search);
      const start = urlParams.get('start');
      
      if (start) {
        return Math.floor(Number.parseInt(start, 10) / 10) + 1;
      }
      
      return 1;
    } catch (error) {
      return undefined;
    }
  }

  /**
   * Count total number of Google Ads advertisers on the page
   */
  private async countAdsOnPage(page: Page): Promise<number> {
    try {
      const adCounts = {
        bySelector: 0,
        byText: 0,
        byPosition: 0,
        byStructure: 0
      };

      // Method 1: Extended ad selectors for comprehensive detection
      const adSelectors = [
        // Top ads section
        '[data-text-ad]',
        '.ads-ad',
        '.ad_cclk',
        '.commercial-unit-desktop-top',
        '.uEierd', // Modern Google ads container
        '.mnr-c', // Shopping ads container
        
        // Shopping ads
        '.pla-unit',
        '.pla-unit-container', 
        '.pla-unit-title',
        '.pla-unit-single-item-container',
        '.shopping-unit',
        '.sh-np__click-target', // New shopping ad format
        '.pla-unit-container-border-color', // Shopping ads with border
        
        // Text ads
        '.ads-fr',
        '.ads-visurl',
        '.cu-container',
        
        // Video ads
        '.videobox',
        '.video-thumbnail-ads',
        
        // Local ads
        '.local-kno-sponsored',
        '.local-sponsored-content',
        
        // Knowledge panel ads
        '.kno-sponsored',
        
        // Hotel/travel ads
        '.commercial-unit-desktop-rhs',
        '.OhScic', // Hotel ads
        
        // App install ads
        '.app-install-ad',
        
        // Job ads
        '.job-sponsored',
        
        // Modern sponsored content
        '.VqFMTc', // Sponsored tag
        '.rGhul', // Ad label container
        '.commercial-unit-desktop-top .g', // Ads within commercial unit
        '.mnr-c .g', // Shopping results within ad container
      ];

      // Count unique ad containers
      const uniqueAdContainers = new Set<string>();

      for (const selector of adSelectors) {
        try {
          const elements = page.locator(selector);
          const count = await elements.count();
          
          for (let i = 0; i < count; i++) {
            const element = elements.nth(i);
            // Get unique identifier for this ad to avoid double counting
            const boundingBox = await element.boundingBox().catch(() => null);
            if (boundingBox) {
              const key = `${Math.round(boundingBox.x)},${Math.round(boundingBox.y)},${Math.round(boundingBox.width)},${Math.round(boundingBox.height)}`;
              uniqueAdContainers.add(key);
            }
          }
        } catch (error) {
          // Continue with next selector
        }
      }

      adCounts.bySelector = uniqueAdContainers.size;

      // Method 2: Enhanced text-based detection
      try {
        const textBasedAds = await page.evaluate(() => {
          const adTextPatterns = [
            /\bsponsored\b/i,
            /\badvertisement\b/i,
            /\bpromo\b/i,
            /^ad$/i,
            /^ads$/i
          ];

          const excludePatterns = [
            /address/i,
            /added/i,
            /advanced/i,
            /advantage/i,
            /advisor/i,
            /adventure/i,
            /adequate/i
          ];

          const adElements = document.querySelectorAll('*');
          const foundAds = new Set();

          for (const element of adElements) {
            const text = element.textContent?.trim() || '';
            const hasAdText = adTextPatterns.some(pattern => pattern.test(text));
            const hasExclude = excludePatterns.some(pattern => pattern.test(text));

            if (hasAdText && !hasExclude && text.length < 50) {
              // Check if this is likely an ad label
              const rect = element.getBoundingClientRect();
              if (rect.width > 0 && rect.height > 0) {
                // Find the parent container that likely contains the ad
                let adContainer = element;
                let levels = 0;
                while (adContainer.parentElement && levels < 5) {
                  adContainer = adContainer.parentElement;
                  const containerRect = adContainer.getBoundingClientRect();
                  if (containerRect.height > 100 && containerRect.width > 200) {
                    break;
                  }
                  levels++;
                }

                const containerKey = `${Math.round(rect.x)},${Math.round(rect.y)}`;
                foundAds.add(containerKey);
              }
            }
          }

          return foundAds.size;
        });

        adCounts.byText = textBasedAds;
      } catch (error) {
        logger.debug('Failed to count ads by text content:', error);
      }

      // Method 3: Position-based detection (top and bottom ad areas)
      try {
        const positionBasedAds = await page.evaluate(() => {
          const topAdArea = document.querySelector('#tvcap, #tads, #res .g:first-child');
          const bottomAdArea = document.querySelector('#bottomads, #rso .g:last-child');
          let count = 0;

          // Check top ads area
          if (topAdArea) {
            const topAds = topAdArea.querySelectorAll('.g, .pla-unit, [data-text-ad]');
            count += topAds.length;
          }

          // Check bottom ads area  
          if (bottomAdArea) {
            const bottomAds = bottomAdArea.querySelectorAll('.g, .pla-unit, [data-text-ad]');
            count += bottomAds.length;
          }

          return count;
        });

        adCounts.byPosition = positionBasedAds;
      } catch (error) {
        logger.debug('Failed to count ads by position:', error);
      }

      // Method 4: Structure-based detection (ads have different DOM structure)
      try {
        const structureBasedAds = await page.evaluate(() => {
          // Look for elements with ad-specific attributes and structure
          const adAttributes = [
            '[data-text-ad]',
            '[data-pla-unit]',
            '[data-commercial-unit]',
            '.g:has(.ads-visurl)',
            '.g:has(.ad_cclk)',
            '.g[data-hveid]:has(.VqFMTc)', // Modern ad format
          ];

          let count = 0;
          for (const selector of adAttributes) {
            try {
              const elements = document.querySelectorAll(selector);
              count += elements.length;
            } catch (e) {
              // Continue with next selector
            }
          }

          return count;
        });

        adCounts.byStructure = structureBasedAds;
      } catch (error) {
        logger.debug('Failed to count ads by structure:', error);
      }

      // Use the highest count from our detection methods (most comprehensive)
      const totalAds = Math.max(
        adCounts.bySelector,
        adCounts.byText,
        adCounts.byPosition,
        adCounts.byStructure
      );

      // Fallback to sum if individual methods found different ads
      const sumOfMethods = Object.values(adCounts).reduce((sum, count) => sum + count, 0);
      const finalCount = totalAds > 0 ? totalAds : Math.min(sumOfMethods, 20); // Cap at reasonable limit

      logger.info(`Found ${finalCount} ads on the page`, {
        bySelector: adCounts.bySelector,
        byText: adCounts.byText,
        byPosition: adCounts.byPosition,
        byStructure: adCounts.byStructure,
        final: finalCount
      });

      return finalCount;

    } catch (error) {
      logger.error('Failed to count ads on page:', error);
      return 0;
    }
  }

  /**
   * Count total number of links on the page
   */
  private async countLinksOnPage(page: Page): Promise<number> {
    try {
      const linkCount = await page.locator('a[href]').count();
      logger.info(`Found ${linkCount} total links on the page`);
      return linkCount;
    } catch (error) {
      logger.error('Failed to count links on page:', error);
      return 0;
    }
  }

  /**
   * Get HTML cache of the page with compression and cleaning
   */
  private async getPageHtmlCache(page: Page): Promise<string> {
    try {
      let htmlContent = await page.content();
      const originalSize = htmlContent.length;

      // Clean up HTML to reduce size while preserving important content
      htmlContent = this.cleanHtmlForStorage(htmlContent);
      
      // Compress HTML using simple techniques (would use zlib in production)
      htmlContent = this.compressHtml(htmlContent);

      const finalSize = htmlContent.length;
      const compressionRatio = ((originalSize - finalSize) / originalSize * 100).toFixed(1);

      logger.info(`Cached and compressed HTML content`, {
        originalSize,
        finalSize,
        compressionRatio: `${compressionRatio}%`,
        savedBytes: originalSize - finalSize
      });

      return htmlContent;
    } catch (error) {
      logger.error('Failed to get page HTML cache:', error);
      return '';
    }
  }

  /**
   * Clean HTML content to remove unnecessary elements while preserving structure
   */
  private cleanHtmlForStorage(html: string): string {
    try {
      // Remove scripts, styles, comments, and other non-essential content
      let cleanedHtml = html
        // Remove script tags and content
        .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
        // Remove style tags and content  
        .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
        // Remove HTML comments
        .replace(/<!--[\s\S]*?-->/g, '')
        // Remove inline styles (keep some for layout detection)
        .replace(/\sstyle="[^"]*"/gi, '')
        // Remove event handlers
        .replace(/\son\w+="[^"]*"/gi, '')
        // Remove data attributes we don't need
        .replace(/\sdata-ved="[^"]*"/gi, '')
        .replace(/\sdata-async-context="[^"]*"/gi, '')
        .replace(/\sdata-async-type="[^"]*"/gi, '')
        // Clean up excessive whitespace
        .replace(/\s+/g, ' ')
        .replace(/>\s+</g, '><')
        .trim();

      return cleanedHtml;
    } catch (error) {
      logger.warn('Failed to clean HTML, returning original:', error);
      return html;
    }
  }

  /**
   * Apply basic compression techniques to HTML
   */
  private compressHtml(html: string): string {
    try {
      // Basic compression techniques
      let compressed = html
        // Remove extra spaces between tags
        .replace(/>\s+</g, '><')
        // Remove spaces around = in attributes
        .replace(/\s*=\s*/g, '=')
        // Minimize whitespace in text content (but preserve line breaks in search results)
        .replace(/\s{2,}/g, ' ')
        // Remove trailing spaces
        .replace(/\s+$/gm, '')
        // Remove empty lines
        .replace(/^\s*[\r\n]/gm, '');

      return compressed.trim();
    } catch (error) {
      logger.warn('Failed to compress HTML, returning original:', error);
      return html;
    }
  }

  /**
   * Extract detailed links arrays categorized by type
   */
  private async extractDetailedLinks(page: Page, searchResults: SearchResult[]): Promise<{
    adsLinks: LinkDetail[];
    organicLinks: LinkDetail[];
    otherLinks: LinkDetail[];
  }> {
    try {
      const adsLinks: LinkDetail[] = [];
      const organicLinks: LinkDetail[] = [];
      const otherLinks: LinkDetail[] = [];

      // Get all links on the page
      const allLinks = page.locator('a[href]');
      const linkCount = await allLinks.count();

      logger.info(`Processing ${linkCount} links for detailed categorization`);

      for (let i = 0; i < linkCount; i++) {
        try {
          const link = allLinks.nth(i);
          const href = await link.getAttribute('href');
          const text = await link.textContent() || '';
          
          if (!href || href.startsWith('#') || href.startsWith('javascript:')) {
            continue; // Skip invalid/anchor links
          }

          const cleanedUrl = this.cleanUrl(href);
          const domain = this.extractDomain(cleanedUrl);
          
          // Skip Google internal links
          if (this.isGoogleInternalLink(cleanedUrl)) {
            continue;
          }

          const linkDetail: LinkDetail = {
            url: cleanedUrl,
            title: text.trim(),
            position: i + 1,
            type: 'other', // Default, will be updated below
            domain,
            anchorText: text.trim()
          };

          // Categorize the link
          const linkType = await this.categorizeLinkType(link, cleanedUrl, searchResults);
          linkDetail.type = linkType;

          // Add to appropriate array
          switch (linkType) {
            case 'ad':
              adsLinks.push(linkDetail);
              break;
            case 'organic':
              organicLinks.push(linkDetail);
              break;
            default:
              otherLinks.push(linkDetail);
              break;
          }

        } catch (error) {
          logger.debug(`Failed to process link ${i}:`, error);
        }
      }

      logger.info(`Categorized links: ${adsLinks.length} ads, ${organicLinks.length} organic, ${otherLinks.length} other`);

      return { adsLinks, organicLinks, otherLinks };

    } catch (error) {
      logger.error('Failed to extract detailed links:', error);
      return { adsLinks: [], organicLinks: [], otherLinks: [] };
    }
  }

  /**
   * Categorize a link by its type based on context and search results
   */
  private async categorizeLinkType(
    link: Locator, 
    url: string, 
    searchResults: SearchResult[]
  ): Promise<LinkDetail['type']> {
    try {
      // Check if this link is from an ad container
      const isInAdContainer = await this.isLinkInAdContainer(link);
      if (isInAdContainer) {
        return 'ad';
      }

      // Check if this link matches any of our parsed search results
      const matchingResult = searchResults.find(result => result.url === url);
      if (matchingResult) {
        return matchingResult.type === 'ad' ? 'ad' : 'organic';
      }

      // Check if it's in main search results area
      const isInSearchResults = await this.isLinkInSearchResults(link);
      if (isInSearchResults) {
        return 'organic';
      }

      // Check if it's navigation/footer
      const isNavigation = await this.isNavigationLink(link);
      if (isNavigation) {
        return 'navigation';
      }

      const isFooter = await this.isFooterLink(link);
      if (isFooter) {
        return 'footer';
      }

      return 'other';

    } catch (error) {
      return 'other';
    }
  }

  /**
   * Check if link is within an ad container
   */
  private async isLinkInAdContainer(link: Locator): Promise<boolean> {
    try {
      const adContainerSelectors = [
        '[data-text-ad]',
        '.ads-ad',
        '.ad_cclk',
        '.commercial-unit-desktop-top',
        '.pla-unit',
        '.shopping-unit',
        '.cu-container'
      ];

      for (const selector of adContainerSelectors) {
        const adContainer = link.locator(`xpath=ancestor::*[contains(@class, "${selector.replace('.', '')}") or contains(@data-text-ad, "")]`);
        const count = await adContainer.count();
        if (count > 0) {
          return true;
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if link is within main search results area
   */
  private async isLinkInSearchResults(link: Locator): Promise<boolean> {
    try {
      const searchResultSelectors = ['.g', '[data-sokoban-container]', '.rc', '.tF2Cxc'];
      
      for (const selector of searchResultSelectors) {
        const resultContainer = link.locator(`xpath=ancestor::*[contains(@class, "${selector.replace('.', '')}")]`);
        const count = await resultContainer.count();
        if (count > 0) {
          return true;
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if link is navigation
   */
  private async isNavigationLink(link: Locator): Promise<boolean> {
    try {
      const navSelectors = ['nav', '.hdtb-mitem', '.hdtb-mn-hd', '#top_nav'];
      
      for (const selector of navSelectors) {
        const navContainer = link.locator(`xpath=ancestor::*[self::nav or contains(@class, "${selector.replace('.', '').replace('#', '')}")]`);
        const count = await navContainer.count();
        if (count > 0) {
          return true;
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if link is in footer
   */
  private async isFooterLink(link: Locator): Promise<boolean> {
    try {
      const footerContainer = link.locator('xpath=ancestor::*[self::footer or contains(@class, "footer") or contains(@id, "footer")]');
      const count = await footerContainer.count();
      return count > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Extract domain from URL
   */
  private extractDomain(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      return 'unknown';
    }
  }

  /**
   * Check if URL is Google internal link
   */
  private isGoogleInternalLink(url: string): boolean {
    const googlePatterns = [
      'accounts.google.com',
      'support.google.com',
      'policies.google.com',
      'google.com/preferences',
      'google.com/advanced_search',
      'google.com/history',
      'google.com/webhp'
    ];

    return googlePatterns.some(pattern => url.includes(pattern));
  }
}