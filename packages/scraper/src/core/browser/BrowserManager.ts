import type { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontext, Page, LaunchOptions } from 'patchright';
import { StealthConfig } from './StealthConfig';
import type { 
  BrowserConfig, 
  BrowserContext as CustomBrowserContext, 
  LegacyProxyConfig, 
  ProxyInfo 
} from '../../types';
import { randomDelay, humanLikeDelay } from '../../utils/delay';
import { logger } from '../../utils/logger';
import { v4 as uuidv4 } from 'uuid';

export class BrowserManager {
  private browser: Browser | null = null;
  private contexts: Map<string, { context: BrowserContext; metadata: CustomBrowserContext }> = new Map();
  private readonly maxContexts: number;
  private readonly config: BrowserConfig;

  constructor(config: BrowserConfig, maxContexts = 5) {
    this.config = config;
    this.maxContexts = maxContexts;
  }

  async launch(): Promise<void> {
    if (this.browser) {
      await this.close();
    }

    try {
      const chromiumBrowser = StealthConfig.configureStealth();
      
      // Patchright recommended configuration for maximum stealth
      const launchOptions: LaunchOptions = {
        headless: this.config.headless,
        slowMo: this.config.slowMo,
        // Patchright automatically handles stealth args, minimal custom args needed
        args: StealthConfig.getStealthArgs(),
        ...this.config.launchOptions
      };

      this.browser = await chromiumBrowser.launch(launchOptions);
      logger.info('Browser launched successfully with stealth configuration');
    } catch (error) {
      logger.error('Failed to launch browser:', error);
      throw error;
    }
  }

  async createContext(proxy?: ProxyInfo | LegacyProxyConfig): Promise<string> {
    if (!this.browser) {
      throw new Error('Browser not launched. Call launch() first.');
    }

    if (this.contexts.size >= this.maxContexts) {
      await this.cleanupOldestContext();
    }

    const contextId = uuidv4();
    const userAgent = StealthConfig.getRandomUserAgent();
    const viewport = StealthConfig.getRandomViewport();

    try {
      const contextOptions: Parameters<Browser['newContext']>[0] = {
        userAgent,
        viewport,
        ignoreHTTPSErrors: true,
        acceptDownloads: false,
        javaScriptEnabled: true,
        // Patchright specific options for better stealth
        locale: 'en-US',
        timezoneId: 'America/New_York',
        permissions: ['geolocation']
      };

      if (proxy) {
        // Handle both new ProxyInfo and legacy ProxyConfig
        const proxyHost = proxy.host;
        const proxyPort = proxy.port;
        const proxyProtocol = 'protocol' in proxy ? proxy.protocol : 'http';
        
        let proxyCredentials: { username?: string; password?: string } = {};
        
        if ('credentials' in proxy && proxy.credentials) {
          // New ProxyInfo format
          proxyCredentials = proxy.credentials;
        } else if ('username' in proxy && 'password' in proxy) {
          // Legacy ProxyConfig format
          proxyCredentials = {
            username: proxy.username,
            password: proxy.password
          };
        }

        contextOptions.proxy = {
          server: `${proxyProtocol}://${proxyHost}:${proxyPort}`,
          username: proxyCredentials.username,
          password: proxyCredentials.password
        };

        logger.info('Using proxy for browser context', {
          host: proxyHost,
          port: proxyPort,
          protocol: proxyProtocol,
          hasAuth: !!(proxyCredentials.username && proxyCredentials.password)
        });
      }

      const context = await this.browser.newContext(contextOptions);

      // Patchright handles most stealth automatically, but we can add some extra measures
      await context.addInitScript(() => {
        // Additional stealth measures that work with Patchright
        // Randomize canvas fingerprint
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        HTMLCanvasElement.prototype.toDataURL = function(...args) {
          const imageData = originalToDataURL.apply(this, args);
          // Add minimal noise to canvas fingerprint
          return imageData;
        };

        // Randomize WebGL fingerprint slightly
        const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
          if (parameter === 37445) { // UNMASKED_VENDOR_WEBGL
            return 'Intel Inc.';
          }
          if (parameter === 37446) { // UNMASKED_RENDERER_WEBGL  
            return 'Intel Iris OpenGL Engine';
          }
          return originalGetParameter.call(this, parameter);
        };
      });

      const metadata: CustomBrowserContext = {
        id: contextId,
        userAgent,
        viewport,
        proxy,
        createdAt: new Date(),
        lastUsed: new Date()
      };

      this.contexts.set(contextId, { context, metadata });
      logger.info(`Created new browser context: ${contextId}`);
      
      return contextId;
    } catch (error) {
      logger.error('Failed to create browser context:', error);
      throw error;
    }
  }

  async getPage(contextId: string): Promise<Page> {
    const contextData = this.contexts.get(contextId);
    if (!contextData) {
      throw new Error(`Context ${contextId} not found`);
    }

    contextData.metadata.lastUsed = new Date();
    const page = await contextData.context.newPage();

    // Add human-like behavior to page
    await this.addHumanLikeBehavior(page);

    return page;
  }

  private async addHumanLikeBehavior(page: Page): Promise<void> {
    // Random mouse movements
    page.on('load', async () => {
      await randomDelay(1000, 3000);
      const viewport = page.viewportSize();
      if (viewport) {
        await page.mouse.move(
          Math.random() * viewport.width,
          Math.random() * viewport.height
        );
      }
    });

    // Override navigator.webdriver
    await page.addInitScript(() => {
      // biome-ignore lint/suspicious/noExplicitAny: Required for browser API override
      delete (navigator as any).webdriver;
    });
  }

  async simulateHumanBehavior(page: Page): Promise<void> {
    const viewport = page.viewportSize();
    if (!viewport) return;

    // Random scroll
    const scrollY = Math.random() * 500;
    await page.evaluate((y) => window.scrollBy(0, y), scrollY);
    await humanLikeDelay();

    // Random mouse movement
    const x = Math.random() * viewport.width;
    const y = Math.random() * viewport.height;
    await page.mouse.move(x, y);
    await randomDelay(100, 500);
  }

  async closeContext(contextId: string): Promise<void> {
    const contextData = this.contexts.get(contextId);
    if (contextData) {
      await contextData.context.close();
      this.contexts.delete(contextId);
      logger.info(`Closed browser context: ${contextId}`);
    }
  }

  private async cleanupOldestContext(): Promise<void> {
    let oldestContextId: string | null = null;
    let oldestTime = Date.now();

    for (const [id, { metadata }] of this.contexts) {
      if (metadata.lastUsed.getTime() < oldestTime) {
        oldestTime = metadata.lastUsed.getTime();
        oldestContextId = id;
      }
    }

    if (oldestContextId) {
      await this.closeContext(oldestContextId);
    }
  }

  async close(): Promise<void> {
    if (this.browser) {
      for (const contextId of this.contexts.keys()) {
        await this.closeContext(contextId);
      }
      await this.browser.close();
      this.browser = null;
      logger.info('Browser closed successfully');
    }
  }

  getContextCount(): number {
    return this.contexts.size;
  }

  getContextMetadata(contextId: string): CustomBrowserContext | undefined {
    return this.contexts.get(contextId)?.metadata;
  }
}