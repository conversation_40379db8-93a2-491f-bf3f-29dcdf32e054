{"name": "@search-keywords-scraper/types", "version": "0.1.0", "description": "Shared TypeScript type definitions", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "format": "prettier --write src/**/*.ts", "test": "jest"}, "devDependencies": {"typescript": "^5.7.3", "eslint": "^9.18.0", "prettier": "^3.4.2", "@types/jest": "^30.0.0", "jest": "^30.0.0"}, "files": ["dist"], "license": "MIT"}