// User types
export interface User {
  id: string;
  email: string;
  createdAt: Date;
  updatedAt: Date;
}

// Keyword batch types
export interface KeywordBatch {
  id: string;
  userId: string;
  name: string;
  status: BatchStatus;
  totalKeywords: number;
  completedKeywords: number;
  failedKeywords: number;
  createdAt: Date;
  updatedAt: Date;
}

export enum BatchStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// Keyword types
export interface Keyword {
  id: string;
  batchId: string;
  keyword: string;
  status: KeywordStatus;
  retryCount: number;
  searchEngine: SearchEngine;
  createdAt: Date;
  updatedAt: Date;
}

export enum KeywordStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export enum SearchEngine {
  GOOGLE = 'google',
  BING = 'bing'
}

// Search result types
export interface SearchResult {
  id: string;
  keywordId: string;
  totalAds: number;
  totalLinks: number;
  htmlContent: string;
  scrapedAt: Date;
  metadata?: Record<string, any>;
}

// API request/response types
export interface CreateBatchRequest {
  name: string;
  keywords: string[];
  searchEngine?: SearchEngine;
}

export interface CreateBatchResponse {
  batch: KeywordBatch;
}

export interface BatchProgressResponse {
  batch: KeywordBatch;
  keywords: Keyword[];
  results: SearchResult[];
}

// Extended batch response that includes keywords directly
export interface BatchWithKeywords extends KeywordBatch {
  keywords: Keyword[];
}

// Pagination metadata
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Batches list response
export interface BatchesListResponse {
  batches: KeywordBatch[];
  pagination: PaginationMeta;
}

// Job queue types
export interface ScrapingJob {
  keywordId: string;
  keyword: string;
  searchEngine: SearchEngine;
  retryCount: number;
}

export interface ScrapingJobResult {
  success: boolean;
  totalAds: number;
  totalLinks: number;
  htmlContent: string;
  error?: string;
  metadata?: Record<string, any>;
}