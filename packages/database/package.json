{"name": "@search-keywords-scraper/database", "version": "0.1.0", "description": "Drizzle ORM schema and database utilities", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "format": "prettier --write src/**/*.ts", "test": "jest", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "dependencies": {"drizzle-orm": "^0.36.4", "postgres": "^3.4.5", "@search-keywords-scraper/types": "workspace:*"}, "devDependencies": {"drizzle-kit": "^0.30.1", "typescript": "^5.7.3", "eslint": "^9.18.0", "prettier": "^3.4.2", "@types/jest": "^30.0.0", "@types/pg": "^8.11.10", "jest": "^30.0.0"}, "files": ["dist"], "license": "MIT"}