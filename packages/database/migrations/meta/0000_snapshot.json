{"id": "65692678-5a8d-4333-912f-1ca1fceb858f", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.keyword_batches": {"name": "keyword_batches", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "batch_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "total_keywords": {"name": "total_keywords", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "completed_keywords": {"name": "completed_keywords", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "failed_keywords": {"name": "failed_keywords", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"keyword_batches_user_id_users_id_fk": {"name": "keyword_batches_user_id_users_id_fk", "tableFrom": "keyword_batches", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.keywords": {"name": "keywords", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "batch_id": {"name": "batch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "keyword": {"name": "keyword", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "keyword_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "retry_count": {"name": "retry_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "search_engine": {"name": "search_engine", "type": "search_engine", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'google'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"keywords_batch_id_keyword_batches_id_fk": {"name": "keywords_batch_id_keyword_batches_id_fk", "tableFrom": "keywords", "tableTo": "keyword_batches", "columnsFrom": ["batch_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.search_results": {"name": "search_results", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "keyword_id": {"name": "keyword_id", "type": "uuid", "primaryKey": false, "notNull": true}, "total_ads": {"name": "total_ads", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_links": {"name": "total_links", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "html_content": {"name": "html_content", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "scraped_at": {"name": "scraped_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"search_results_keyword_id_keywords_id_fk": {"name": "search_results_keyword_id_keywords_id_fk", "tableFrom": "search_results", "tableTo": "keywords", "columnsFrom": ["keyword_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.batch_status": {"name": "batch_status", "schema": "public", "values": ["pending", "processing", "completed", "failed"]}, "public.keyword_status": {"name": "keyword_status", "schema": "public", "values": ["pending", "processing", "completed", "failed"]}, "public.search_engine": {"name": "search_engine", "schema": "public", "values": ["google", "bing"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}