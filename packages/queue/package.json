{"name": "@search-keywords-scraper/queue", "version": "0.1.0", "description": "Shared queue types and constants for Bull Queue implementation", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"bull": "^4.16.3"}, "devDependencies": {"@types/node": "^22.10.7", "typescript": "^5.7.3"}, "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.js"}}, "license": "MIT"}