import { KeywordScraping<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '../types';

export function validateKeywordScrapingJob(data: any): data is KeywordScrapingJob {
  return (
    typeof data === 'object' &&
    typeof data.keywordId === 'string' &&
    typeof data.batchId === 'string' &&
    typeof data.userId === 'string' &&
    typeof data.keyword === 'string' &&
    ['google', 'bing'].includes(data.searchEngine) &&
    typeof data.retryAttempt === 'number' &&
    data.retryAttempt >= 0
  );
}

export function validateBatchJob(data: any): data is Batch<PERSON>ob {
  return (
    typeof data === 'object' &&
    typeof data.batchId === 'string' &&
    typeof data.userId === 'string' &&
    ['complete', 'failed', 'progress_update'].includes(data.action)
  );
}