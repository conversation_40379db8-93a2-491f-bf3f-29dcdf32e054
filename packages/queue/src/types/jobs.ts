export interface KeywordScrapingJob {
  keywordId: string;
  batchId: string;
  userId: string;
  keyword: string;
  searchEngine: 'google' | 'bing';
  retryAttempt: number;
  priority?: 'high' | 'normal' | 'low';
  metadata?: Record<string, any>;
}

export interface BatchJob {
  batchId: string;
  userId: string;
  action: 'complete' | 'failed' | 'progress_update';
  completedCount?: number;
  failedCount?: number;
  totalCount?: number;
}

export type QueueJobData = KeywordScrapingJob | BatchJob;