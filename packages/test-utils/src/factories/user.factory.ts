import { faker } from '@faker-js/faker';

export interface TestUser {
  id: string;
  email: string;
  createdAt: Date;
  updatedAt: Date;
}

export class UserFactory {
  static create(overrides: Partial<TestUser> = {}): TestUser {
    return {
      id: faker.string.uuid(),
      email: faker.internet.email(),
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  static createMany(count: number, overrides: Partial<TestUser> = {}): TestUser[] {
    return Array.from({ length: count }, () => this.create(overrides));
  }
}