import { faker } from '@faker-js/faker';

export interface TestKeyword {
  id: string;
  batchId: string;
  keyword: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  retryCount: number;
  searchEngine: 'google' | 'bing';
  createdAt: Date;
  updatedAt: Date;
}

export interface TestKeywordBatch {
  id: string;
  userId: string;
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  totalKeywords: number;
  completedKeywords: number;
  failedKeywords: number;
  createdAt: Date;
  updatedAt: Date;
}

export class KeywordFactory {
  static create(overrides: Partial<TestKeyword> = {}): TestKeyword {
    return {
      id: faker.string.uuid(),
      batchId: faker.string.uuid(),
      keyword: faker.lorem.words(2),
      status: 'pending',
      retryCount: 0,
      searchEngine: faker.helpers.arrayElement(['google', 'bing'] as const),
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  static createMany(count: number, overrides: Partial<TestKeyword> = {}): TestKeyword[] {
    return Array.from({ length: count }, () => this.create(overrides));
  }

  static createValidKeywords(count: number = 5): string[] {
    return Array.from({ length: count }, () => 
      faker.lorem.words(faker.number.int({ min: 1, max: 3 }))
    );
  }

  static createInvalidKeywords(): string[] {
    return [
      '', // Empty
      ' '.repeat(501), // Too long
      '   ', // Only whitespace
    ];
  }

  static createCSVContent(keywords: string[]): string {
    return keywords.join('\n');
  }

  static createMockFile(content: string, filename: string = 'test.csv'): File {
    const blob = new Blob([content], { type: 'text/csv' });
    return new File([blob], filename, { type: 'text/csv' });
  }
}

export class BatchFactory {
  static create(overrides: Partial<TestKeywordBatch> = {}): TestKeywordBatch {
    const totalKeywords = faker.number.int({ min: 1, max: 100 });
    const completedKeywords = faker.number.int({ min: 0, max: totalKeywords });
    const failedKeywords = faker.number.int({ min: 0, max: totalKeywords - completedKeywords });
    
    return {
      id: faker.string.uuid(),
      userId: faker.string.uuid(),
      name: faker.company.catchPhrase(),
      status: 'pending',
      totalKeywords,
      completedKeywords,
      failedKeywords,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  static createMany(count: number, overrides: Partial<TestKeywordBatch> = {}): TestKeywordBatch[] {
    return Array.from({ length: count }, () => this.create(overrides));
  }

  static createWithKeywords(
    batchOverrides: Partial<TestKeywordBatch> = {},
    keywordCount: number = 5
  ): { batch: TestKeywordBatch; keywords: TestKeyword[] } {
    const batch = this.create({
      totalKeywords: keywordCount,
      ...batchOverrides,
    });

    const keywords = KeywordFactory.createMany(keywordCount, {
      batchId: batch.id,
    });

    return { batch, keywords };
  }
}