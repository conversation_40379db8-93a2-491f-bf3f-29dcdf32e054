import { faker } from '@faker-js/faker';

export interface TestSearchResult {
  id: string;
  keywordId: string;
  totalAds: number;
  totalLinks: number;
  htmlContent: string;
  metadata: Record<string, any>;
  adsLinks: any[];
  organicLinks: any[];
  otherLinks: any[];
  scrapedAt: Date;
}

export interface TestLinkDetail {
  title: string;
  url: string;
  snippet?: string;
  position?: number;
}

export class SearchResultFactory {
  static create(overrides: Partial<TestSearchResult> = {}): TestSearchResult {
    return {
      id: faker.string.uuid(),
      keywordId: faker.string.uuid(),
      totalAds: faker.number.int({ min: 0, max: 10 }),
      totalLinks: faker.number.int({ min: 5, max: 50 }),
      htmlContent: this.createMockHTML(),
      metadata: {
        keyword: faker.lorem.words(2),
        searchEngine: faker.helpers.arrayElement(['google', 'bing']),
        results: this.createMockResults(),
      },
      adsLinks: this.createMockAdsLinks(),
      organicLinks: this.createMockOrganicLinks(),
      otherLinks: this.createMockOtherLinks(),
      scrapedAt: new Date(),
      ...overrides,
    };
  }

  static createMockHTML(): string {
    return `
      <html>
        <head><title>Search Results</title></head>
        <body>
          <div class="ads-ad">Ad 1</div>
          <div class="ads-ad">Ad 2</div>
          <div class="g">
            <h3>Result 1</h3>
            <a href="https://example.com">Link 1</a>
          </div>
          <div class="g">
            <h3>Result 2</h3>
            <a href="https://example2.com">Link 2</a>
          </div>
        </body>
      </html>
    `;
  }

  static createMockResults(): TestLinkDetail[] {
    return Array.from({ length: faker.number.int({ min: 5, max: 10 }) }, (_, index) => ({
      title: faker.lorem.sentence(),
      url: faker.internet.url(),
      snippet: faker.lorem.paragraph(),
      position: index + 1,
    }));
  }

  static createMockAdsLinks(): TestLinkDetail[] {
    return Array.from({ length: faker.number.int({ min: 0, max: 5 }) }, () => ({
      title: faker.company.name() + ' - Ad',
      url: faker.internet.url(),
      snippet: faker.lorem.sentence(),
    }));
  }

  static createMockOrganicLinks(): TestLinkDetail[] {
    return Array.from({ length: faker.number.int({ min: 5, max: 15 }) }, () => ({
      title: faker.lorem.sentence(),
      url: faker.internet.url(),
      snippet: faker.lorem.paragraph(),
    }));
  }

  static createMockOtherLinks(): TestLinkDetail[] {
    return Array.from({ length: faker.number.int({ min: 0, max: 10 }) }, () => ({
      title: faker.lorem.words(3),
      url: faker.internet.url(),
    }));
  }
}