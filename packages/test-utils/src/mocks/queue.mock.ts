export interface MockJob {
  id: string;
  data: any;
  progress: (progress: number) => Promise<void>;
}

export interface MockQueue {
  add: jest.MockedFunction<(name: string, data: any, opts?: any) => Promise<MockJob>>;
  process: jest.MockedFunction<(processor: (job: <PERSON>ck<PERSON>ob) => Promise<void>) => void>;
  on: jest.MockedFunction<(event: string, callback: (...args: any[]) => void) => void>;
  close: jest.MockedFunction<() => Promise<void>>;
}

export class MockQueueProvider {
  private jobs: MockJob[] = [];
  private processors: Map<string, (job: MockJob) => Promise<void>> = new Map();

  createQueue(name: string): MockQueue {
    const queue: MockQueue = {
      add: jest.fn().mockImplementation((jobName: string, data: any, opts?: any) => {
        const job: MockJob = {
          id: `job-${Date.now()}-${Math.random()}`,
          data,
          progress: jest.fn().mockResolvedValue(undefined),
        };

        this.jobs.push(job);
        return Promise.resolve(job);
      }),

      process: jest.fn().mockImplementation((processor: (job: MockJob) => Promise<void>) => {
        this.processors.set(name, processor);
      }),

      on: jest.fn().mockImplementation(() => {}),

      close: jest.fn().mockResolvedValue(undefined),
    };

    return queue;
  }

  async processAllJobs(): Promise<void> {
    const allJobs = [...this.jobs];
    this.jobs = [];

    for (const job of allJobs) {
      for (const [, processor] of this.processors) {
        await processor(job);
      }
    }
  }

  getQueuedJobs(): MockJob[] {
    return [...this.jobs];
  }

  clearJobs(): void {
    this.jobs = [];
  }

  reset(): void {
    this.jobs = [];
    this.processors.clear();
  }
}

// Global mock instance
let mockQueueProvider: MockQueueProvider | null = null;

export function getMockQueueProvider(): MockQueueProvider {
  if (!mockQueueProvider) {
    mockQueueProvider = new MockQueueProvider();
  }
  return mockQueueProvider;
}

export function resetMockQueue(): void {
  if (mockQueueProvider) {
    mockQueueProvider.reset();
  }
}