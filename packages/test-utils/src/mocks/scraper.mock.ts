import { SearchResultFactory, TestLinkDetail } from '../factories/search-result.factory';

export interface MockScrapingResult {
  query: string;
  results: TestLinkDetail[];
  adsCount: number;
  totalLinksCount: number;
  htmlCache: string;
  adsLinks: TestLinkDetail[];
  organicLinks: TestLinkDetail[];
  otherLinks: TestLinkDetail[];
  timestamp: Date;
}

export interface MockSearchOptions {
  query: string;
  page?: number;
  resultsPerPage?: number;
}

export class MockScraper {
  private shouldFail: boolean = false;
  private failureError: Error = new Error('Mock scraping failed');
  private delay: number = 0;
  private customResults: Map<string, MockScrapingResult> = new Map();

  async initialize(): Promise<void> {
    // Mock initialization
  }

  async close(): Promise<void> {
    // Mock cleanup
  }

  async search(options: MockSearchOptions): Promise<MockScrapingResult> {
    if (this.delay > 0) {
      await new Promise(resolve => setTimeout(resolve, this.delay));
    }

    if (this.shouldFail) {
      throw this.failureError;
    }

    // Return custom result if set, otherwise generate mock result
    if (this.customResults.has(options.query)) {
      return this.customResults.get(options.query)!;
    }

    return this.generateMockResult(options.query);
  }

  // Test helpers
  setFailure(shouldFail: boolean, error?: Error): void {
    this.shouldFail = shouldFail;
    if (error) {
      this.failureError = error;
    }
  }

  setDelay(ms: number): void {
    this.delay = ms;
  }

  setCustomResult(query: string, result: Partial<MockScrapingResult>): void {
    const fullResult = {
      ...this.generateMockResult(query),
      ...result,
    };
    this.customResults.set(query, fullResult);
  }

  clearCustomResults(): void {
    this.customResults.clear();
  }

  reset(): void {
    this.shouldFail = false;
    this.failureError = new Error('Mock scraping failed');
    this.delay = 0;
    this.customResults.clear();
  }

  private generateMockResult(query: string): MockScrapingResult {
    const adsLinks = SearchResultFactory.createMockAdsLinks();
    const organicLinks = SearchResultFactory.createMockOrganicLinks();
    const otherLinks = SearchResultFactory.createMockOtherLinks();
    
    return {
      query,
      results: SearchResultFactory.createMockResults(),
      adsCount: adsLinks.length,
      totalLinksCount: adsLinks.length + organicLinks.length + otherLinks.length,
      htmlCache: SearchResultFactory.createMockHTML(),
      adsLinks,
      organicLinks,
      otherLinks,
      timestamp: new Date(),
    };
  }
}

export class MockGoogleScraper extends MockScraper {
  constructor() {
    super();
  }
}

export class MockBingScraper extends MockScraper {
  constructor() {
    super();
  }
}