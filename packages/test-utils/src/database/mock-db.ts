// Mock database for testing
export interface MockDbQuery {
  select: jest.MockedFunction<any>;
  insert: jest.MockedFunction<any>;
  update: jest.MockedFunction<any>;
  delete: jest.MockedFunction<any>;
  from: jest.MockedFunction<any>;
  where: jest.MockedFunction<any>;
  leftJoin: jest.MockedFunction<any>;
  orderBy: jest.MockedFunction<any>;
  limit: jest.MockedFunction<any>;
  offset: jest.MockedFunction<any>;
  values: jest.MockedFunction<any>;
  returning: jest.MockedFunction<any>;
  set: jest.MockedFunction<any>;
  transaction: jest.MockedFunction<any>;
  groupBy: jest.MockedFunction<any>;
}

export function createMockDb(): MockDbQuery {
  const mockDb = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    leftJoin: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    offset: jest.fn().mockReturnThis(),
    values: jest.fn().mockReturnThis(),
    returning: jest.fn(),
    set: jest.fn().mockReturnThis(),
    transaction: jest.fn(),
    groupBy: jest.fn().mockReturnThis(),
  } as MockDbQuery;

  return mockDb;
}

export function resetMockDb(mockDb: MockDbQuery): void {
  Object.values(mockDb).forEach(mockFn => {
    if (jest.isMockFunction(mockFn)) {
      mockFn.mockClear();
      if (mockFn !== mockDb.returning && mockFn !== mockDb.transaction) {
        mockFn.mockReturnThis();
      }
    }
  });
}