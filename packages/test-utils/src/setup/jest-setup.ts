import { setupTestSuite as dbSetupTestSuite, teardownTestSuite as dbTeardownTestSuite, cleanupTestDatabase } from '../database/test-db';
import { resetMockQueue } from '../mocks/queue.mock';

// Global test setup
export async function globalSetup(): Promise<void> {
  // Setup test database connection
  await dbSetupTestSuite();
  
  console.log('Global test setup completed');
}

// Global test teardown
export async function globalTeardown(): Promise<void> {
  // Close database connections
  await dbTeardownTestSuite();
  
  console.log('Global test teardown completed');
}

// Setup for each test suite
export async function setupTestSuite(): Promise<void> {
  // Clean database before each test suite
  await cleanupTestDatabase();
  
  // Reset all mocks
  resetMockQueue();
}

// Teardown for each test suite
export async function teardownTestSuite(): Promise<void> {
  // Clean database after each test suite
  await cleanupTestDatabase();
  
  // Reset all mocks
  resetMockQueue();
}

// Setup for each individual test
export function setupTest(): void {
  // Reset mocks before each test
  jest.clearAllMocks();
}

// Common Jest matchers and utilities
export const testUtils = {
  // Async test helper with timeout
  withTimeout: <T>(promise: Promise<T>, timeoutMs: number = 5000): Promise<T> => {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error(`Test timeout after ${timeoutMs}ms`)), timeoutMs)
      )
    ]);
  },

  // Wait for condition helper
  waitFor: async (
    condition: () => boolean | Promise<boolean>, 
    options: { timeout?: number; interval?: number } = {}
  ): Promise<void> => {
    const { timeout = 5000, interval = 100 } = options;
    const start = Date.now();
    
    while (Date.now() - start < timeout) {
      const result = await condition();
      if (result) {
        return;
      }
      await new Promise(resolve => setTimeout(resolve, interval));
    }
    
    throw new Error(`Condition not met within ${timeout}ms`);
  },

  // Delay helper
  delay: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
};