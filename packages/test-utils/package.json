{"name": "@search-keywords-scraper/test-utils", "version": "0.1.0", "description": "Shared testing utilities and factories", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "type-check": "tsc --noEmit"}, "dependencies": {"@search-keywords-scraper/database": "workspace:*", "@search-keywords-scraper/types": "workspace:*", "@faker-js/faker": "^9.4.0", "drizzle-orm": "^0.36.4", "postgres": "^3.4.4"}, "devDependencies": {"@types/jest": "^30.0.0", "@types/node": "^22.10.7", "jest": "^30.0.0", "typescript": "^5.7.3"}, "license": "MIT"}