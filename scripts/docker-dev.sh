#!/bin/bash

# Docker Development Script
# Convenience script for common Docker development operations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Setup environment file
setup_env() {
    if [ ! -f .env ]; then
        print_status "Creating .env from .env.docker template..."
        cp .env.docker .env
        print_warning "Please edit .env with your actual Supabase credentials before continuing."
        print_warning "Run: nano .env"
        exit 1
    fi
}

# Start development environment
start() {
    print_status "Starting Docker development environment..."
    
    check_docker
    setup_env
    
    # Build and start services
    docker-compose up -d --build
    
    print_success "Services started successfully!"
    print_status "Web App: http://localhost:3000"
    print_status "API: http://localhost:3001"
    print_status "Redis Commander: http://localhost:8081 (with --tools)"
    print_status "pgAdmin: http://localhost:8080 (with --tools)"
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Run migrations
    print_status "Running database migrations..."
    docker-compose exec api pnpm db:migrate || print_warning "Migration failed - services might still be starting"
    
    print_success "Development environment ready!"
}

# Stop development environment
stop() {
    print_status "Stopping Docker development environment..."
    docker-compose down
    print_success "Services stopped successfully!"
}

# Restart services
restart() {
    print_status "Restarting Docker development environment..."
    docker-compose down
    docker-compose up -d
    print_success "Services restarted successfully!"
}

# Show logs
logs() {
    local service=${1:-}
    if [ -z "$service" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f "$service"
    fi
}

# Run tests
test() {
    print_status "Running tests in Docker containers..."
    docker-compose exec api pnpm test
    docker-compose exec worker pnpm test
}

# Clean up everything
clean() {
    print_warning "This will remove all containers, volumes, and images. Continue? (y/N)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        print_status "Cleaning up Docker environment..."
        docker-compose down -v --rmi local
        docker system prune -f
        print_success "Cleanup completed!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Show help
help() {
    echo "Docker Development Script"
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  start          Start the development environment"
    echo "  stop           Stop all services"
    echo "  restart        Restart all services"  
    echo "  logs [service] Show logs (all services or specific service)"
    echo "  test           Run tests in containers"
    echo "  clean          Clean up all Docker resources (destructive)"
    echo "  help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs api"
    echo "  $0 test"
    echo ""
}

# Main script logic
case "${1:-}" in
    start)
        if [[ "${2:-}" == "--tools" ]]; then
            export COMPOSE_PROFILES=tools
        fi
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    logs)
        logs "${2:-}"
        ;;
    test)
        test
        ;;
    clean)
        clean
        ;;
    help|--help|-h)
        help
        ;;
    *)
        print_error "Unknown command: ${1:-}"
        echo ""
        help
        exit 1
        ;;
esac