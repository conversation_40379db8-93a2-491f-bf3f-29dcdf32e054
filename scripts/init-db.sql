-- Initialize development database
-- This script is run when the PostgreSQL container starts up

-- Create additional databases if needed
-- CREATE DATABASE scraper_test;

-- Create extensions that might be needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Set timezone
SET timezone = 'UTC';

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE scraper_dev TO postgres;

-- Create basic indexes that are commonly needed
-- (The actual schema will be created by Drizzle migrations)