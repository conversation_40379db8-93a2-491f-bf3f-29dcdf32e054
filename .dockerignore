# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build outputs
.next/
dist/
build/
out/
coverage/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Documentation
README.md
CHANGELOG.md
docs/
*.md

# Test files
**/*.test.*
**/*.spec.*
**/__tests__/
**/__mocks__/
coverage/
.nyc_output/

# Development files
docker-compose*.yml
Dockerfile.dev
.dockerignore

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Temporary folders
.tmp/
temp/

# Cache directories
.cache/
.parcel-cache/

# Editor directories and files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# TypeScript
*.tsbuildinfo

# Playwright
/test-results/
/playwright-report/
/playwright/.cache/